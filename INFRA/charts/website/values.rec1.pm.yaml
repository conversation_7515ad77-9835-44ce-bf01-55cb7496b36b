.vault: &vault
    vault:
        enabled: true
        secrets:
            - name: app
              path: mosaic/website/rec1/app
            - name: infra
              path: mosaic/website/rec1/infra
            - name: pub-coreadsmanager-abvariant
              path: pub/coreadsmanager/rec1/abvariantpub/data
    annotations:
        vault.hashicorp.com/role: "rec1-mosaic-website"

.image: &image
    image:
        repository: 749738944708.dkr.ecr.eu-west-1.amazonaws.com/php-5.2-mosaic-website-php
        pullPolicy: Always

.env: &env
    DD_SERVICE: mosaic-website
    DD_TRACE_SAMPLING_RULES: '[ { "service": "cnt-api", "sample_rate": 0.9 } ]'
    DD_TRACE_SAMPLE_RATE: 0.9
    DD_TRACE_HTTP_CLIENT_SPLIT_BY_DOMAIN: true
    DD_LOGS_INJECTION: true
    SECRETS_DIR: /vault/secrets/
    INFRA_ENV: $(Environment)

accessteam:
    enabled: true
    teams:
        - TeamFem
        - TeamPub

webadmin:
    Team: fem
    <<: *vault
    env:
        <<: *env
        DEFAULT_HTTP_HOST: $(Environment).femmeactuelle.fr
        APP_ID: fac
    ScheduleOff: 1-5_6-19
    iam_role: arn:aws:iam::749738944708:role/rec1-mosaic-website
    autoscaling:
        enabled: false
    component:
        nginx:
            image:
                repository: 749738944708.dkr.ecr.eu-west-1.amazonaws.com/nginx-5.2-mosaic-website-nginx
                pullPolicy: Always
            resources:
                requests:
                    cpu: 150m
                    memory: 70Mi
                limits:
                    memory: 100Mi
        php:
            <<: *image
            resources:
                requests:
                    cpu: 300m
                    memory: 256Mi
                limits:
                    memory: 1500Mi
    ingress:
        enabled: true
        annotationsalb:
            alb.ingress.kubernetes.io/healthcheck-path: '/ping'
        paths: ["/"]
        hosts:
            - $(Environment)-shopping.femmeactuelle.fr
            - $(Environment)-shopping.voici.fr
            - $(Environment)-shopping.geo.fr
            - $(Environment)-shopping.programme-tv.net
            - $(Environment)-shopping.cuisineactuelle.fr
            - $(Environment)-shopping.caminteresse.fr

            - $(Environment)-jeux.femmeactuelle.fr
            - $(Environment)-jeux.cuisineactuelle.fr
            - $(Environment)-jeux.voici.fr
            - $(Environment)-jeux.geo.fr
            - $(Environment)-jeux.capital.fr
            - $(Environment)-jeux.caminteresse.fr
            - $(Environment)-jeux.programme-tv.net
            - $(Environment)-jeux.programme.tv
            - $(Environment)-jeux.cesoirtv.com

            - $(Environment)-photo.cuisineactuelle.fr
            - $(Environment)-photo.cesoirtv.com
            - $(Environment)-photo.programme.tv
            - $(Environment)-photo.voici.fr
            - $(Environment)-photo.harpersbazaar.fr
            - $(Environment)-photo.gala.fr
            - $(Environment)-photo.capital.fr
            - $(Environment)-photo.geo.fr
            - $(Environment)-photo.caminteresse.fr
            - $(Environment)-photo.hbrfrance.fr
            - $(Environment)-photo.seeandso.com
            - $(Environment)-photo.femmeactuelle.fr
            - $(Environment)-photo.programme-tv.net
            - $(Environment)-photo.gentside.com
            - $(Environment)-photo.ohmymag.com
            - $(Environment)-photo.seeandso.com
            - $(Environment)-de-photo.seeandso.com

            - $(Environment)-actu.femmeactuelle.fr
            - $(Environment)-actu.cuisineactuelle.fr
            - $(Environment)-actu.gala.fr
            - $(Environment)-actu.voici.fr
            - $(Environment)-actu.harpersbazaar.fr
            - $(Environment)-actu.programme-tv.net
            - $(Environment)-actu.capital.fr
            - $(Environment)-actu.cesoirtv.com
            - $(Environment)-actu.geo.fr
            - $(Environment)-actu.programme.tv
            - $(Environment)-actu.caminteresse.fr
            - $(Environment)-actu.hbrfrance.fr
            - $(Environment)-actu.ohmymag.com

            - $(Environment).voici.fr
            - $(Environment).femmeactuelle.fr
            - $(Environment).cuisineactuelle.fr

            - $(Environment).serengo.net
            - $(Environment).prima.fr
            - $(Environment)-video.femmeactuelle.fr
            - $(Environment)-test-produit.femmeactuelle.fr
        tls: []
    nodeSelector: {}
    tolerations: []

scaledjobs:
    Team: fem
    jobs:
        - name: queue-cac
          <<: *vault
          <<: *env
          <<: *image
          env:
              <<: *env
              DEFAULT_HTTP_HOST: $(Environment).cuisineactuelle.fr
              APP_ID: cac
          command: [ "php" ]
          args: ["/var/www/default/bin/console", "app:cache:regenerate", "--chunk-size=20"]
          autoscaling:
              enabled: true
              type: "redis"
              min: 0
              max: 10
              redis:
                  address: "staging-fem-global-redis.jwxibn.ng.0001.euw1.cache.amazonaws.com:6379"
                  listName: "rec1_CAC:ApiContent:list"
                  listLength: 10
                  activationListLength: 0

        - name: queue-fac
          <<: *vault
          <<: *env
          <<: *image
          env:
              <<: *env
              DEFAULT_HTTP_HOST: $(Environment).femmeactuelle.fr
              APP_ID: fac
          command: [ "php" ]
          args: ["/var/www/default/bin/console", "app:cache:regenerate", "--chunk-size=20"]
          autoscaling:
              enabled: true
              type: "redis"
              min: 0
              max: 10
              redis:
                  address: "staging-fem-global-redis.jwxibn.ng.0001.euw1.cache.amazonaws.com:6379"
                  listName: "rec1_FAC:ApiContent:list"
                  listLength: 10
                  activationListLength: 0

affinity: {}
