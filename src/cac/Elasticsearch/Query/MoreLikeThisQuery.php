<?php

declare(strict_types=1);

namespace App\cac\Elasticsearch\Query;

use App\cac\Elasticsearch\Helper\SearchHelper;
use App\cac\Model\Article;
use PMD\Bundle\FemElasticsearchBundle\Query\QueryBuilder;

class MoreLikeThisQuery
{
    final public const int LIMIT = 100;

    public static function get(Article $article, int $limit = self::LIMIT, int $offset = 0, array $source = []): QueryBuilder
    {
        $limit = min(abs($limit), self::LIMIT);
        $baseQuery = (new QueryBuilder())
            ->setTtl(3600)
            ->match('title', self::extractSearch($article))
            ->neq('id', $article->getId())
            ->size($limit)
            ->from($offset);

        $query = [
            'query' => $baseQuery->toArray()['query'],
            'size' => $limit,
        ];

        if (!empty($source)) {
            $query['_source'] = $source;
        }

        return $baseQuery->setQuery($query);
    }

    private static function extractSearch(Article $article): string
    {
        $search = $article->getTitle();
        if ($article->hasTags()) {
            $search = implode(' ', $article->getTagsTitles());
        }

        return SearchHelper::cleanSearchText($search);
    }
}
