<?php

declare(strict_types=1);

namespace App\Slideshow\Controller;

use App\common\Config\AppConfig;
use App\common\Controller\ControllerHelper;
use App\common\Repository\SlideshowRepository;
use PMD\ApiContent\Model\Slide;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Attribute\Route;

final readonly class SlideshowController
{
    public function __construct(
        private ControllerHelper $controllerHelper,
        private SlideshowRepository $slideshowRepository,
        private array $appConfig
    ) {
    }

    #[Route(
        path: '/{slideshowPath}',
        name: 'diaporama_slideshow',
        requirements: ['slideshowPath' => '[\/a-zA-Z0-9-_.]+-[0-9]+'],
        methods: ['GET'],
    )]
    #[Route(
        path: '/{slideshowPath}/{suffix}',
        name: 'diaporama_slideshow_old',
        requirements: ['slideshowPath' => '[\/a-zA-Z0-9-_.]+-[0-9]+'],
        methods: ['GET'],
    )]
    #[Route(
        path: '/{slideshowPath}#{photoTitle}-{photoId}',
        name: 'diaporama_photo',
        requirements: ['slideshowPath' => '[a-zA-Z0-9-_.]+-[0-9]+', 'photoId' => '[a-zA-Z0-9-_.]+', 'photoTitle' => '[a-zA-Z0-9-_.]*'],
        methods: ['GET'],
    )]
    public function show(Request $request, string $slideshowPath, $suffix = null): Response
    {
        $config = new AppConfig($this->appConfig);
        if ($suffix) {
            return new RedirectResponse('/'.$slideshowPath, Response::HTTP_MOVED_PERMANENTLY);
        }
        $brand = $config->getBrandKey();
        $content = $this->slideshowRepository->findOneByPath($slideshowPath, $brand);

        if (null === $content) {
            throw new NotFoundHttpException('No slideshow found for this path');
        }

        return $this->controllerHelper->render(
            '@Front/src/views/pages/slideshow/slideshow.html.twig',
            [
                'content' => $content,
                // FMN-1050 removes slides that have non Photo object (quick and dirty fix)
                'slideshowsDocuments' => $content->getSlideshowsDocuments()->filter(fn (Slide $slide) => $slide->getPhoto()),
            ]
        );
    }
}
