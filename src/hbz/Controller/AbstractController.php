<?php

declare(strict_types=1);

namespace App\hbz\Controller;

use App\common\Controller\ControllerHelper;
use PMD\ApiContent\Client\AbstractClient as CntClient;

abstract readonly class AbstractController
{
    public const TTL_LIVE = 30;
    public const TTL_SHORT = 60;
    public const TTL_MEDIUM = 300;
    public const TTL_MEDIUMEXTENDED = 600;
    public const TTL_LONG = 3600;
    public const TTL_LONGEXTENDED = 21600;
    public const TTL_STATIC = 86400;
    public const GRACE_DURATION_MEDIUM = '2419200'; // 4w

    public const array PATH_MAPPING = [
        'fashion' => '/mode',
        'beauty' => '/beaute',
        'culture' => '/culture',
    ];

    public function __construct(
        protected readonly CntClient $cntClient,
        protected readonly ControllerHelper $controllerHelper
    ) {
    }
}
