<?php

declare(strict_types=1);

namespace App\Shopping\Config\Brands;

class ConfigCac
{
    public static function get()
    {
        return [
            'domain' => 'cuisineactuelle.fr',
            'brandKey' => 'SHGFAC', // TODO Changer en SHGCAC quand dispo
            'brandKeyResizer' => 'CAC',
            'brandName' => 'Cuisine Actuelle',
            'title' => 'Cuisine Actuelle Shopping',
            'headers' => [ // TODO Changer pour SHGCAC quand dispo
                'nav' => [
                    ['href' => '/mode', 'title' => 'Mode'],
                    ['href' => '/consommation', 'title' => 'Consommation'],
                    ['href' => '/voyages', 'title' => 'Voyages'],
                    ['href' => '/services', 'title' => 'Services'],
                    ['href' => '/sante', 'title' => 'Santé'],
                    ['href' => '/beaute', 'title' => 'Beauté'],
                ],
            ],
            'google_analytics' => [
                'account' => 'UA-********-6',
            ],
            'footers' => [
                'nav' => [
                    ['href' => 'http://www.prismamedia.com/cgu-groupe-prisma/', 'title' => 'CONDITIONS GÉNÉRALES D\'UTILISATION'],
                    ['href' => 'https://www.prismamediasolutions.com/les-marques/cuisine-actuelle', 'title' => 'PUBLICITÉ'],
                    ['href' => 'https://www.prismamedia.com/mentions-legales-cac/', 'title' => 'MENTIONS LÉGALES'],
                    ['href' => 'https://www.prismashop.fr/tous-les-magazines/cuisine/cuisine-actuelle.html?code=SIECACFOOT&utm_source=cac-fr&utm_medium=sites-editos&utm_campaign=footer-abo&utm_content=papier', 'title' => 'ABONNEMENT MAGAZINE'],
                    ['href' => 'http://www.prismamedia.com/charte-pour-la-protection-des-donnees/', 'title' => 'CHARTE POUR LA PROTECTION DES DONNÉES PERSONNELLES'],
                ],
            ],
        ];
    }
}
