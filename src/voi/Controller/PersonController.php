<?php

declare(strict_types=1);

namespace App\voi\Controller;

use App\common\CNT\Query\PeopleQuery as CNTPeopleQuery;
use App\common\Controller\ControllerHelper;
use App\common\Model\Person;
use PMD\ApiContent\Client\AbstractClient as CntClient;
use PMD\ApiContent\Request\Query;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

readonly class PersonController
{
    public function __construct(
        private ControllerHelper $controllerHelper,
        private CntClient $cntClient
    ) {
    }

    public function show(Person $person, Request $request): Response
    {
        if ('actu' === $request->get('bio')) {
            return $this->controllerHelper->forward(PersonContentController::class.'::show', [
                'request' => $request,
                'person' => $person,
            ]);
        }
        $articleLength = 19;
        $currentPage = $request->query->getInt('page', 1);
        $articleOffset = $articleLength * ($currentPage - 1);
        $query = new Query('peopleQuery_getOne');

        $query->addSubQuery('person',
            CNTPeopleQuery::findOne('$person_offset', '$person_limit', '$person_relationshipsLimit'),
            [
                'brand' => $this->controllerHelper->getBrand(),
                'url' => $request->getPathInfo(),
                'offset' => ['value' => $articleOffset, 'isSubQueryParameter' => true],
                'limit' => ['value' => $articleLength, 'isSubQueryParameter' => true],
                'relationshipsLimit' => ['value' => 19, 'isSubQueryParameter' => true],
            ],
        );

        $result = $this->cntClient->execute($query);

        if (!isset($result['person'])) {
            throw new NotFoundHttpException(sprintf("People %s doesn't exist", $request->getPathInfo()));
        }

        /** @var Person $person */
        $person = $result['person'];

        if ($request->getPathInfo() !== $person->getPath()) {
            return new RedirectResponse($person->getPath(), Response::HTTP_MOVED_PERMANENTLY);
        }

        $latestArticleMediaVideo = null;
        $latestVideoArticles = null;
        $latestSlideshowArticles = null;
        foreach ($person->getContents() as $content) {
            /** @var \App\voi\Model\Article $content */
            if ($content->getVideo()?->isDailymotionVideo()) {
                if (!$latestArticleMediaVideo) {
                    $latestArticleMediaVideo = $content->getVideo();
                }
                $latestVideoArticles[] = $content;
            }
            if ($content->hasPhotos()) {
                $latestSlideshowArticles[] = $content;
            }
        }

        return $this->controllerHelper->render('@Front/src/views/pages/person/home/<USER>',
            [
                'person' => $person,
                'content' => $person,
                'latestArticles' => $person->getContents(),
                'latestArticleMediaVideo' => $latestArticleMediaVideo,
                'latestSlideshowArticles' => $latestSlideshowArticles,
                'latestVideoArticles' => $latestVideoArticles,
            ],
        );
    }
}
