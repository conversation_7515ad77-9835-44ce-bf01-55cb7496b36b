<?php

declare(strict_types=1);

namespace App\common\Config;

final readonly class AppConfig
{
    public function __construct(private array $data = [])
    {
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function get($key): mixed
    {
        return $this->data[$key] ?? null;
    }

    public function getBrandKey(): ?string
    {
        return $this->data['brand']['brandKey'] ?? $this->data['brandKey'] ?? null;
    }
}
