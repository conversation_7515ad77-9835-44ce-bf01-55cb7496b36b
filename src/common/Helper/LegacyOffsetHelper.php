<?php

declare(strict_types=1);

namespace App\common\Helper;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

readonly class LegacyOffsetHelper
{
    public function __construct(private UrlGeneratorInterface $urlGenerator)
    {
    }

    public function redirectOffsetPagination(Request $request, int $limit, ?int $maxPages = null): ?RedirectResponse
    {
        $offset = $request->query->has('offset') ? $request->query->getInt('offset') : null;

        if (null === $offset) {
            return null;
        }

        // if $offset is negatif or  is not a multiple of $limit, throw an exception
        if (0 > $offset || 0 !== $offset % $limit) {
            throw new NotFoundHttpException('Invalid offset or limit');
        }

        $page = (int) floor($offset / $limit) + 1;
        // if $page (aka current page) exceeds $maxPages, redirects to main page
        if ($maxPages && $page > $maxPages) {
            throw new NotFoundHttpException('Invalid offset');
        }

        $queryParams = array_filter(
            $request->attributes->get('_route_params') + $request->query->all(),
            fn ($key) => !str_starts_with($key, '_'),
            \ARRAY_FILTER_USE_KEY
        );

        unset($queryParams['offset']);
        $queryParams['page'] = $page;

        $routeName = $request->attributes->get('_route');
        $url = $this->urlGenerator->generate($routeName, $queryParams);

        return new RedirectResponse($url, Response::HTTP_MOVED_PERMANENTLY);
    }
}
