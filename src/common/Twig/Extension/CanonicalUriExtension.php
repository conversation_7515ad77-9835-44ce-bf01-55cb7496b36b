<?php

declare(strict_types=1);

namespace App\common\Twig\Extension;

use Symfony\Component\HttpFoundation\RequestStack;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class CanonicalUriExtension extends AbstractExtension
{
    private const string PARAM_OFFSET = 'offset';
    private const string PARAM_PAGE = 'page';

    public function __construct(private readonly RequestStack $requestStack)
    {
    }

    #[\Override]
    public function getFunctions(): array
    {
        return [new TwigFunction('render_canonical_uri', $this->render(...))];
    }

    public function render(bool $isArticleHasPageBreak = false): string
    {
        $request = $this->requestStack->getMainRequest();
        if ($isArticleHasPageBreak && null === $request->query->get(self::PARAM_PAGE)) {
            return $request->getPathInfo();
        }

        // priority to "page" for regular cases
        if (null !== $request->query->get(self::PARAM_PAGE)) {
            return $request->getPathInfo().'?'.self::PARAM_PAGE.'='.$request->query->get(self::PARAM_PAGE);
        }

        if (null !== $request->query->get(self::PARAM_OFFSET)) {
            return $request->getPathInfo().'?'.self::PARAM_OFFSET.'='.$request->query->get(self::PARAM_OFFSET);
        }

        return $request->getPathInfo();
    }
}
