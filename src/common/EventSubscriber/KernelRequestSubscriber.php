<?php

declare(strict_types=1);

namespace App\common\EventSubscriber;

use Psr\Log\LoggerAwareTrait;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\KernelEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class KernelRequestSubscriber implements EventSubscriberInterface
{
    use LoggerAwareTrait;

    #[\Override]
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [
                ['onKernelRequest', -255],
            ],
        ];
    }

    public function onKernelRequest(KernelEvent $event): void
    {
        $event->getRequest()->headers->remove('accept');
    }
}
