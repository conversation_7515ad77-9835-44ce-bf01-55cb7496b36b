<?php

declare(strict_types=1);

namespace App\TopActu\Controller;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Twig\Environment;

class RobotsTxtController
{
    #[Route('/robots.txt', priority: 1)]
    public function index(Environment $twigEnvironment): Response
    {
        return new Response(
            $twigEnvironment->render('pages/robots/robots.txt.html.twig'),
            Response::HTTP_OK,
            ['content-type' => 'text/plain']
        );
    }
}
