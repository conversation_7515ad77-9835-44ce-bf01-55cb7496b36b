<?php

declare(strict_types=1);

namespace App\TopActu\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class InImageExtension extends AbstractExtension
{
    public function getFunctions()
    {
        return [
            new TwigFunction('add_in_image', [$this, 'adInImage'], [
                'is_safe' => ['html'],
            ]),
        ];
    }

    public function adInImage(string $htmlBody): string
    {
        // Vérifier si le corps HTML est vide
        if ('' === trim($htmlBody)) {
            return $htmlBody; // Retourner tel quel si vide
        }

        // Trouver tous les <figure data-type="image">
        $pattern = '/<figure data-type="image"(.*?)>(.*?)<\/figure>/i';
        $count = 0;

        // Utiliser preg_replace_callback pour traiter chaque figure
        $htmlBody = preg_replace_callback($pattern, function ($matches) use (&$count) {
            ++$count;
            $class = (1 === $count % 2) ? ' class="ads-core-inimage"' : '';

            return '<figure data-type="image"'.$class.'>'.$matches[2].'</figure>';
        }, $htmlBody);

        return $htmlBody;
    }
}
