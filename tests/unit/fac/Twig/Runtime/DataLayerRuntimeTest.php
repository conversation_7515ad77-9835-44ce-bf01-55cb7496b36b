<?php

declare(strict_types=1);

namespace unit\fac\Twig\Runtime;

use App\fac\Manager\DataLayerManager;
use Cocur\Slugify\Slugify;
use PHPUnit\Framework\TestCase;
use PMD\ApiContent\Model\ArticleSlideshow;
use PMD\ApiContent\Model\Content;
use PMD\ApiContent\Resolver\ClassResolver;
use PMD\ApiContent\Transformer\Transformer;
use PMD\DraftToHtmlBundle\Output\DraftConverted;
use PMD\DraftToHtmlBundle\Renderer\HtmlRenderer;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

class DataLayerRuntimeTest extends TestCase
{
    /**
     * @param array<mixed> $object
     *
     * @return object|array<mixed>
     */
    public static function transformToCnt(mixed $object)
    {
        return (new Transformer(new ClassResolver(['App\common\Model'], [])))->transform($object);
    }

    public function testWebPage(): void
    {
        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'WebPage',
            'pageCategory' => 'autres',
            'pageSubCategory' => '',
            'hasVideos' => 'none',
            'keywords' => [],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime([], ['_route' => 'default'])->render(null));
    }

    public function testHomePage(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Category',
            'title' => 'FAC Home',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/',
                ],
            ],
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
            '_route' => 'pmd_fac.home_page',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'WebSite',
            'pageCategory' => '_homepage',
            'pageSubCategory' => '',
            'hasVideos' => 'none',
            'keywords' => [],
            'name' => 'FAC Home',
            'isPremium' => false,
            'alternativeHeadline' => 'FAC Home',
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    public function testTagListDataLayer(): void
    {
        $query = [];
        $attributes = [
            'slug' => 'psychologie',
            '_route' => 'pmd_fac_tag_list',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'CollectionPage',
            'pageCategory' => 'tag',
            'pageSubCategory' => 'autres',
            'hasVideos' => 'none',
            'keywords' => ['psychologie'],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render(null));
    }

    public function testFirstLevelCategoryDataLayer(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Category',
            'title' => 'Actu',
            'level' => 2,
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/actu',
                ],
            ],
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'CollectionPage',
            'pageCategory' => 'actu',
            'pageSubCategory' => 'hp',
            'hasVideos' => 'none',
            'keywords' => [],
            'name' => 'Actu',
            'isPremium' => false,
            'alternativeHeadline' => 'Actu',
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    public function testSecondLevelCategoryDataLayer(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Category',
            'title' => 'News',
            'level' => 3,
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/actu/news',
                ],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Actu',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'FAC',
                    'level' => 1,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'BONE',
                    'level' => 0,
                ],
            ],
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'CollectionPage',
            'pageCategory' => 'actu',
            'pageSubCategory' => 'news',
            'hasVideos' => 'none',
            'keywords' => [],
            'name' => 'News',
            'isPremium' => false,
            'alternativeHeadline' => 'News',
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    public function testCategoryTopArticles(): void
    {
        $query = [];
        $attributes = [
            'slug' => 'sante',
            '_route' => 'pmd_fac_category_top_articles',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'CollectionPage',
            'pageCategory' => 'sante',
            'pageSubCategory' => 'tops-sante',
            'hasVideos' => 'none',
            'keywords' => [],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render(null));
    }

    public function testRecipePageDataLayer(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Recipe',
            'id' => 'faac98a0-d9bb-4bf1-acab-597d9ccc152d',
            'title' => 'Recette de tomates farcies',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/cuisine/recettes/tomate/recette-de-tomates-farcies',
                ],
            ],
            'tags' => [
                ['__typename' => 'Tag', 'title' => 'tomates'],
                ['__typename' => 'Tag', 'title' => 'farce'],
                ['__typename' => 'Tag', 'title' => 'Poivre de Guinée'],
            ],
            'parsedIngredients' => [
                'areIngredientsConverted' => true,
                'splitIngredients' => [
                    [
                        'ingredients' => [
                            [
                                'ingredient' => 'tomates',
                                'referenceIngredient' => 'totmate',
                            ],
                        ],
                    ],
                    [
                        'ingredients' => [
                            [
                                'ingredient' => 'viande hachée',
                                'referenceIngredient' => 'viande',
                            ],
                            [
                                'ingredient' => 'Poivre de Guinée',
                                'referenceIngredient' => 'poivre',
                            ],
                        ],
                    ],
                ],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Cuisine',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'Recettes',
                    'level' => 3,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'Tomate',
                    'level' => 4,
                ],
            ],
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
            '_route' => 'pmd_fem_recette',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'Recipe',
            'pageCategory' => 'cuisine',
            'pageSubCategory' => 'recettes',
            'hasVideos' => 'none',
            'keywords' => [
                'tomates',
                'farce',
                'poivre de guinée',
                'totmate',
                'viande hachée',
                'viande',
                'poivre',
            ],
            'contentObjectId' => 'recipe:faac98a0-d9bb-4bf1-acab-597d9ccc152d',
            'name' => 'Recette de tomates farcies',
            'isPremium' => false,
            'alternativeHeadline' => 'Recette de tomates farcies',
            'publishedAt' => null,
            'qualifiers' => [],
            'source' => '',
            'slideshow' => ['count' => 0, 'url' => '#'],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    public function testAmpRecipePageDataLayer(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Recipe',
            'id' => '85b5b8a5-7f82-4082-bc19-126eda08b536',
            'title' => 'Recette de tomates farcies',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/cuisine/recettes/tomate/recette-de-tomates-farcies',
                ],
            ],
            'tags' => [
                ['__typename' => 'Tag', 'title' => 'tomates'],
                ['__typename' => 'Tag', 'title' => 'farce'],
                ['__typename' => 'Tag', 'title' => 'rouge'],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Cuisine',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'Recettes',
                    'level' => 3,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'Tomate',
                    'level' => 4,
                ],
            ],
        ]);

        $attributes = [
            'content' => $article,
            '_route' => 'pmd_fem_recette',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'Recipe',
            'pageCategory' => 'cuisine',
            'pageSubCategory' => 'recettes',
            'hasVideos' => 'none',
            'keywords' => [
                'tomates',
                'farce',
                'rouge',
            ],
            'contentObjectId' => 'recipe:85b5b8a5-7f82-4082-bc19-126eda08b536',
            'name' => 'Recette de tomates farcies',
            'isPremium' => false,
            'alternativeHeadline' => 'Recette de tomates farcies',
            'publishedAt' => null,
            'qualifiers' => [],
            'source' => '',
            'slideshow' => ['count' => 0, 'url' => '#'],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime([], $attributes)->render($article));
    }

    public function testArticlePageAndDiapoInArticle(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Article',
            'title' => 'Le chromebook, la nouvelle alternative à la tablette',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/vie-pratique/high-tech/le-chromebook-la-nouvelle-alternative-a-la-tablette-2078873',
                ],
            ],
            'tags' => [
                ['__typename' => 'Tag', 'title' => 'ordinateur'],
                ['__typename' => 'Tag', 'title' => 'conseils'],
                ['__typename' => 'Tag', 'title' => 'technologie'],
                ['__typename' => 'Tag', 'title' => 'tablette'],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Vie pratique',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'High-tech',
                    'level' => 3,
                ],
            ],
            'publishedAt' => (new \DateTime('05/12/2023'))->format('Y-m-d\TH:i:s.u\Z'),
            'qualifiers' => [
                [
                    '__typename' => 'qualifier',
                    'id' => 'a67a027f-203d-426c-8eb6-12cb8ca7fa71',
                    'title' => 'Diapo dans article',
                    'slug' => 'diapo-dans-article',
                ],
            ],
            'articleSlideshow' => new ArticleSlideshow([
                'id' => 'foo',
            ]),
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
            '_route' => 'pmd_fem_article',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'ArticleGallery',
            'pageCategory' => 'vie-pratique',
            'pageSubCategory' => 'high-tech',
            'keywords' => [
                'Ordinateur',
                'Conseils',
                'Technologie',
                'Tablette',
            ],
            'name' => 'Le chromebook, la nouvelle alternative à la tablette',
            'hasVideos' => 'none',
            'isPremium' => false,
            'alternativeHeadline' => 'Le chromebook, la nouvelle alternative à la tablette',
            'publishedAt' => '2023-05-12T00:00:00.000000Z',
            'qualifiers' => ['diapo-dans-article'],
            'source' => '',
            'slideshow' => ['count' => 0, 'url' => '#'],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    public function testAmpArticlePage(): void
    {
        $article = self::transformToCnt([
            '__typename' => 'Article',
            'id' => '3',
            'title' => 'Le chromebook, la nouvelle alternative à la tablette',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/vie-pratique/high-tech/le-chromebook-la-nouvelle-alternative-a-la-tablette-2078873',
                ],
            ],
            'tags' => [
                ['__typename' => 'Tag', 'title' => 'ordinateur'],
                ['__typename' => 'Tag', 'title' => 'conseils'],
                ['__typename' => 'Tag', 'title' => 'technologie'],
                ['__typename' => 'Tag', 'title' => 'tablette'],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Vie pratique',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'High-tech',
                    'level' => 3,
                ],
            ],
            'publishedAt' => '2023-01-01T14:00:00.000Z',
        ]);

        $attributes = [
            'content' => $article,
            '_route' => 'pmd_fem_article',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'NewsArticle',
            'pageCategory' => 'vie-pratique',
            'pageSubCategory' => 'high-tech',
            'keywords' => [
                'Ordinateur',
                'Conseils',
                'Technologie',
                'Tablette',
            ],
            'contentObjectId' => 'article:3',
            'name' => 'Le chromebook, la nouvelle alternative à la tablette',
            'hasVideos' => 'none',
            'isPremium' => false,
            'alternativeHeadline' => 'Le chromebook, la nouvelle alternative à la tablette',
            'publishedAt' => '2023-01-01T14:00:00.000Z',
            'qualifiers' => [],
            'source' => '',
            'slideshow' => [
                'count' => 0,
                'url' => '#',
            ],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime([], $attributes)->render($article));
    }

    /**
     * @dataProvider bioPageDataProvider
     */
    public function testBioPage($data, $expected): void
    {
        /** @var Content $article */
        $article = self::transformToCnt($data['entity']);
        $query = $data['query'];
        $attributes = [
            'content' => $article,
            '_route' => $data['route'],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    /**
     * @todo f2b fix it
     */
    public function testArticlePageWithLeaderVideo(): void
    {
        /** @var Content $article */
        $article = self::transformToCnt([
            '__typename' => 'Article',
            'title' => 'Le chromebook, la nouvelle alternative à la tablette',
            'urls' => [
                [
                    '__typename' => 'Url',
                    'path' => '/vie-pratique/high-tech/le-chromebook-la-nouvelle-alternative-a-la-tablette-2078873',
                ],
            ],
            'medias' => [
                ['__typename' => 'Media', 'type' => 'VIDEO', 'providerName' => 'Dailymotion'],
            ],
            'tags' => [
                ['__typename' => 'tag', 'title' => 'ordinateur'],
                ['__typename' => 'tag', 'title' => 'conseils'],
                ['__typename' => 'tag', 'title' => 'technologie'],
                ['__typename' => 'tag', 'title' => 'tablette'],
            ],
            'breadcrumb' => [
                [
                    '__typename' => 'Category',
                    'title' => 'Vie pratique',
                    'level' => 2,
                ],
                [
                    '__typename' => 'Category',
                    'title' => 'High-tech',
                    'level' => 3,
                ],
            ],
            'publishedAt' => '2023-01-01T14:00:00.000Z',
        ]);

        $query = [];
        $attributes = [
            'content' => $article,
            '_route' => 'article',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'NewsArticle',
            'pageCategory' => 'vie-pratique',
            'pageSubCategory' => 'high-tech',
            'hasVideos' => 'leader-Dailymotion',
            'keywords' => [
                'Ordinateur',
                'Conseils',
                'Technologie',
                'Tablette',
            ],
            'name' => 'Le chromebook, la nouvelle alternative à la tablette',
            'isPremium' => false,
            'alternativeHeadline' => 'Le chromebook, la nouvelle alternative à la tablette',
            'publishedAt' => '2023-01-01T14:00:00.000Z',
            'qualifiers' => [],
            'source' => '',
            'slideshow' => [
                'count' => 0,
                'url' => '#',
            ],
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render($article));
    }

    /**
     * @throws \Exception
     */
    public function testPeopleDiapoPage(): void
    {
        $query = [];
        $attributes = [
            'slug' => 'johnny-hallyday',
            '_route' => 'pmd_fac.people.diapo_show',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'imagegallery',
            'pageCategory' => 'diaporama-star',
            'pageSubCategory' => 'autres',
            'hasVideos' => 'none',
            'keywords' => ['johnny-hallyday'],
            'contentObjectId' => 'person:johnny-hallyday',
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render(null));
    }

    /**
     * @throws \Exception
     */
    public function testRecipeDiapoPage(): void
    {
        $query = [];
        $attributes = [
            'slug' => 'abricot',
            '_route' => 'pmd_fac.recipe.diapo_show',
        ];

        $expected = [
            'path' => '/',
            'queryString' => '',
            'env' => 'prod',
            'pageType' => 'imagegallery',
            'pageCategory' => 'diaporama-recipe',
            'pageSubCategory' => 'autres',
            'hasVideos' => 'none',
            'keywords' => ['abricot'],
            'contentObjectId' => 'tag:abricot',
        ];

        $this->assertEquals($expected, $this->getDataLayerRuntime($query, $attributes)->render(null));
    }

    public function bioPageDataProvider()
    {
        return [
            [
                [
                    'entity' => [
                        '__typename' => 'Category',
                        'title' => 'Toutes les personnalités',
                        'urls' => [
                            [
                                '__typename' => 'Url',
                                'path' => '/bio',
                            ],
                        ],
                    ],
                    'query' => [],
                    'route' => 'pmd_fac.bio_home',
                ],
                [
                    'path' => '/',
                    'queryString' => '',
                    'env' => 'prod',
                    'pageType' => 'CollectionPage',
                    'pageCategory' => 'bio',
                    'pageSubCategory' => 'hp',
                    'hasVideos' => 'none',
                    'keywords' => [],
                    'name' => 'Toutes les personnalités',
                    'isPremium' => false,
                    'alternativeHeadline' => 'Toutes les personnalités',
                ],
            ],
            [
                [
                    'entity' => [
                        '__typename' => 'Person',
                        'fullName' => 'Johnny Hallyday',
                        'urls' => [
                            [
                                '__typename' => 'Url',
                                'path' => '/bio/johnny-hallyday',
                            ],
                        ],
                    ],
                    'query' => [],
                    'route' => 'pmd_cnt_fac_person',
                ],
                [
                    'path' => '/',
                    'queryString' => '',
                    'env' => 'prod',
                    'pageType' => 'Person',
                    'pageCategory' => 'bio',
                    'pageSubCategory' => 'autres',
                    'hasVideos' => 'none',
                    'keywords' => [],
                    'name' => 'Johnny Hallyday',
                    'isPremium' => false,
                    'alternativeHeadline' => 'Johnny Hallyday',
                ],
            ],
        ];
    }

    private function getDataLayerRuntime(array $query = [], array $attributes = []): DataLayerManager
    {
        $request = new Request($query, [], $attributes);

        $requestStack = new RequestStack();
        $requestStack->push($request);

        $slugify = new Slugify();

        $draftConverted = new DraftConverted(isset($attributes['content']) ? '<p>'.$attributes['content']->getTitle().'</p>' : '');

        $htmlRenderer = $this->createMock(HtmlRenderer::class);
        $htmlRenderer
            ->expects($this->any())
            ->method('render')
            ->willReturn($draftConverted);

        return new DataLayerManager($requestStack, $slugify, $htmlRenderer, ['appId' => 'fac']);
    }
}
