<?php

declare(strict_types=1);

namespace unit\cac\Elasticsearch\Query;

use App\cac\Elasticsearch\Query\SearchQuery;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Form\FormConfigInterface;
use Symfony\Component\Form\FormInterface;

/**
 * @SuppressWarnings(PHPMD)
 */
final class SearchQueryTest extends TestCase
{
    /**
     * @dataProvider getDataForGetRecipes
     */
    public function testGetRecipes(array $expected, array $searches): void
    {
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->once())->method('getData')->willReturn($searches);

        $formConfig = $this->createMock(FormConfigInterface::class);
        $formConfig->expects($this->once())->method('getOption')->willReturn(true);
        $form->expects($this->once())->method('getConfig')->willReturn($formConfig);

        $this->assertEquals($expected, (new SearchQuery())->getRecipes($form)->toArray());
    }

    public function testGetQuery(): void
    {
        $searchQuery = new SearchQuery();

        $this->assertEquals(
            [
                'query' => [
                    'bool' => [
                        'must' => [
                            [
                                'multi_match' => [
                                    'query' => 'gâteau au chocolat',
                                    'fields' => ['title'],
                                ],
                            ],
                        ],
                    ],
                ],
                'sort' => [
                    [
                        'userreview.rank' => [
                            'order' => 'desc',
                            'missing' => '_last',
                        ],
                    ],
                ],
            ],
            $searchQuery->getQuery(
                'gâteau au chocolat',
                [
                    'name' => 'rating_rank',
                    'direction' => 'desc',
                ]
            )->toArray()
        );

        $this->expectException(\InvalidArgumentException::class);

        $searchQuery->getQuery(
            'gâteau au chocolat',
            [
                'name' => 'toto',
                'direction' => 'desc',
            ],
        );
    }

    public function getDataForGetRecipes(): array
    {
        $aggs = [
            //            'calorieLevel' => [
            //                'terms' => [
            //                    'field' => 'calorieLevel',
            //                    'size' => 999999,
            //                    'min_doc_count' => 1,
            //                ],
            //            ],
            //            'cookingMethod' => [
            //                'terms' => [
            //                    'field' => 'cookingMethod',
            //                    'size' => 999999,
            //                    'min_doc_count' => 1,
            //                ],
            //            ],
            'estimatedCost' => [
                'terms' => [
                    'field' => 'estimatedCost.label',
                    'size' => 999999,
                    'min_doc_count' => 1,
                ],
            ],
            'difficulty' => [
                'terms' => [
                    'field' => 'difficulty.label',
                    'size' => 999999,
                    'min_doc_count' => 1,
                ],
            ],
            //            'cuisinesCategory' => [
            //                'terms' => [
            //                    'field' => 'cuisinesCategory.title',
            //                    'size' => 999999,
            //                    'min_doc_count' => 1,
            //                ],
            //            ],
            //            'mealsCategory' => [
            //                'terms' => [
            //                    'field' => 'mealsCategory.title',
            //                    'size' => 999999,
            //                    'min_doc_count' => 1,
            //                ],
            //            ],
            //            'dietsCategory' => [
            //                'terms' => [
            //                    'field' => 'dietsCategory.title',
            //                    'size' => 999999,
            //                    'min_doc_count' => 1,
            //                ],
            //            ],
            'userreview' => [
                'range' => [
                    'field' => 'userreview.average',
                    'ranges' => [
                        [
                            'from' => 0,
                        ],
                        [
                            'from' => 0.2,
                        ],
                        [
                            'from' => 0.4,
                        ],
                        [
                            'from' => 0.6,
                        ],
                        [
                            'from' => 0.8,
                        ],
                        [
                            'from' => 1,
                        ],
                    ],
                ],
            ],
        ];

        $source = [
            'includes' => [
                'id',
                'title',
                'slug',
                'urls.path',
                'urls.publicId',
                'authors',
                'medias',
                'tags',
                'waitTime',
                'totalTime',
                'cookTime',
                'prepTime',
                'cookingMethod',
                'estimatedCost',
                'difficulty',
                'calorieLevel',
                'userreview',
                'rawIngredients.text',
                'instructions.text',
            ],
            'excludes' => [
                'medias.iframely',
            ],
        ];

        return [
            [
                'expected' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => 'gâteau au chocolat',
                                        'fields' => [
                                            'title',
                                        ],
                                    ],
                                ],
                            ],
                            'filter' => [
                                [
                                    'term' => [
                                        'medias.providerName' => 'PMD_BONE_Image',
                                    ],
                                ],
                            ],
                            'must_not' => [
                                [
                                    'term' => [
                                        'source' => 'user_recipe',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    '_source' => $source,
                    'aggs' => $aggs,
                ],
                'searches' => ['keyword' => 'gâteau au chocolat'],
            ],
            [
                'expected' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                [
                                    'multi_match' => [
                                        'query' => 'pizza',
                                        'fields' => [
                                            'title^2',
                                            'categoriesFlattens.title',
                                            'tags.title',
                                        ],
                                    ],
                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'calorieLevel' => [
                                //                                            'Elevé',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'cookingMethod' => [
                                //                                            'Cuisson au four',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'estimatedCost.label' => [
                                //                                            'Bon marché',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                [
                                    'terms' => [
                                        'difficulty.label' => [
                                            'Facile',
                                        ],
                                    ],
                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'cuisinesCategory.title' => [
                                //                                            'Recette italienne',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'mealsCategory.title' => [
                                //                                            'Apéro dinatoire',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                //                                [
                                //                                    'terms' => [
                                //                                        'dietsCategory.title' => [
                                //                                            'Recette végétarienne',
                                //                                        ],
                                //                                    ],
                                //                                ],
                                [
                                    'range' => [
                                        'userreview.average' => [
                                            'gte' => 0.6,
                                        ],
                                    ],
                                ],
                            ],
                            'filter' => [
                                [
                                    'term' => [
                                        'medias.providerName' => 'PMD_BONE_Image',
                                    ],
                                ],
                            ],
                            'must_not' => [
                                [
                                    'term' => [
                                        'source' => 'user_recipe',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    '_source' => $source,
                    'aggs' => $aggs,
                ],
                'searches' => [
                    'keyword' => 'pizza',
                    'calorieLevel' => [
                        'Elevé',
                    ],
                    //                    'cookingMethod' => [
                    //                        'Cuisson au four',
                    //                    ],
                    'difficulty' => [
                        'Facile',
                    ],
                    //                    'estimatedCost' => [
                    //                        'Bon marché',
                    //                    ],
                    //                    'cuisinesCategory' => [
                    //                        'Recette italienne',
                    //                    ],
                    //                    'mealsCategory' => [
                    //                        'Apéro dinatoire',
                    //                    ],
                    //                    'dietsCategory' => [
                    //                        'Recette végétarienne',
                    //                    ],
                    'userreview' => [
                        '3',
                    ],
                    'sort' => 'ASC',
                    'direction' => '',
                    'page' => '1',
                ],
            ],
        ];
    }
}
