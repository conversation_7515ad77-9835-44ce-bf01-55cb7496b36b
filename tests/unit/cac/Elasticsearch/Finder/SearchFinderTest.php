<?php

declare(strict_types=1);

namespace unit\cac\Elasticsearch\Finder;

use App\cac\Elasticsearch\Finder\SearchFinder;
use App\cac\Elasticsearch\FinderBuilder;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use PMD\Bundle\FemElasticsearchBundle\Finder\Finder;
use PMD\Bundle\FemElasticsearchBundle\Query\MultiQuery;

class SearchFinderTest extends TestCase
{
    public function testFindRelatedArticles(): void
    {
        $finder = $this->getMockBuilder(Finder::class)
            ->disableOriginalConstructor()
            ->setMethods(['mfind'])
            ->getMock();

        $finder->expects($this->once())
            ->method('mfind')
            ->willReturn(new MultiQuery());

        /** @var MockObject|FinderBuilder $finderBuilder */
        $finderBuilder = $this->getMockBuilder(FinderBuilder::class)
            ->disableOriginalConstructor()
            ->setMethods(['get'])
            ->getMock();

        $finderBuilder->expects($this->once())
            ->method('get')
            ->willReturn($finder);

        $searchFinder = new SearchFinder($finderBuilder);

        $searchFinder->findRelatedArticles('gâteau chocolat');
    }
}
