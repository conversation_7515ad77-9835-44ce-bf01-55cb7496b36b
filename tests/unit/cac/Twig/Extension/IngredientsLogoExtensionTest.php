<?php

declare(strict_types=1);

namespace unit\cac\Twig\Extension;

use App\cac\Twig\Extension\IngredientsLogoExtension;
use PHPUnit\Framework\TestCase;
use Twig\TwigFilter;

class IngredientsLogoExtensionTest extends TestCase
{
    private IngredientsLogoExtension $extension;

    protected function setUp(): void
    {
        $this->extension = new IngredientsLogoExtension();
    }

    public function testGetFiltersReturnsExpectedFilter(): void
    {
        $filters = $this->extension->getFilters();

        $this->assertCount(1, $filters);
        $this->assertInstanceOf(TwigFilter::class, $filters[0]);
        $this->assertEquals('pmd_display_logo', $filters[0]->getName());
        $this->assertEquals(['html'], $filters[0]->getSafe(new \Twig\Node\Node()));
    }

    /**
     * @dataProvider ingredientProvider
     */
    public function testDisplayLogoWithVariousIngredients(string $ingredient, bool $expected, string $path): void
    {
        $result = $this->extension->display<PERSON>ogo($ingredient, $path);
        $this->assertEquals($expected, $result);
    }

    public function ingredientProvider(): array
    {
        return [
            'matching ingredient 1' => ['pomme', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'matching ingredient 2' => ['POMME', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'matching ingredient 3' => ['5 pommes ', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'matching ingredient 4' => ['POMME', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'matching ingredient 5' => ['1 pomme granny', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'non-matching ingredient' => ['banane', false, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'partial match' => ['pommede', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'empty string' => ['', false, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'case sensitive' => ['POMME', true, '/recettes/gateau-aux-pommes-sans-pate-225041'],
            'invalid path' => ['pomme', false, '/recettes/canapes-de-pomme-au-boudin-et-canneberge-200318'],
        ];
    }

    public function testDisplayLogoOutsideCampaignDates(): void
    {
        $extension = $this->getMockBuilder(IngredientsLogoExtension::class)
            ->onlyMethods(['isCampaignActive'])
            ->getMock();

        $extension->method('isCampaignActive')
            ->willReturn(false);

        $this->assertFalse($extension->displayLogo('pomme', '/recettes/gateau-aux-pommes-sans-pate-225041'));
    }

    public function testDisplayLogoWithinCampaignDates(): void
    {
        $extension = $this->getMockBuilder(IngredientsLogoExtension::class)
            ->onlyMethods(['isCampaignActive'])
            ->getMock();

        $extension->method('isCampaignActive')
            ->willReturn(true);

        $this->assertTrue($extension->displayLogo('pomme', '/recettes/gateau-aux-pommes-sans-pate-225041'));
    }
}
