<?php

declare(strict_types=1);

namespace unit\common\Config;

use App\common\Config\AppConfig;
use PHPUnit\Framework\TestCase;

final class AppConfigTest extends TestCase
{
    /**
     * @dataProvider provider
     */
    public function test($inputs, $expected): void
    {
        $appConfig = new AppConfig($inputs);

        $this->assertEquals($appConfig->getData(), $inputs);
        $this->assertEquals($expected['brandKey'], $appConfig->getBrandKey());
        $this->assertEquals($expected['projectFolder'], $appConfig->get('projectFolder'));
        $this->assertNull($appConfig->get('undefinedKey'));
    }

    public function provider(): array
    {
        return [
            [
                [
                    'brandKey' => 'FAC',
                    'projectFolder' => 'fac',
                    'domain' => 'femmeactuelle.fr',
                ],
                [
                    'brandKey' => 'FAC',
                    'projectFolder' => 'fac',
                ],
            ],
            [
                [
                    'projectFolder' => 'Shopping',
                    'subDomain' => 'shopping',
                    'brand' => [
                        'brandKey' => 'FAC',
                    ],
                ],
                [
                    'brandKey' => 'FAC',
                    'projectFolder' => 'Shopping',
                ],
            ],
        ];
    }
}
