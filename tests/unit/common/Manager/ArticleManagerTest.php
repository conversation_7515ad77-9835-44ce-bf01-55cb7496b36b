<?php

declare(strict_types=1);

namespace unit\common\Manager;

use App\common\Manager\ArticleManager;
use App\fac\Model\Article;
use PHPUnit\Framework\TestCase;
use PMD\DraftToHtmlBundle\Renderer\HtmlRenderer;

final class ArticleManagerTest extends TestCase
{
    public function testRemoveDuplicatedArticles(): void
    {
        $article1 = new Article(['id' => 'cc-ae']);
        $article2 = new Article(['id' => 'td-vb']);
        $article3 = new Article(['id' => 'xc-ds']);

        $list1 = [$article1, $article2, $article3];
        $list2 = [$article2, $article3];

        $manager = new ArticleManager($this->createMock(HtmlRenderer::class));

        $result = $manager->removeDuplicatedArticles($list1, $list2);
        $this->assertCount(1, $result);
        $this->assertSame($article1, $result[0]);

        $list2 = [$article2, $article1, $article3];
        $result = $manager->removeDuplicatedArticles($list1, $list2);
        $this->assertCount(0, $result);

        $result = $manager->removeDuplicatedArticles($list1, []);
        $this->assertCount(3, $result);
        $this->assertSame([$article1, $article2, $article3], $list1);
    }
}
