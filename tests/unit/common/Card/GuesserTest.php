<?php

declare(strict_types=1);

namespace unit\common\Card;

use App\common\Card\Guesser;
use PHPUnit\Framework\TestCase;

class GuesserTest extends TestCase
{
    /**
     * @dataProvider zoneLabelProvider
     */
    public function testGuessZone(string $zoneLabel, string $expected): void
    {
        $guesser = new Guesser();

        $this->assertEquals($expected, $guesser->guessZone($zoneLabel));
    }

    /**
     * @dataProvider filtersLabelProvider
     */
    public function testGuessFilters(string $filtersLabel, array $expected): void
    {
        $guesser = new Guesser();

        $this->assertEquals($expected, $guesser->guessFilters($filtersLabel));
    }

    public function zoneLabelProvider(): \Generator
    {
        yield ['angle-mort', Guesser::DEFAULT_ZONE];
        yield ['other-name', Guesser::DEFAULT_ZONE];
        yield ['other-name,extra-label', Guesser::DEFAULT_ZONE];
        yield ['', Guesser::DEFAULT_ZONE];
        yield ['zone[angle-mort]', Guesser::DEFAULT_ZONE];
        yield ['zone[other-name]', 'other-name'];
        yield ['zone[other-name,extra-label]', 'other-name,extra-label'];
        yield ['zone[]', Guesser::DEFAULT_ZONE];
        yield ['zon[other-name]', Guesser::DEFAULT_ZONE];
        yield ['zone[ other-name ]', 'other-name'];
        yield ['zone [other-name]', 'other-name'];
        yield ['zone [ other-name ] ', 'other-name'];
    }

    public function filtersLabelProvider(): \Generator
    {
        yield ['', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => true,
        ]];
        yield ['filters[]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => true,
        ]];
        yield ['filters[  ]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => true,
        ]];
        yield ['filters[(categories:/recettes/dessert)]', [
            'categories' => ['/recettes/dessert'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(categories:/cuisine/recettes,/cuisine/tips)]', [
            'categories' => ['/cuisine/recettes', '/cuisine/tips'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(categories:/cuisine/recettes&/cuisine/tips)]', [
            'categories' => ['/cuisine/recettes&/cuisine/tips'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(categories:/cuisine/recettes|/cuisine/tips)]', [
            'categories' => ['/cuisine/recettes|/cuisine/tips'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[( categories : /cuisine/recettes , /cuisine/tips )]', [
            'categories' => ['/cuisine/recettes', '/cuisine/tips'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => [],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:dessert)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:dessert,boisson)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert', 'boisson'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:dessert&chocolat)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert', 'chocolat'],
            'tagsOperator' => Guesser::OPERATOR_AND,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:dessert|chocolat)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert|chocolat'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:boisson,dessert&chocolat)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['boisson', 'dessert', 'chocolat'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(tags:dessert&chocolat,boisson)]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert', 'chocolat', 'boisson'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[ ( tags : dessert , boisson ) ]', [
            'categories' => [],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert', 'boisson'],
            'tagsOperator' => Guesser::OPERATOR_OR,
            'noFilter' => false,
        ]];
        yield ['filters[(categories:/cuisine/recettes,/cuisine/tips),(tags:dessert&chocolat)]', [
            'categories' => ['/cuisine/recettes', '/cuisine/tips'],
            'operator' => Guesser::OPERATOR_OR,
            'tags' => ['dessert', 'chocolat'],
            'tagsOperator' => Guesser::OPERATOR_AND,
            'noFilter' => false,
        ]];
    }
}
