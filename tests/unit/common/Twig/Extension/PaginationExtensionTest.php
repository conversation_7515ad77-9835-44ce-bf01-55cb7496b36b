<?php

declare(strict_types=1);

namespace unit\common\Twig\Extension;

use App\common\Twig\Extension\PaginationExtension;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\RequestStack;

class PaginationExtensionTest extends TestCase
{
    private PaginationExtension $extension;

    protected function setUp(): void
    {
        $requestStack = $this->createMock(RequestStack::class);
        $this->extension = new PaginationExtension($requestStack);
    }

    /**
     * @dataProvider pageDataProvider
     */
    public function testPagePagination(array $data, ?array $expected): void
    {
        $pagination = $this->extension->getPaginationRenderPage(...$data);

        $this->assertions($pagination, $expected);
    }

    public static function pageDataProvider(): \Generator
    {
        yield 'with nb results < max per page' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => '',
                'currentPage' => 1,
                'nbResults' => 5,
                'maxPerPage' => 10,
            ],
            'expected' => null,
        ];

        yield 'with invalid page given' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => 'page',
                'currentPage' => 5,
                'nbResults' => 33,
                'maxPerPage' => 10,
            ],
            'expected' => null,
        ];

        yield 'with max page given' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => 'page',
                'currentPage' => 1,
                'nbResults' => 150,
                'maxPerPage' => 10,
                'maxPages' => 10,
            ],
            'expected' => [
                'pagination' => [
                    'last' => 10,
                ],
                // other keys are tested in other scenarios
            ],
        ];

        yield 'without page uri on page 1' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => 'page',
                'currentPage' => 1,
                'nbResults' => 12,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'next' => 2,
                    'last' => 3,
                ],
                'max_per_page' => 5,
                'current_page' => 1,
                'previous_page_url' => null,
                'next_page_url' => '?page=2',
            ],
        ];

        yield 'without page uri on page > 1' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => 'page',
                'currentPage' => 2,
                'nbResults' => 12,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 1,
                    'next' => 3,
                ],
                'max_per_page' => 5,
                'current_page' => 2,
                'previous_page_url' => '',
                'next_page_url' => '?page=3',
            ],
        ];

        yield 'without page uri on last page' => [
            'data' => [
                'pageUri' => '',
                'pageParameter' => 'page',
                'currentPage' => 3,
                'nbResults' => 12,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '?page=2',
                'next_page_url' => null,
            ],
        ];

        yield 'with page uri, without page param, on page > 2' => [
            'data' => [
                'pageUri' => '/test/uri?param1=1&param2=2',
                'pageParameter' => 'page',
                'currentPage' => 3,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '/test/uri?param1=1&param2=2&page=2',
                'next_page_url' => '/test/uri?param1=1&param2=2&page=4',
            ],
        ];

        yield 'with page uri, with page param, on page > 2' => [
            'data' => [
                'pageUri' => '/test/uri?param1=1&page=3&param2=2',
                'pageParameter' => 'page',
                'currentPage' => 3,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '/test/uri?param1=1&page=2&param2=2',
                'next_page_url' => '/test/uri?param1=1&page=4&param2=2',
            ],
        ];

        yield 'with page uri, encoded, with page param, on page > 2' => [
            'data' => [
                'pageUri' => '/test/uri?param%5Ba%5D=1&param%5Bpage%5D=3&param%5Bb%5D=2',
                'pageParameter' => 'param[page]',
                'currentPage' => 3,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '/test/uri?param%5Ba%5D=1&param%5Bpage%5D=2&param%5Bb%5D=2',
                'next_page_url' => '/test/uri?param%5Ba%5D=1&param%5Bpage%5D=4&param%5Bb%5D=2',
            ],
        ];

        yield 'with page uri path contains page parameter' => [
            'data' => [
                'pageUri' => '/page/test/uri',
                'pageParameter' => 'page',
                'currentPage' => 3,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '/page/test/uri?page=2',
                'next_page_url' => '/page/test/uri?page=4',
            ],
        ];

        yield 'with page uri path contains page parameter and page param set in query params' => [
            'data' => [
                'pageUri' => '/page/test/uri?param=test&page=3',
                'pageParameter' => 'page',
                'currentPage' => 3,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                ],
                'max_per_page' => 5,
                'current_page' => 3,
                'previous_page_url' => '/page/test/uri?param=test&page=2',
                'next_page_url' => '/page/test/uri?param=test&page=4',
            ],
        ];
    }

    /**
     * @dataProvider offsetDataProvider
     */
    public function testOffsetPagination(array $data, ?array $expected): void
    {
        $pagination = $this->extension->getPaginationRenderOffset(...$data);

        $this->assertions($pagination, $expected);
    }

    public static function offsetDataProvider(): \Generator
    {
        yield 'with page uri on first page' => [
            'data' => [
                'pageUri' => '/test/uri',
                'offset' => 0,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'next' => 2,
                    'last' => 4,
                ],
                'max_per_page' => 5,
                'offset' => 0,
                'previous_offset' => -5,
                'previous_page_url' => null,
                'next_offset' => 5,
                'next_page_url' => '/test/uri?offset=5',
                'page_uri' => '/test/uri?offset=0',
                'page_parameter' => 'offset',
            ],
        ];

        yield 'with page uri on second page' => [
            'data' => [
                'pageUri' => '/test/uri',
                'offset' => 5,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 1,
                    'next' => 3,
                    'last' => 4,
                ],
                'max_per_page' => 5,
                'offset' => 5,
                'previous_offset' => 0,
                'previous_page_url' => '/test/uri?offset=0',
                'next_offset' => 10,
                'next_page_url' => '/test/uri?offset=10',
                'page_uri' => '/test/uri?offset=5',
                'page_parameter' => 'offset',
            ],
        ];

        yield 'with page uri on page > 2' => [
            'data' => [
                'pageUri' => '/test/uri',
                'offset' => 10,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                    'last' => 4,
                ],
                'max_per_page' => 5,
                'offset' => 10,
                'previous_offset' => 5,
                'previous_page_url' => '/test/uri?offset=5',
                'next_offset' => 15,
                'next_page_url' => '/test/uri?offset=15',
                'page_uri' => '/test/uri?offset=10',
                'page_parameter' => 'offset',
            ],
        ];

        yield 'with page uri on last page' => [
            'data' => [
                'pageUri' => '/test/uri',
                'offset' => 15,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 3,
                ],
                'max_per_page' => 5,
                'offset' => 15,
                'previous_offset' => 10,
                'previous_page_url' => '/test/uri?offset=10',
                'next_offset' => null,
                'next_page_url' => null,
                'page_uri' => '/test/uri?offset=15',
                'page_parameter' => 'offset',
            ],
        ];

        yield 'with page uri with offset param on page > 2' => [
            'data' => [
                'pageUri' => '/test/uri?offset=10',
                'offset' => 10,
                'nbResults' => 17,
                'maxPerPage' => 5,
            ],
            'expected' => [
                'pagination' => [
                    'first' => 1,
                    'previous' => 2,
                    'next' => 4,
                    'last' => 4,
                ],
                'max_per_page' => 5,
                'offset' => 10,
                'previous_offset' => 5,
                'previous_page_url' => '/test/uri?offset=5',
                'next_offset' => 15,
                'next_page_url' => '/test/uri?offset=15',
                'page_uri' => '/test/uri?offset=10',
                'page_parameter' => 'offset',
            ],
        ];
    }

    private function assertions(?array $pagination, ?array $expected): void
    {
        if (null === $expected) {
            $this->assertNull($pagination);

            return;
        }

        foreach ($expected as $key => $value) {
            $this->assertArrayHasKey($key, $pagination);
            if ('pagination' === $key) {
                foreach ($value as $itemKey => $itemValue) {
                    $this->assertArrayHasKey($itemKey, $pagination[$key]);
                    $this->assertEquals($itemValue, $pagination[$key][$itemKey]);
                }
            } else {
                $this->assertEquals($value, $pagination[$key]);
            }
        }
    }
}
