<?php

declare(strict_types=1);

namespace unit\common\Twig\Extension;

use App\common\Twig\Extension\SplitHtmlExtension;
use PHPUnit\Framework\TestCase;

class SplitHtmlExtensionTest extends TestCase
{
    private SplitHtmlExtension $extension;

    protected function setUp(): void
    {
        // Initialise l'extension avec une configuration fictive
        $this->extension = new SplitHtmlExtension(['some_config']);
    }

    public function testSplitHTMLReturnsMultipleParagraphs(): void
    {
        // Contenu HTML de test avec plusieurs <p>
        $htmlContent = '
            <p>Premier paragraphe.</p>
            <p>Deuxième paragraphe.</p>
            <p>Troisième paragraphe.</p>
        ';

        // Appel de la méthode splitHTML
        $result = $this->extension->splitHTML($htmlContent, 'p');

        // Vérification du nombre d'éléments retournés
        $this->assertCount(3, $result);

        // Vérification du contenu de chaque élément
        $this->assertSame('<p>Premier paragraphe.</p>', $result[0]);
        $this->assertSame('<p>Deuxième paragraphe.</p>', $result[1]);
        $this->assertSame('<p>Troisième paragraphe.</p>', $result[2]);
    }

    public function testSplitHTMLReturnsFirstDiv(): void
    {
        // Contenu HTML avec une balise <div>
        $htmlContent = '
            <div>Contenu dans un div.</div>
            <p>Un paragraphe.</p>
        ';

        // Appel de la méthode splitHTML pour <div>
        $result = $this->extension->splitHTML($htmlContent, 'div');

        // Vérification du nombre d'éléments retournés
        $this->assertCount(1, $result);

        // Vérification du contenu du premier <div>
        $this->assertSame('<div>Contenu dans un div.</div>', $result[0]);
    }

    public function testSplitHTMLReturnsNullIfNoTags(): void
    {
        // Contenu HTML sans balises <p>
        $htmlContent = '
            <div>Contenu dans un div.</div>
        ';

        // Appel de la méthode splitHTML pour <p>
        $result = $this->extension->splitHTML($htmlContent, 'p');

        // Vérification que le résultat est null (car il n'y a pas de <p>)
        $this->assertNull($result);
    }

    public function testSplitHTMLIgnoresInvalidTagNames(): void
    {
        // Contenu HTML avec un mélange de balises
        $htmlContent = '
            <p>Paragraphe valide.</p>
            <invalid>Balise non valide.</invalid>
        ';

        // Appel de la méthode splitHTML avec une balise invalide
        $result = $this->extension->splitHTML($htmlContent, 'invalid');

        // Vérification que le résultat est null (car la balise n'est pas valide)
        $this->assertNull($result);
    }

    public function testSplitHTMLHandlesEmptyContent(): void
    {
        // Appel de la méthode splitHTML avec un contenu vide
        $result = $this->extension->splitHTML('', 'p');

        // Vérification que le résultat est null (car le contenu est vide)
        $this->assertNull($result);
    }
}
