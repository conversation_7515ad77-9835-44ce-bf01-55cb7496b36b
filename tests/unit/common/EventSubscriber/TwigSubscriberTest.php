<?php

declare(strict_types=1);

namespace unit\common\EventSubscriber;

use App\common\EventSubscriber\TwigSubscriber;
use App\common\Manager\HeaderManager;
use PHPUnit\Framework\TestCase;
use Twig\Environment;

final class TwigSubscriberTest extends TestCase
{
    public array $globals = ['brandConfig' => ['appId' => 'toto', 'name' => 'tata']];

    /**
     * @dataProvider getData()
     * */
    public function testOnContentDispatched(array $globals, ?array $headers): void
    {
        $twig = $this->createMock(Environment::class);
        $twig->method('getGlobals')->willReturnCallback(fn () => $this->globals);
        $twig->method('addGlobal')->willReturnCallback(function (string $name, mixed $value) {
            $this->globals[$name] = $value;

            return $this->globals;
        });

        $headerManager = $this->createMock(HeaderManager::class);
        $headerManager->method('getHeader')->willReturn($headers);

        $twigSubscriber = new TwigSubscriber($twig, $headerManager);
        $twigSubscriber->onKernelRequest();

        $this->assertSame($globals, $this->globals);
    }

    public static function getData(): \Generator
    {
        yield [
            'globals' => [
                'brandConfig' => ['appId' => 'toto', 'name' => 'tata'],
                'header' => null,
            ],
            null,
        ];

        yield [
            'globals' => [
                'brandConfig' => ['appId' => 'toto', 'name' => 'tata'],
                'header' => ['navbar' => ['aaa']],
            ],
            ['navbar' => ['aaa']],
        ];
    }
}
