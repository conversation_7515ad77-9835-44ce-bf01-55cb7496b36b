<?php

declare(strict_types=1);

namespace unit\TopActu\Twig;

use App\TopActu\Twig\CoreAdsCustomExtension;
use PHPUnit\Framework\TestCase;

class CoreAdsCustomTest extends TestCase
{
    private CoreAdsCustomExtension $extension;

    protected function setUp(): void
    {
        $this->extension = new CoreAdsCustomExtension();
    }

    /**
     * @dataProvider dataProviderTest
     */
    public function testCoreAdsTagDefer(string $html, string $expectedHtml, string $message): void
    {
        $result = $this->extension->coreAdsTagDefer($html);
        $this->assertSame($expectedHtml, $result, $message);
    }

    /**
     * Data provider.
     */
    public function dataProviderTest(): array
    {
        return [
            'empty body' => ['', '', 'Expected an empty string for an empty HTML body'],
            'no ads-core-placer class' => [
                '<div class="no-ads"></div>',
                '<div class="no-ads"></div>',
                'Expected the HTML to remain unchanged if no ads-core-placer class is found',
            ],
            'single ads-core-placer class' => [
                '<div class="ads-core-placer"></div>',
                '<div class="ads-core-placer-defer"></div>',
                'Expected ads-core-placer class to be changed to ads-core-placer-defer',
            ],
            'multiple ads-core-placer classes' => [
                '<div class="ads-core-placer"></div><div class="ads-core-placer"></div>',
                '<div class="ads-core-placer-defer"></div><div class="ads-core-placer-defer"></div>',
                'Expected all ads-core-placer classes to be changed to ads-core-placer-defer',
            ],
            'nested ads-core-placer class' => [
                '<div><div class="ads-core-placer"></div></div>',
                '<div><div class="ads-core-placer-defer"></div></div>',
                'Expected nested ads-core-placer class to be changed to ads-core-placer-defer',
            ],
        ];
    }
}
