<?php

declare(strict_types=1);

namespace App\Tests\TopActu\Twig;

use App\TopActu\Twig\EmbedVideoRendererExtension;
use PHPUnit\Framework\TestCase;

class EmbededVideoRenderedTest extends TestCase
{
    private EmbedVideoRendererExtension $extension;

    protected function setUp(): void
    {
        $this->extension = new EmbedVideoRendererExtension();
    }

    /**
     * @dataProvider dataProviderTest
     */
    public function testGetEmbedVideoTest(string $htmlBody, string $playerId, ?string $customChildEmbedId, string $expected): void
    {
        $result = $this->extension->getEmbedVideo($htmlBody, $playerId, $customChildEmbedId);
        $this->assertSame($expected, $result);
    }

    // Test avec un `playerId` vide
    public function testGetEmbedVideoWithEmptyPlayerIdThrowsException(): void
    {
        $htmlBody = '<div class="video-wrapper-dm-embed" id="dm-embed-123"></div></div>';

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Le playerId ne peut pas être vide.');

        $this->extension->getEmbedVideo($htmlBody, '');
    }

    /**
     * Data provider pour les cas de tests d'intégration de vidéo.
     */
    public function dataProviderTest(): array
    {
        return [
            'single video' => [
                '<div class="video-wrapper-dm-embed" id="dm-embed-123"></div></div>',
                'player123',
                '',
                '<div class="ratio-16-9"><div class="prisma-player video-wrapper-dm-embed" id="dm-embed-123"><div id="child-dm-embed-123"></div></div></div>',
            ],
            'multiple videos' => [
                '<div class="video-wrapper-dm-embed" id="dm-embed-123"></div></div><div class="video-wrapper-dm-embed" id="dm-embed-456"></div></div>',
                'player123',
                '',
                '<div class="ratio-16-9"><div class="prisma-player video-wrapper-dm-embed" id="dm-embed-123"><div id="child-dm-embed-123"></div></div></div>'
                .'<div class="ratio-16-9"><div class="prisma-player video-wrapper-dm-embed" id="dm-embed-456"><div id="child-dm-embed-456"></div></div></div>',
            ],
            'custom child embed id' => [
                '<div class="video-wrapper-dm-embed" id="dm-embed-123"></div></div>',
                'player123',
                'custom-embed-id',
                '<div class="ratio-16-9"><div class="prisma-player video-wrapper-dm-embed" id="dm-embed-123"><div id="custom-embed-id"></div></div></div>',
            ],
        ];
    }
}
