Lancement du daemon Selenium pour les tests js:
------------------------------

<PERSON>ur Lancer les commandes suivantes et démarrer selenium-server en local, il vous sera necessaire de démarrer en local selenium standalone
    
    make start-local-js

Ce qui va lancer : 

    wget https://github.com/SeleniumHQ/selenium/releases/download/selenium-3.141.59/selenium-server-standalone-3.141.59.jar
    java -jar tests/selenium-server-standalone-3.141.59.jar

Cela va charger selenium-server-standalone-3.141.59 en utilisant `Java -jar` et vous pourrez lancer ensuite 
    
    BEHAT_TAGS=javascript make test-functional-js

Démarrage des tests Behat :
------------------------------
Lancer les commandes depuis n'importe quel terminal.

Plusieurs façons :

    cd www/tests && php bin/behat
    make test-functional

ou encore

    cd www && ./vendor/bin/behat -c tests/behat.yml tests/features/page.feature -v
ou encore

    cd current && phing env:dev test:functional
