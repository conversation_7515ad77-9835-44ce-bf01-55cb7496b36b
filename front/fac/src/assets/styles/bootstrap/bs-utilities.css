.clearfix::after,
.clearfix::before,
.form-delta::after,
.form-delta::before,
.gallery-delta .gallery-footer .users::after,
.gallery-delta .gallery-footer .users::before,
.list-grid::after,
.list-grid::before {
	content: ' ';
	display: table;
}

.clearfix::after,
.form-delta::after,
.gallery-delta .gallery-footer .users::after,
.list-grid::after {
	clear: both;
}

.center-block {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.pull-right {
	float: right !important;
}

.pull-left {
	float: left !important;
}

.hide {
	display: none !important;
}

.show {
	display: block !important;
}

.invisible {
	visibility: hidden;
}

.text-hide {
	font: 0/0;
	color: transparent;
	text-shadow: none;
	background-color: transparent;
	border: 0;
}

.hidden {
	display: none !important;
	visibility: hidden !important;
}

.affix {
	position: fixed;
}
