.media-overlay {
	display: table;
	position: relative;
	z-index: 1;
	table-layout: fixed;
}

.media-overlay::before {
	content: '';
	position: absolute;
	z-index: -1;
	inset: 0;
}

.overlay-content {
	display: table-cell;
	word-wrap: break-word;
	overflow-wrap: break-word;
	width: 100%;
}

.overlay-black {
	color: var(--color-white);
}

.overlay-black a {
	color: var(--color-white);
	text-decoration: none;
}

.overlay-black::before {
	background-color: var(--color-black);
}

.fade-full {
	opacity: 0;
	transition: opacity;
}

.fade-full::before {
	opacity: 0;
	transition: opacity;
}

.fade-full:hover {
	opacity: 1;
	transition: opacity;
}

.fade-full:hover::before {
	opacity: 1;
	transition: opacity;
}

.fade-half {
	opacity: 0;
	transition: opacity;
}

.fade-half::before {
	opacity: 0;
	transition: opacity;
}

.fade-half:hover {
	opacity: 1;
	transition: opacity;
}

.fade-half:hover::before {
	opacity: 0.5;
	transition: opacity;
}

.fade-half-to-quarter {
	opacity: 0.5;
	transition: opacity;
}

.fade-half-to-quarter::before {
	opacity: 0.5;
	transition: opacity;
}

.fade-half-to-quarter:hover {
	opacity: 1;
	transition: opacity;
}

.fade-half-to-quarter:hover::before {
	opacity: 0.25;
	transition: opacity;
}

.fade-quarter-to-half {
	opacity: 0.25;
	transition: opacity;
}

@media (--media-max-tablet) {
	.fade-quarter-to-half {
		opacity: 1;
	}

	.fade-quarter-to-half::before {
		opacity: 0.5;
	}
}

.fade-quarter-to-half::before {
	opacity: 0.25;
	transition: opacity;
}

.fade-quarter-to-half:hover {
	opacity: 1;
	transition: opacity;
}

.fade-quarter-to-half:hover::before {
	opacity: 0.5;
	transition: opacity;
}

.transition-fast {
	transition-duration: 0.3s;
}

.transition-fast:hover,
.transition-fast::before {
	transition-duration: 0.3s;
}

.transition-middle {
	transition-duration: 0.7s;
}

.transition-middle:hover,
.transition-middle::before {
	transition-duration: 0.7s;
}

.transition-long {
	transition-duration: 1.2s;
}

.transition-long:hover,
.transition-long::before {
	transition-duration: 1.2s;
}
