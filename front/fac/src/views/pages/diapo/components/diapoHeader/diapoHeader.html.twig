{% set image = image is defined ? image : null %}
{% set total = total is defined ? total : 0 %}
{% set title = title is defined ? title : 0 %}

<header class="diapoHeader {{ image ? 'withImage' : '' }}">
{% if image %}
    <div class="diapoHeader-imageWrapper">
        <img class="diapoHeader-image" src="{{ image }}" />
        <p class="diapoHeader-totalLabel">{{ total }} photos</p>
    </div>
{% else %}
    <p class="diapoHeader-totalLabel">{{ total }} photos</p>
{% endif %}
    <h1 class="diapoHeader-title">{{ label }}</h1>
</header>
