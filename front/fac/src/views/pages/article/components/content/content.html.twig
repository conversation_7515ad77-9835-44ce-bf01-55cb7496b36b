{% import '@CommonFront/macros/svg/views/svg.html.twig' as macroSvg %}

{% set text = draftToHtml(article.body.draft).html|raw %}
{% set isArticleLongAffiliated = article.hasQualifier('article-long') and article.hasQualifier('affiliation') %}
{% set charactersBetweenEachAd = isArticleLongAffiliated ? 5000 : 1000 %}
{% set hasEmbed = hasEmbed is defined ? hasEmbed : null %}
{% set isCoachingArticle = coaching.category is defined ? true : false %}
{% set article_long = article_long is defined ? article_long : null %}

<template data-embed>{{ hasEmbed|raw }}</template>

<script>
    consentCheck('allConsentGiven', () => {
        const dataEmbed = document.querySelector('template[data-embed]')
        const embedScript = dataEmbed?.content.cloneNode(true)
        if (embedScript) dataEmbed.after(embedScript)
    })
</script>

<div
    {% if isCoachingArticle %}
        data-fem-article-nav='{"offset": 60, "offsetOpen": 70}'
    {% endif %}
    class="articleContent{% if article_long %} isLong{% endif %}"
    data-block="articleContent"
    data-article-content>
    {% if isImcPage|default(false) %}
       {% include '@Front/src/views/components/imc/imc.html.twig' %}
    {% endif %}
    {% if isCoachingArticle %}
        {% include '@SharedComponents/fem-coaching/components/articlenav-loader/articlenav-loader.html.twig' with {
            coaching: coaching
        } only %}
    {% endif %}

    {% set adsMobile %}
        <div class="ad pave-haut2 onlyMobile" data-block="Pave-Haut2">
            {{ coreads_tag('Pave-Haut2', {id: 'Pave-Haut2_%ID%-mobile', class: isPremium|default(false) ? 'ads-lazyload' : null}) }}
        </div>
    {% endset %}
    {% set adsDesktop %}
        <div class="ad pave-haut2-desktop ads-placeholder onlyDesktop">
            {{ coreads_tag('Pave-Haut2-Desktop', {id: 'Pave-Haut2-Desktop_%ID%-desktop', class: isPremium|default(false) ? 'ads-lazyload' : null}) }}
        </div>
    {% endset %}

    {% set getFormat = article_long ? article_long.body : text %}

    {% if article_long or text is not null %}
        {{ coreads_inject_html(getFormat, {mobile: adsMobile, desktop: adsDesktop}, charactersBetweenEachAd, 150) }}
    {% endif %}

    {% if isCoachingArticle %}
        <div data-desktop-bar class="{{ appConfig.brand|lower }}"></div>
    {% endif %}
</div>
