.articleContent {
	font-size: var(--fontSize-md);
	line-height: 28px;
	letter-spacing: -0.5px;
	text-align: center;
	color: var(--color-greyText);

	& > * {
		text-align: left;

		+ * {
			margin-block: 1em;
		}
	}

	i,
	em {
		font-style: italic;
	}

	ul,
	ol {
		list-style: initial;
		padding-left: var(--space24);
		list-style-position: inside;
	}

	li {
		margin-bottom: 0.5em;
	}

	& img {
		max-width: 100%;
		height: auto;
	}

	& figure {
		position: relative;
		width: 100%;

		&[data-site='PMD_BONE_Image'] {
			display: inline-block;
			width: 100%;
			margin-left: var(--margin-xs);
			margin-right: var(--margin-xs);
		}
	}

	& figcaption {
		margin-top: var(--margin-sm);
		width: 100%;
		text-align: right;

		& .caption {
			display: flex;
			align-items: flex-start;
			justify-content: flex-end;
			color: var(--color-text);
			font-size: var(--fontSize-sm);
			font-variation-settings: 'wght' var(--fontWeight-medium);
			font-style: normal;
			border-top: 1px solid var(--color-border);
			line-height: 20px;
			padding-top: 4px;
		}

		& .credit {
			font-size: var(--fontSize-xxs);
			display: inline-block;
			padding: 0 10px;
			color: var(--color-text);
			font-family: var(--fontFamily-base);
			font-variation-settings: 'wght' var(--fontWeight-medium);
			background-color: rgb(255 255 255 / 80%);

			@media (--media-min-lg-tablet) {
				font-size: var(--fontSize-sm);
			}
		}
	}

	& h2 {
		font-family: var(--fontFamily-heading);
		font-variation-settings: 'wght' var(--fontWeight-semibold);
		font-size: var(--fontSize-xxl);
		line-height: 36px;
		color: var(--color-epsilon);
	}

	& h3 {
		font-family: var(--fontFamily-base);
		font-variation-settings: 'wght' var(--fontWeight-medium);
		font-size: var(--fontSize-xl);
		line-height: 36px;
	}

	& strong {
		font-variation-settings: 'wght' var(--fontWeight-semibold);
	}

	& a {
		color: var(--color-textAlt);
		text-decoration: underline;
	}

	& iframe {
		max-width: 100%;
	}

	& div[id*='Pave-Haut2'] {
		max-width: 100%;
		max-height: inherit;
	}

	& .ads-core-placer {
		text-align: center;
	}

	& .lien-c2c {
		display: flex;
		justify-content: center;

		.cta {
			display: block;
			text-align: center;
			background-color: var(--color-premium);
			border-radius: 4px;
			color: var(--color-white);
			padding: var(--space-sm) var(--space-md);
			margin: var(--space-sm) 0;
			text-decoration: none;
			min-width: 300px;
			font-weight: var(--fontWeight-bold);
		}
	}

	&.isLong {
		h2 {
			font-variation-settings: 'wght' var(--fontWeight-regular);
			font-size: var(--fontSize-xxl);
			letter-spacing: -0.2px;
		}

		@media (--media-min-desktop) {
			h2 {
				font-size: var(--fontSize-xxxl);
			}
		}
	}
}
