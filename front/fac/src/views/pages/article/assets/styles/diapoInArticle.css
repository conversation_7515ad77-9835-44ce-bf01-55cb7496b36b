.dia {
	&-diapo {
		margin-bottom: var(--fontSize-lg);
		overflow: hidden;
	}

	&-picture {
		position: relative;

		img {
			max-width: 100%;
		}
	}

	&-title-container,
	&-title {
		font-family: var(--fontFamily-heading);
		font-size: var(--fontSize-lg);
		font-variation-settings: 'wght' var(--fontWeight-semibold);
		font-weight: var(--fontWeight-semibold);
		margin-bottom: var(--space-md);
	}

	&-figcaption {
		padding: var(--space-sm) var(--space-mdp) var(--space-mdp) var(--space-mdp);
		background-color: var(--color-white-lighter);
	}

	&-description {
		font-family: var(--fontFamily-base);
		font-size: var(--fontSize-base);
		font-variation-settings: 'wght' var(--fontWeight-regular);
		font-weight: var(--fontWeight-regular);
	}

	&-credit {
		display: flex;
		justify-content: space-between;
		color: var(--color-greyText);
		font-size: var(--fontSize-sm);
		font-variation-settings: 'wght' var(--fontWeight-extralight);
		font-weight: var(--fontWeight-extralight);
	}

	&-sidebar {
		display: none;

		@media (--media-min-desktop) {
			display: flex;
			flex-direction: column;
		}

		&Row {
			flex-grow: 1;
			position: relative;
		}

		&Ad {
			position: sticky;
			top: var(--space-sm);
		}
	}
}
