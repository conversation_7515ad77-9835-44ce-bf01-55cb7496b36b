// Import Assets
import 'sourceAssets/styles/colors'
import 'sourceAssets/styles/global'
import 'sourceAssets/styles/utils'
import 'shared/assets/styles/playerSticky.css'

// Import templates
import 'sourceViews/templates/default/config'
import 'sourceViews/templates/sidebar/config'

// Import components
import 'sourceViews/essential.js'
import 'sourceViews/components/articleAuthor/config'
import 'sourceViews/components/articleBanner/config'
import 'sourceViews/components/articleCollaboration/config'
import 'sourceViews/components/articleDate/config'
import 'sourceViews/components/articleDropdown/config'
import 'sourceViews/components/articleList/config'
import 'sourceViews/components/articleSource/config'
import 'sourceViews/components/articleTitle/config'
import 'sourceViews/components/articleSubtitle/config'
import 'sourceViews/components/button/config'
import 'sourceViews/components/breadcrumbs/config'
import 'sourceViews/components/diapoButton/config'
import 'sourceViews/components/followTagList/config'
import 'sourceViews/components/headingTitle/config'
import 'sourceViews/components/horoscopeWidget/config'
import 'sourceViews/components/leadImage/config'
import 'sourceViews/components/magazineBlock/config'
import 'sourceViews/components/pmcInsertNL/config'
import 'sourceViews/components/parentingBar/config'
import 'sourceViews/components/pmcInsertNL/pmcInsertNLSimone/config'
import 'sourceViews/components/insertCroqKilos/config'
import 'sourceViews/components/preFooter/config'
import 'sourceViews/components/prismashopBlock/config'
import 'sourceViews/components/socialBar/config'
import 'sourceViews/components/socialButton/config'
import 'sourceViews/components/sidebar/config'
import 'sourceViews/components/sidebarLayout/config'
import 'sourceViews/components/sponsored/config'
import 'sourceViews/components/subImageBar/config'
import 'sourceViews/components/tags/config'
import 'sourceViews/components/tagCloud/config'
import 'sourceViews/components/tooltip/config'
import 'sourceViews/components/videoCard/config'
import 'sourceViews/components/videoCarousel/config'
import 'sourceViews/components/tables/config'
import 'commonSrc/views/components/podcastPlayerVoxeus/config'
import 'sourceViews/components/leadMagnetDialogsManager/config'

import 'commonSrc/views/components/authorBlock/config'
import { imcContainer } from 'sourceViews/components/imc/assets/scripts/imcUtils.js'

// Import autoinit local scripts
import './assets/scripts/script'
import './assets/styles/style'
import './assets/styles/diapoInArticle'
import './assets/styles/pmcModal'

import './components/content/config'
import './components/eventBlock/config'
import './components/printArticle/config'
import './components/podcast/config'
import './components/excerpt/config'
import './components/relatedArticles/config'

import 'commonSrc/views/components/saveArticle/config'
import 'commonSrc/views/components/saveArticle/assets/scripts/main'

import 'sourceViews/components/pmcInsertNL/pmcInsertNLMinceur/config'
import 'sourceViews/components/sprite/assets/images/logo-fa.svg'
import 'sourceViews/components/sprite/assets/images/profile-placeholder.svg'

/**
 * Imports ABTests
 */
import 'fac/pages/article/components/abtests/quizz/quizz.config.js'
import 'fac/pages/article/components/abtests/weeklyFiles/weeklyFiles.config.js'
import 'fac/pages/article/components/abtests/favorites/favorites.config.js'

if (imcContainer) {
	import(/* webpackChunkName: "dynamic-import-imc" */ 'sourceViews/components/imc/config')
}
