.longFormatHeader {
	display: flex;
	flex-direction: column;

	&-topContent {
		display: flex;
		flex-direction: column;
		font-family: var(--fontFamily-caveat);
		font-size: 60px;
		font-variation-settings: 'wght' var(--fontWeight-bold);
		margin: var(--margin-mdp) auto;

		@media (--media-min-desktop) {
			max-width: 300px;
		}
	}

	&-title {
		margin: 0 auto;
		max-width: 180px;
		line-height: 52px;
		font-size: 60px;
		transform: rotate(-5deg);

		& span {
			font-size: 80px;
		}
	}

	&-credit {
		font-size: var(--fontSize-xxxl);
		font-variation-settings: 'wght' var(--fontWeight-regular);
		margin-left: auto;

		& svg {
			transform: translateX(30px);
		}

		@media (--media-min-desktop) {
			margin-right: 0;
		}
	}

	&-links {
		display: flex;
		flex-direction: column;
		margin-bottom: var(--margin-mdp);
		width: 100%;

		@media (--media-min-desktop) {
			margin-bottom: var(--margin-sm);
			flex-direction: row;
		}
	}

	&-link {
		text-align: center;
		margin: 0 var(--margin-sm);

		@media (--media-min-desktop) {
			margin: 0 auto;
		}

		&:hover {
			text-decoration: none;
		}
	}

	&-linkNumber {
		font-family: var(--fontFamily-heading);
		font-size: 35px;

		&.accentColor {
			&-1 {
				color: var(--accent-color-1);
			}

			&-2 {
				color: var(--accent-color-2);
			}

			&-3 {
				color: var(--accent-color-3);
			}

			&-4 {
				color: var(--accent-color-4);
			}
		}
	}

	&-linkTitle {
		font-size: var(--fontSize-base);
		position: relative;
		color: var(--color-text);
		font-variation-settings: 'wght' var(--fontWeight-bold);

		&::after {
			content: '';
			position: absolute;
			bottom: -5px;
			right: 0;
			width: 100%;
			height: 5px;

			@media (--media-min-desktop) {
				display: none;
			}
		}

		@media (--media-min-desktop) {
			&:hover {
				&::after {
					display: block;
				}
			}
		}

		&.backgroundColor {
			&-1 {
				&::after {
					background-color: var(--accent-color-1);
				}
			}

			&-2 {
				&::after {
					background-color: var(--accent-color-2);
				}
			}

			&-3 {
				&::after {
					background-color: var(--accent-color-3);
				}
			}

			&-4 {
				&::after {
					background-color: var(--accent-color-4);
				}
			}
		}
	}

	&-desc {
		margin-bottom: var(--margin-lg);
		text-align: center;
		color: var(--color-text);

		@media (--media-min-desktop) {
			text-align: left;
		}
	}
}

.longFormatHeaderSticky {
	position: fixed;
	width: 100vw;
	height: 65px;
	top: 0;
	left: 0;
	transition: top 0.3s ease-in-out;
	z-index: 100;
	background-color: var(--color-white);
	border-bottom: 2px solid var(--color-borderLight);

	&-hidden {
		top: -67px;
	}

	@media (--media-min-desktop) {
		height: 60px;
	}

	.socialBar {
		display: none;

		@media (--media-min-desktop) {
			display: flex;
		}
	}

	&-container {
		max-width: 1200px;
		margin: 0 auto;
		height: 100%;
		display: grid;
		grid-template-rows: repeat(2, 1fr);

		@media (--media-min-desktop) {
			grid-template-columns: 1fr 3fr 1fr;
			grid-template-rows: 60px;
		}
	}

	&-logo {
		width: 112px;
		margin: 14px auto 5px;

		@media (--media-min-desktop) {
			width: 168px;
			margin: auto var(--margin-lg);
		}
	}

	&-links-containers {
		width: 30%;
		margin: 0 auto;
		display: flex;
		text-align: center;

		@media (--media-min-desktop) {
			margin: var(--margin-mdp) 0 0 0;
			width: 100%;
		}
	}

	&-link {
		flex: 1 0 25%;
		font-size: var(--fontSize-base);
		font-family: var(--fontFamily-heading);
		position: relative;
		text-align: center;

		@media (--media-min-desktop) {
			flex: 0 1 auto;
			margin: 0 var(--margin-sm);
			display: flex;
			justify-content: center;
		}

		&:focus,
		&:hover {
			text-decoration: none;
		}

		&.active {
			&::after {
				content: '';
				position: absolute;
				bottom: -2px;
				right: 0;
				width: 100%;
				height: 3px;
				background-color: var(--accent-color-1);
			}

			&-1 {
				& span:first-child {
					color: var(--accent-color-1);
				}

				&::after {
					background-color: var(--accent-color-1);
				}
			}

			&-2 {
				& span:first-child {
					color: var(--accent-color-2);
				}

				&::after {
					background-color: var(--accent-color-2);
				}
			}

			&-3 {
				& span:first-child {
					color: var(--accent-color-3);
				}

				&::after {
					background-color: var(--accent-color-3);
				}
			}

			&-4 {
				& span:first-child {
					color: var(--accent-color-4);
				}

				&::after {
					background-color: var(--accent-color-4);
				}
			}

			@media (--media-min-desktop) {
				& .longFormatHeaderSticky-linkTitle {
					display: block;
				}
			}
		}
	}

	&-linkNumber {
		color: var(--color-text);
		font-variation-settings: 'wght' var(--fontWeight-bold);
	}

	&-linkTitle {
		display: none;
		color: var(--color-text);
		font-variation-settings: 'wght' var(--fontWeight-semibold);
		font-family: var(--fontFamily-base);

		@media (--media-min-desktop) {
			margin-left: var(--margin-sm);
		}
	}
}
