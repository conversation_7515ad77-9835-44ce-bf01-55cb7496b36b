{% extends '@Front/src/views/templates/default/default.html.twig' %}

{# Override layout variables #}
{% set hasAds = false %}
{% set isLegacyNeeded = false %}

{% if brand == 'PRI' %}
    {% set trigram = "pri" %}
    {% set metaDescription = "Trouvez l'abonnement au magazine Prima qui vous convient grâce aux multiples formules proposées : choix du format, de la durée, du prix - Prima" %}
    {% set metaTitle = "Abonnement Prima" %}
    {% set metaTitleSuffix = " - Prima" %}
    {% set title = "Rejoignez la communauté des créatrices !" %}
    {% set buttonLink = "https://prismashop.commander1.com/c3/?tcs=2103&chn=sites-editos&src=pra-fr&cmp=header-abo&url=https://www.prismashop.fr/edito-prima?code=SIEPRABOUABOHEAD&utm_source=pra-fr&utm_medium=sites-editos&utm_campaign=header-abo" %}
    {% set dataUrl = "https://cdn-prismashop.prismamediadigital.com/pcatalog/edito/flow/category/25" %}
    {% set dfpSlug = "Prima" %}
{% elseif brand == 'FAS' %}
    {% set trigram = "ser" %}
    {% set metaDescription = "Trouvez l'abonnement au magazine Femme Actuelle Senior qui vous convient grâce aux multiples formules proposées : choix du format, de la durée, du prix - Femmeactuelle.fr" %}
    {% set metaTitle = "Abonnement Femme actuelle Senior & Serengo" %}
    {% set metaTitleSuffix = " - Femme Actuelle Senior" %}
    {% set title = "Pour les seniors en action" %}
    {% set buttonLink = "https://prismashop.commander1.com/c3/?tcs=2103&chn=sites-editos&src=ser-fr&cmp=header-abo&url=https://www.prismashop.fr/edito-femme-actuelle-senior?code=SIESERBOUABOHEAD&utm_source=ser-fr&utm_medium=sites-editos&utm_campaign=header-abo" %}
    {% set dataUrl = "https://cdn-prismashop.prismamediadigital.com/pcatalog/edito/flow/category/361" %}
    {% set dfpSlug = "Serengo" %}
{% else %}
    {% set trigram = "fac" %}
    {% set metaDescription = "Trouvez l'abonnement au magazine Femme Actuelle qui vous convient grâce aux multiples formules proposées : choix du format, de la durée, du prix - Femmeactuelle.fr" %}
    {% set metaTitle = "Abonnement magazine Femme Actuelle pas cher" %}
    {% set metaTitleSuffix = " - Femmeactuelle.fr" %}
    {% set title = "On reconnait tout de suite une Femme Actuelle" %}
    {% set buttonLink = "https://prismashop.commander1.com/c3/?tcs=2103&chn=sites-editos&src=fac-fr&cmp=header-abo&url=https://www.prismashop.fr/edito-femmeactuelle?code=SIEFACHEAD&utm_source=fac-fr&utm_medium=sites-editos&utm_campaign=header-abo" %}
    {% set dataUrl = "https://cdn-prismashop.prismamediadigital.com/pcatalog/edito/flow/category/640" %}
    {% set dfpSlug = "Femme-Actuelle" %}
{% endif %}

{% block metaDescriptionContent %}{{ metaDescription }}{% endblock %}
{% block metaTitleContent %}{{ metaTitle }}{% endblock %}
{% block metaTitleContentSuffix %}{{ metaTitleSuffix }}{% endblock %}

{% block coreads_prefooter %}{% endblock %}
{% block coreads_megaban %}{% endblock %}

{% block defaultContainer %}
    <div class="subscription {{ trigram }}Brand">
        <div class="subscription-banner" style="background-image: url({{ asset("assets/fac/images/banner-" ~ trigram ~ ".jpg") }})">
            <div class="subscription-logos">
                <div class="subscription-logo isBrandLogo">
                    {% include '@Front/src/views/pages/subscription/assets/images/logo-' ~ trigram ~ '.svg' %}
                </div>
                <div>&</div>
                <div class="subscription-logo">
                    {% include '@Front/src/views/pages/subscription/assets/images/logo-prismashop.svg' %}
                </div>
            </div>
            <h1 class="subscription-title">{{ title }}</h1>
            <a href="{{ buttonLink }}" class="subscription-button">Je découvre le magazine</a>
        </div>
        <div id="ps-subscription"></div>
    </div>
{% endblock %}

{% block scriptBottoms %}
    {{ parent() }}
    <script src="{{ scds_url('ps-subscription/public/starter.js') }}"
        charset="utf-8"
        data-psSubscription
        data-js="{{ scds_url('ps-subscription/public/bundle.js') }}"
        data-css="{{ scds_url('ps-subscription/public/css/' ~ trigram ~ '.css') }}"
        data-brand="{{ trigram }}"
        data-selectid="ps-subscription"
        data-dataurl="{{ dataUrl }}"
        data-json="{{ scds_url('ps-subscription/public/data/' ~ trigram ~ '.json') }}"
        data-dfp-id="1524756713626-0"
        data-dfp-slug="{{ dfpSlug }}"></script>
    </script>
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ app.request.getSchemeAndHttpHost() ~ asset('subscription.css') }}">
    {% if brand == 'FAS' %}
        <style>
            body {
                background: #eae8e0;
            }
        </style>
    {% endif %}
{% endblock %}
