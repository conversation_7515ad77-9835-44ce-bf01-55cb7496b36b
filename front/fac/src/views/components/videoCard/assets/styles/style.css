.videoCard {
	position: relative;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	height: 200px;
	padding: 10px;
	overflow: hidden;

	&-background {
		position: absolute;
		inset: 0;
		transition: transform 1s ease;
		transform: scale(1, 1);
		background-size: cover;
	}

	&:hover &-background {
		transform: scale(2, 2);
	}

	&-sticker {
		position: relative;
		width: 80px;
		height: 80px;
		margin: var(--margin-mdp) auto;
		border: 1px solid var(--color-borderLight);
		box-shadow: 0 2px 4px 0 rgb(0 0 0 / 50%);
		border-radius: 50%;
		background-size: 200%;
	}

	&-link {
		position: relative;
		display: block;
		height: 40px;
		text-align: center;
		font-size: var(--fontSize-xs);
		font-variation-settings: 'wght' var(--fontWeight-semibold);
		color: var(--color-white);

		&:any-link {
			color: var(--color-white);
			text-decoration: none;
		}
	}
}
