{% set horoscopeImgDefault = asset('images/horoscope-widget.png') %}
{% set horoscopeImgArticle = asset('images/horoscope-widget-article.png') %}
{% set classes = classes is defined ? classes : [] %}
{% set isArticle = article|default(null) ? article.type|default('')|lower == 'article' : '' %}
<div class="horoscopeWidget {{ classes|join(' ') }}">
    {% include '@Front/src/views/components/headingTitle/headingTitle.html.twig' with {
        text: "Votre horoscope",
        type: "underline-title"
    } %}

    <div class="horoscopeWidget-body ">
        <figure class="horoscopeWidget-figure">
                <img src="{{ isArticle ? horoscopeImgArticle : horoscopeImgDefault }}"
                    class="lazyload"
                    {{ isArticle ? 'width="222" height="169" ' : 'width="333" height="90"' }}
                    alt="Votre horoscope"
                />
        </figure>
        <div class="horoscopeWidget-wrapper">
        <p class="horoscopeWidget-title">Grande joie ou petits tracas, à quoi ressembleront vos journées ?</p>
        <p class="horoscopeWidget-text">Recevez vos prévisions quotidiennes par e-mail</p>

        {% set followId = '15d732ed-7177-4ac0-b31f-895f157f122f' %}
        {% set followName = article.slug|default('') %}
        {% set followHref = '/page/tag/horoscope-quotidien' %}
        {% set bookmarkType = 'tag' %}
        {% set followSlug = 'horoscope-quotidien' %}
        {% set signup = (isArticle == 'tag' ? 'FAC_BOOKMARK-FOLLOW-TAG_BOUTON-ARTICLE_SITE-FAC_TAG-HOROSCOPE-QUOTIDIEN' : 'FAC_BOOKMARK-FOLLOW-TAG_WIDGET-HOROSCOPE_SITE-FAC_TAG-HOROSCOPE-QUOTIDIEN') %}

        {% include '@Front/src/views/components/button/button.html.twig' with {
                    classes: ['horoscopeWidget-followButton', 'button-rounded', 'backgroundDark', 'isCentered'],
                    text: 'Je m\'abonne',
                    altText: 'Je me désabonne',
                    size: 'small',
                    attributes: [
                        'data-id="' ~ followId ~ '"',
                        'data-slug="' ~ followName ~ '"',
                        'data-article-path="' ~ followHref ~ '"',
                        'data-subscribe=""',
                        'data-block="button"',
                        'data-manual-auth',
                        'data-bookmark-button=""',
                        'data-bookmark-type="follow:' ~ bookmarkType ~ '"',
                        'data-bookmark-status="false"',
                        'data-pmc-signup-content="' ~ bookmarkType ~ '-' ~ followSlug ~ '"',
                        'data-pmc-service-tag="' ~ followSlug ~ '"',
                        'data-pmc-signup-service="' ~ signup ~ '"',
                        'data-tracking-v4="trigger:click//event:button_click//button_name:blindSpot"'
                    ],
        } %}
        </div>
    </div>
</div>
