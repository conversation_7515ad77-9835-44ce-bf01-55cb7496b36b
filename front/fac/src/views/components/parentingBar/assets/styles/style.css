.parentingBar {
	display: flex;
	flex-direction: column;
	width: 100%;
	margin: var(--margin-sm) 0;
	padding: 0;
	list-style: none;
	border: 1px solid var(--color-gamma);
	background-color: var(--color-background);

	@media (--media-min-lg-tablet) {
		flex-direction: row;
	}

	& > * {
		flex-grow: 1;
		border-top: 1px solid var(--color-gamma);

		@media (--media-min-lg-tablet) {
			border-top: none;
			border-left: 1px solid var(--color-gamma);
		}

		&:first-child {
			border: none;
		}
	}

	&-element {
		display: flex;
		align-items: center;
		height: 60px;
		justify-content: center;
		font-size: var(--fontSize-lg);
		font-variation-settings: 'wght' var(--fontWeight-medium);
		color: inherit;
		text-transform: uppercase;

		&:hover {
			text-decoration: none;
		}

		& > svg {
			display: inline-block;
			vertical-align: middle;
			width: 24px;
			margin-right: var(--margin-sm);
			fill: var(--color-main);
		}

		&.withCircleIcon > svg {
			width: 30px;
			height: 30px;
			padding: 5px;
			border: 1px solid var(--color-main);
			border-radius: 50%;
		}
	}
}
