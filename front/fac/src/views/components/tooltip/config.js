import './assets/styles/style'
import Tooltip from './assets/scripts/script'

// Initialize tooltips
for (const element of document.querySelectorAll('[data-tooltip][data-tooltip-autoinit]')) {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const tooltip = new Tooltip({
		element,
		anchor: element.closest('[data-tooltip-anchor]')
	})
}

// Initialize bookmark tooltip helper
if (!window.localStorage.bookmarkHelperShown) {
	for (const bookmarkHelperElement of document.querySelectorAll(
		'[data-tooltip][data-tooltip-bookmarkhelper]'
	)) {
		const bookmarkHelper = new Tooltip({
			element: bookmarkHelperElement,
			anchor: bookmarkHelperElement.closest('[data-tooltip-anchor]'),
			onHide: () => {
				window.localStorage.setItem('bookmarkHelperShown', true)
			}
		})
		bookmarkHelper.show()
	}
}
