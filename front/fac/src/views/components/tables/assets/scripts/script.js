function initTables() {
	const isTableLeft = [...document.querySelectorAll('table.left')]

	isTableLeft.forEach((e) => {
		const th = [...e.getElementsByTagName('th')]
		const td = [...e.querySelectorAll('tbody tr td:first-child')]
		const scrollX = e.closest('.scrollX')
		const thHeight = th[0].offsetHeight + 'px'
		const thWidth = th[0].offsetWidth + 'px'
		const maxWidth = thWidth

		if (e.scrollWidth > e.parentElement.clientWidth) {
			th.forEach((e) => {
				e.style.height = thHeight
				e.style.width = thWidth
			})

			th[0].style.position = 'absolute'
			th[0].style.left = '0'

			td.forEach((e) => {
				const tdHeight = e.offsetHeight + 'px'
				e.style.width = maxWidth
				e.style.height = tdHeight
				e.style.position = 'absolute'
				e.style.left = '0'
			})

			scrollX.style.marginLeft = maxWidth
			scrollX.classList.add('fxShadow')
		} // endif
	})
}

initTables()
