{% extends '@Front/pages/article/components/abtests/shared/templates/combinatoryLayout.html.twig' %}
{% from '@CommonFront/macros/svg/views/svg.html.twig' import getSvgTag %}

{% set campaign = campaign|default('fac_rr_variation_1') %}

{% block content %}
    {% set file %}
        {{ include("@Front/pages/article/components/abtests/weeklyFiles/data/#{testCategory}.json", with_context = false) }}
    {% endset %}
    {% set file = file|json_decode() %}

    <div class="weeklyFiles">
        <div class="weeklyFiles-image">
            {{ include('@CommonFront/components/image/views/image.html.twig', {
                image: {
                    attributes: {
                        'alt': file.title,
                    },
                    sizes: {
                        'mobile': {w: 360, h: 213, r: '16x9'}
                    },
                    url: absolute_url(asset(file.image))
                }
            }, with_context = false) }}
            <div class="weeklyFiles-tag">{{ getSvgTag("weeklyFilesArrow") }} Dossier de la semaine</div>
        </div>
        <div class="weeklyFiles-content">
            <div class="weeklyFiles-text">{{ file.title }}</div>
            <div class="weeklyFiles-cta">
                <a href="{{ file.url|replace({'{campaign}': campaign}) }}" class="weeklyFiles-ctaLink">Lire maintenant</a>
            </div>
        </div>
    </div>
{% endblock %}
