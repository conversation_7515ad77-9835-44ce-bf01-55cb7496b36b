.weeklyFiles {
	--white: var(--color-white, #fff);
	--black: #102325;
	--red: #FE6C69;

	display: flex;
	overflow: hidden;
	flex-direction: column;
	border-radius: 20px;
	line-height: 1.25;
	margin: 20px auto;

	&-image {
		position: relative;
		display: block;
		aspect-ratio: 16 / 9;

		.image {
			aspect-ratio: 16 / 9;
		}

		img {
			aspect-ratio: 16 / 9;
			object-fit: cover;
			position: absolute;
			inset: 0;
			width: 100%;
			height: 100%;
		}
	}

	&-tag {
		position: absolute;
		top: 0;
		right: 0;
		display: flex;
		align-items: center;
		padding: 10px;
		border-radius: 0 20px;
		background-color: var(--white);
		color: var(--black);
		font-size: 16px;
		font-weight: 600;
		gap: 10px;
		letter-spacing: 0.5px;

		svg {
			width: 25px;
			height: 25px;
			fill: none;
		}

		&::before,
		&::after {
			position: absolute;
			width: 40px;
			height: 40px;
			border-radius: 50%;
			box-shadow: 15px -15px 0 0 var(--white);
			content: "";
		}

		&::before {
			top: 0;
			left: -40px;
		}

		&::after {
			top: 45px;
			right: 0;
		}
	}

	&-content {
		display: flex;
		flex-direction: column;
		padding: 10px 15px 15px;
		background-color: var(--red);
		gap: 15px;
		z-index: 1;
	}

	&-text {
		display: block;
		color: var(--white);
		font-size: 22px;
		font-weight: 600;
		text-align: center;

		&.active {
			font-size: 20px;
			font-weight: 400;
		}
	}

	&-cta {
		display: block;
		text-align: center;
	}

	&-ctaLink {
		display: inline-block;
		padding: 10px 25px;
		border: none;
		border-radius: 20px;
		background-color: var(--black);
		color: var(--white);
		font-size: 18px;
		font-weight: 600;
		cursor: pointer;

		&:hover {
			color: var(--white);
			text-decoration: none;
		}
	}
}
