{% extends '@Front/pages/article/components/abtests/shared/templates/combinatoryLayout.html.twig' %}

{% set campaign = campaign|default('fac_rr_variation_1') %}

{% block content %}
    {% if testCategory == 'index-glycemique' %}
        {% set categoryId = 'a35578ae-d382-4f43-9fa3-e41d081b5d31' %}
        {% set picto = asset('images/picto-cuisine.png') %}
    {% elseif testCategory == 'orthographe' %}
        {% set categoryId = 'ab45aac2-4ce6-4a69-879a-7e06b3472d7e' %}
        {% set picto = asset('images/picto-check.png') %}
    {% elseif testCategory == 'nettoyage' %}
        {% set categoryId = '3b0e39e7-6393-4ab6-86ea-54e584c806ae' %}
        {% set picto = asset('images/picto-check.png') %}
    {% elseif testCategory == 'cuisine-italienne' %}
        {% set categoryId = '8cfdb8ed-a70e-479e-a02a-6858ee7f67e1' %}
        {% set picto = asset('images/picto-cuisine.png') %}
    {% elseif testCategory == 'prenoms' %}
        {% set categoryId = '6fc20350-0c5f-4ce6-8b03-724dd859091a' %}
        {% set picto = asset('images/picto-coeur.png') %}
    {% elseif testCategory == 'signe-astro' %}
        {% set categoryId = '30e32c20-3d62-4bab-82dc-5404055a4123' %}
        {% set picto = asset('images/picto-coeur.png') %}
    {% elseif testCategory == 'tendances' %}
        {% set categoryId = 'f3cf9e98-932d-4175-a4d7-4fbd07d55e46' %}
        {% set picto = asset('images/picto-mode.png') %}
    {% elseif testCategory == 'forme' %}
        {% set categoryId = '017e2103-9cd8-4391-8de4-807d29e8bfc5' %}
        {% set picto = asset('images/picto-sante.png') %}
    {% endif %}

    <h2 class="underlineTitle">
        <span>Coups de ❤️​ Femme Actuelle</span>
    </h2>
    <div class="favorites" data-categoryId="{{ categoryId }}" data-campaign="{{ campaign }}" data-picto="{{ picto }}">
        <ul class="favorites-list"></ul>
    </div>
{% endblock %}
