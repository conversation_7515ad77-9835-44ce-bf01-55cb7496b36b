[class*=' ratio'],
[class^='ratio'] {
	position: relative;

	&::before {
		position: absolute;
		display: block;
		width: 100%;
		height: 100%;
		background: url('../svgs/placeholder.svg?isImage');
		background-position: center;
		background-repeat: no-repeat;
		background-size: 60% auto;
		content: '';
	}
}

.image {
	position: relative;
	overflow: hidden;

	.apply-ratio,
	object ~ div,
	object,
	embed,
	video,
	img,
	iframe {
		position: absolute;
		inset: 0;
		width: 100%;
		height: 100%;
	}
}

.ratio-1x1 {
	aspect-ratio: 1 / 1;
}

.ratio-3x4 {
	aspect-ratio: 3 / 4;
}

.ratio-4x3 {
	aspect-ratio: 4 / 3;
}

.ratio-16x9 {
	aspect-ratio: 16 / 9;
}

@media (--from-tablet) {
	.ratio-1x1Tablet {
		aspect-ratio: 1 / 1;
	}

	.ratio-3x4Tablet {
		aspect-ratio: 3 / 4;
	}

	.ratio-16x9Tablet {
		aspect-ratio: 16 / 9;
	}
}

@media (--from-desktop) {
	.ratio-1x1Desktop {
		aspect-ratio: 1 / 1;
	}

	.ratio-3x4Desktop {
		aspect-ratio: 3 / 4;
	}

	.ratio-4x3Destop {
		aspect-ratio: 4 / 3;
	}

	.ratio-16x9Desktop {
		aspect-ratio: 16 / 9;
	}
}
