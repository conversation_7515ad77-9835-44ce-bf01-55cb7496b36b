import scrollTop from 'commonSrc/assets/scripts/utils/scroll-top.ts'

/**
 * HeaderNavbar
 * @module Shared/header/headerNavbar
 */
export default class HeaderNavbar {
	/**
	 * Creates an instance of the HeaderNavbar manager
	 * @param {object} options - Optionnal parameters
	 * @param {HTMLElement} options.header - A DOM node for the header
	 * @param {HTMLElement} options.headerNavbar - A DOM node for the header navbar
	 * @param {number} options.delta - The minimum amount of scroll before handling it
	 * @param {number} options.timer - The time between scroll checks for the navbar positionning
	 */
	constructor({
		header = document.querySelector('#header'),
		headerTopbar = document.querySelector('.header-topbar'),
		headerNavbar = document.querySelector('.header-navbar'),
		delta = 5,
		timer = 250
	} = {}) {
		this.header = header
		this.headerTopbar = headerTopbar
		this.headerNavbar = headerNavbar
		this.delta = delta
		this.timer = timer

		this.didScroll = false
		this.lastScrollTop = 0
		this.currentScroll = 0
		this.headerOffsetHeight = 0
		this.headerTopbarOffsetHeight = 0
		this.headerNavbarOffsetHeight = 0
		this.viewportHeight = 0

		this.player = document.querySelector('.playerDailymotion')
		this.playerPosTop = 0
		this.playerHeight = 0

		this.onScroll = this.onScroll.bind(this)
		this.updateWindowSize = this.updateWindowSize.bind(this)
	}

	/**
	 * Init the Header and Navbar position management
	 */
	init() {
		this.setClassProperties()
		this.updateWindowSize()
		this.addStickyToNavbar()

		// Add all event listeners
		this.addEvents()
		this.checkHeaderNavbarStickyness()
	}

	/**
	 * Set class properties
	 */
	setClassProperties() {
		this.headerOffsetHeight = this.header.offsetHeight

		this.headerTopbarOffsetHeight = this.headerTopbar.offsetHeight
		this.headerNavbarOffsetHeight = this.headerNavbar.offsetHeight

		this.playerPosTop = this.player?.getBoundingClientRect().top || 0
		this.playerHeight = this.player?.offsetHeight || 0
	}

	/**
	 * Add events on scroll and resize events
	 */
	addEvents() {
		window.addEventListener('scroll', this.onScroll, {
			passive: true
		})
		window.addEventListener('resize', this.updateWindowSize, {
			passive: true
		})
	}

	/**
	 * Handles scroll events for determining which action to take
	 * based on current scroll value
	 */
	onScroll() {
		this.didScroll = true
		this.currentScroll = scrollTop()

		if (this.isScrollBeforeHeaderHeight()) {
			this.handleScrollBeforeHeaderHeight()
		} else {
			this.handleScrollAfterHeaderHeight()
		}
	}

	/**
	 * Updates Class values on a resize event
	 */
	updateWindowSize() {
		this.windowInnerHeight = window.innerHeight
		this.bodyClientHeight = document.body.clientHeight
		this.viewportHeight = window.innerHeight
	}

	/**
	 * Sets an interval for checking if user has scrolled
	 * If it's the case, handles that scroll
	 */
	checkHeaderNavbarStickyness() {
		setInterval(() => {
			if (this.didScroll) {
				this.handleHeaderNavbarStickyness()
				this.didScroll = false
			}
		}, this.timer)
	}

	/**
	 * Handles scroll event to manage Navbar positionning
	 */
	handleHeaderNavbarStickyness() {
		this.currentScroll = scrollTop()

		// Make sure user scroll more than delta
		if (this.hasScrolledLessThanDelta()) {
			return
		}

		if (this.isScrollAfterNavbarHeight()) {
			this.checkScrollForNavbar()
		}

		this.lastScrollTop = this.currentScroll
	}

	/**
	 * Checks if user has scrolled less than delta
	 * @returns {boolean} Has it scrolled less than delta?
	 */
	hasScrolledLessThanDelta() {
		return Math.abs(this.lastScrollTop - this.currentScroll) <= this.delta
	}

	/**
	 * Checks if the user has scrolled after Navbar height
	 * @returns {boolean} Is scroll after navbar height?
	 */
	isScrollAfterNavbarHeight() {
		return this.currentScroll > this.headerNavbarOffsetHeight
	}

	/**
	 * Handles different actions depending of scroll directions
	 */
	checkScrollForNavbar() {
		if (this.isScrollDown()) {
			this.removeStickyFromNavbar()
		} else {
			// Scroll Up
			this.addStickyToNavbar()
		}
	}

	/**
	 * Checks if the user has scrolled before header height
	 * @returns {boolean} Is scroll before header height?
	 */
	isScrollBeforeHeaderHeight() {
		return this.currentScroll <= this.headerOffsetHeight
	}

	/**
	 * If scroll is before header height, removes sticky classes and adds transform property to Navbar
	 */
	handleScrollBeforeHeaderHeight() {
		if (this.isScrollOnTopOfPage() && !document.body.classList.contains('playerIsSticky')) {
			this.header.classList.remove('sticky')
			this.header.classList.remove('sticky-active')
			this.headerNavbar.style.transform = `translate3d(0, ${this.currentScroll * -1}px, 0)`
		}
	}

	/**
	 * Checks if the user has scrolled to top or between top and header height
	 * @returns {boolean} Is scroll on top of page?
	 */
	isScrollOnTopOfPage() {
		return this.currentScroll === 0 || !this.header.classList.contains('sticky-active')
	}

	/**
	 * Handles scroll after Header height, adds sticky class and removes transform property on Navbar
	 */
	handleScrollAfterHeaderHeight() {
		this.header.classList.add('sticky')
		this.headerNavbar.style.removeProperty('transform')
	}

	/**
	 * Checks if the user has scrolled down
	 * @returns {boolean} Is scroll down?
	 */
	isScrollDown() {
		return this.currentScroll > this.lastScrollTop && this.isScrollAfterNavbarHeight()
	}

	/**
	 * Removes sticky class for Navbar
	 */
	removeStickyFromNavbar() {
		this.header.classList.remove('sticky-active')
	}

	/**
	 * Adds sticky class for navbar if needed
	 */
	addStickyToNavbar() {
		if (this.isStickyNeededForNavbar()) {
			this.header.classList.add('sticky-active')
			this.header.classList.add('sticky')
		}
	}

	/**
	 * Checks if sticky is needed for the Navbar
	 * If current page content is smaller than window height, returns false
	 * @returns {boolean} Is sticky needed for navbar?
	 */
	isStickyNeededForNavbar() {
		const isContentHigherThanWindowHeight = this.bodyClientHeight > this.windowInnerHeight
		const isCurrentScrollHigherThanTopbar = this.currentScroll > this.headerTopbarOffsetHeight
		const playerIsSticky = this.isPlayerOutOfViewport()

		return (
			(isContentHigherThanWindowHeight && isCurrentScrollHigherThanTopbar) || playerIsSticky
		)
	}

	/**
	 * check if player container is out of the viewport
	 * @returns {boolean} is player out of viewport
	 */
	isPlayerOutOfViewport() {
		const playerBottomPos = this.playerPosTop + this.playerHeight

		return playerBottomPos + this.currentScroll > this.viewportHeight
	}
}
