'use strict'

const path = require('path')
const pathSrc = path.resolve(__dirname, '../src')

const brands = Object.freeze(['SHGFAC', 'SHGVO<PERSON>', 'SHGGEO', 'SHG<PERSON><PERSON>', 'SHGCAM', 'SHGCAC'])
const brandsConfig = {}

for (const brand of brands) {
	const brandLower = brand.toLowerCase()
	brandsConfig[brandLower] = [`${pathSrc}/brands/${brandLower}/config`]
}

module.exports = {
	entry: {
		brands: brandsConfig,
		project: {
			home: [`${pathSrc}/views/pages/home/<USER>
			category: [`${pathSrc}/views/pages/category/config`],
			article: [`${pathSrc}/views/pages/article/config`]
		}
	}
}
