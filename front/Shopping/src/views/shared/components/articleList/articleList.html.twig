{% set articles = articles|default([]) %}
{% set articleTitleTag = articlesTitleTag|default('h3') %}
{% set classes = classes|default([]) %}
{% set cardClasses = cardClasses|default([]) %}
{% set cardSize = cardSize|default('medium') %}

<section class="articleList {{ classes|join(' ') }}" data-block="articleList">
    <div class="articleList-wrapper">
        {% for article in articles %}
            {% include '@Front/src/views/shared/components/articleCard/articleCard.html.twig' with {
                article,
                articleTitleTag,
                size: cardSize,
                classes: cardClasses|merge(['articleList-item'])
            } only %}
        {% endfor %}
    </div>
    {% block pagination %}{% endblock %}
</section>
