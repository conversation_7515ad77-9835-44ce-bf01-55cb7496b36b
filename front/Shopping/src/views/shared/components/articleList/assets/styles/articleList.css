.articleList {
	padding: var(--space-10) 0;
	margin-top: var(--space-15);

	&-wrapper {
		display: grid;
		grid-template-columns: minmax(0, 1fr);
		grid-gap: var(--space-10);

		@media (--media-min-phone) {
			grid-template-columns: repeat(2, 1fr);
		}
	}

	&.isTight &-wrapper {
		@media (--media-min-tablet) {
			grid-template-columns: repeat(3, 1fr);
		}
	}

	&.isTighter &-wrapper {
		@media (--media-min-phone) {
			grid-template-columns: repeat(3, 1fr);
		}

		@media (--media-min-tablet) {
			grid-template-columns: repeat(4, 1fr);
		}
	}
}
