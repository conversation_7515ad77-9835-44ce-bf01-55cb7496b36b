{% set article = article|default(null) %}
{% set articleTitleTag = articleTitleTag|default('h3') %}
{% set classes = classes|default([]) %}
{% set attributes = attributes|default([]) %}
{% set dataBlock = dataBlock|default('articleCard') %}
{% set withoutImage = withoutImage|default(false) %}
{% set size = size|default('medium') %}
{% set lazyloadImage = lazyloadImage ?? true %}
{% set urlParameter = urlParameter|default(null) %}
{% set urlArticle = article.path ~ urlParameter %}

{% set pictureSizes %}
    {% include '@Front/src/views/shared/components/articleCard/pictureSizes.json' %}
{% endset %}
{% set pictureSizes = pictureSizes|json_decode() %}

<article class="articleCard {{ size }} {{ not withoutImage and article.hasPhotos ? 'hasImage' : '' }} {{ classes|join(' ') }}" data-wide-target data-block="{{ dataBlock }}" {{ attributes|join(' ')|raw }}>
    {% if not withoutImage and pictureSizes[size] is defined and pictureSizes[size] is not empty %}
        {% include '@Front/src/views/shared/components/articleCardImage/articleCardImage.html.twig' with {
            article,
            pictureSizes: pictureSizes[size],
            lazyload: lazyloadImage
        } only %}
    {% endif %}

    <div class="articleCard-body">
        <{{ articleTitleTag }} class="articleCard-title">
            <a class="articleCard-link" data-wide href="{{ urlArticle }}">{{ article.title }}</a>
        </{{ articleTitleTag }}>
    </div>
</article>
