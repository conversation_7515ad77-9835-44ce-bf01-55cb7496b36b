.articleCard {
	display: flex;
	justify-content: center;
	text-align: center;
	align-items: center;
	min-height: 80px;
	background-color: var(--c-white);

	@media (--media-min-tablet) {
		&:hover {
			box-shadow: 1px 2px 6px 0 var(-c-shadow);
		}
	}

	& &-body {
		display: flex;
		flex-direction: column;
		padding: 10px;
		background-color: var(--c-white);
		color: var(--c-text);
	}

	& &-title {
		flex-grow: 1;
		margin: 0;
		font-size: var(--fz-18);
		line-height: 1.2;
		letter-spacing: -0.5px;
		font-variation-settings: 'wght' var(--fw-400);
	}

	&:hover &-title {
		a {
			text-decoration: underline;
		}
	}

	& &-link {
		color: inherit;
		font-variation-settings: 'wght' var(--fw-400);

		&:hover,
		&:focus,
		&:active,
		&:visited {
			color: inherit;
		}
	}

	&.smaller {
		flex-direction: row;
		border: 1px solid var(--c-border);

		@media (--media-min-desktop) {
			flex-direction: column;
		}
	}

	&.smaller &-imageWrapper {
		width: 50%;
		flex-shrink: 0;
		padding: 10px;

		@media (--media-min-desktop) {
			width: 100%;
			padding: 10px 10px 0;
		}
	}

	&.smaller &-body {
		width: 50%;
		flex-shrink: 0;

		@media (--media-min-desktop) {
			width: 100%;
		}
	}

	&.small {
		flex-direction: row;
		border: 1px solid var(--c-border);

		@media (--media-min-phone) {
			flex-direction: column;
		}
	}

	&.small &-imageWrapper {
		width: 50%;
		flex-shrink: 0;
		padding: 10px;

		@media (--media-min-phone) {
			width: 100%;
			padding: 0;
		}
	}

	&.small &-body {
		width: 50%;
		height: 100%;
		flex-shrink: 0;
		flex-grow: 1;

		@media (--media-min-phone) {
			width: 100%;
			height: auto;
		}
	}

	&.small &-title {
		order: 1;
		text-align: left;

		@media (--media-min-phone) {
			order: initial;
			text-align: inherit;
		}
	}

	&.medium {
		flex-direction: column;
	}

	&.medium &-body {
		width: 100%;
		flex-grow: 1;
		border: 1px solid var(--c-border);

		@media (--media-min-desktop) {
			padding: var(--space-15);
		}
	}

	&.medium &-title {
		@media (--media-min-desktop) {
			font-size: 22px;
		}
	}

	&.large {
		flex-direction: column;
		border: 10px solid var(--c-almostWhite);
	}

	&.large &-body {
		width: 100%;
		flex-grow: 1;
		border: 1px solid var(--c-border);

		@media (--media-min-desktop) {
			padding: 20px;
		}
	}

	&.large &-title {
		@media (--media-min-desktop) {
			line-height: 1.25;
			font-size: var(--fz-28);
		}
	}

	&.horizontal {
		flex-direction: row;
		border: 1px solid var(--c-border);
	}

	&.horizontal &-imageWrapper {
		width: 50%;
		flex-shrink: 0;
		padding: 10px;
	}

	&.horizontal &-body {
		width: 50%;
		flex-shrink: 0;
		text-align: left;
	}

	&.horizontal &-title {
		max-height: 86px;
		line-height: 1.2;
		font-size: var(--fz-14);
		text-overflow: ellipsis;
		word-wrap: break-word;
		overflow: hidden;
	}

	&.horizontal &-rating {
		display: none;
	}

	&.horizontal &-recipeInfo {
		display: none;
	}

	&.withBottomMargin {
		margin-bottom: var(--space-15);
	}

	&.hasSmallText &-title {
		@media (--media-min-desktop) {
			line-height: 2;
			font-size: var(--fz-16);
		}
	}
}
