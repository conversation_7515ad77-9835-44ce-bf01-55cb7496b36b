import pushInDatalayer from 'shared/assets/scripts/utils/push-in-datalayer'
import jsCookie from 'js-cookie'

let renderDatalayer = {}
window.dataLayer.map((dataLayerItem) => {
	renderDatalayer = Object.assign(renderDatalayer, dataLayerItem)
})

let contentData = [
	{ auth_id: jsCookie.get('authId') },
	{ client_id: jsCookie.get('_ga')?.slice(6) },
	{ user_agent: navigator.userAgent },
	{ page_title: renderDatalayer.name },
	{ page_category: renderDatalayer.pageCategory },
	{ page_type: renderDatalayer.pageType },
	{ path: renderDatalayer.path },
	{ keywords: renderDatalayer.keywords.join(',') },
	{ env: renderDatalayer.env }
]

if (renderDatalayer.pageType.toLowerCase() === 'newsarticle') {
	const articleContentData = [
		{ content_object_id: renderDatalayer.contentObjectId },
		{ content_provider: renderDatalayer.source }, // Je ne sais pas quoi mettre ici, la valeur 6 médias ne me dit rien
		{ author_name: renderDatalayer.author },
		{ author_group: renderDatalayer.author },
		{ publication_date: new Date(renderDatalayer.publishedAt).toLocaleDateString('fr-FR') },
		{ one_qualifier: renderDatalayer?.qualifiers?.join(',') ?? '' }
	]
	contentData = [...contentData, ...articleContentData]
}

pushInDatalayer(contentData)
