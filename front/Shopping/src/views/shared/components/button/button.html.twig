{% set text = text|default(null) %}
{% set altText = altText|default(null) %}
{% set icon = icon|default(null) %}
{% set iconClasses = iconClasses|default([]) %}
{% set href = href|default(null) %}
{% set classes = classes|default([]) %}
{% set size = size|default('normal') %}
{% set fontSize = fontSize|default('normalFontSize') %}
{% set attributes = attributes|default([]) %}
{% set linkAttributes = linkAttributes|default([]) %}
{% set label = card.label|default(null) %}
{% set classes = classes|merge([size, fontSize]) %}
{% set tracking = tracking|default(null) %}
{% set signupService = signupService|default(null) %}

{% if (href) %}
    <a class="button button-link {{ classes|join(' ') }}" {{ attributes|join(' ')|raw }} data-block="button"
      data-track
      {% set labelSearch = 'zone[' %}
      {% if label and labelSearch in label %}
        {% set labelFiltered = label|split(labelSearch)[1]|split(']')[0] %}
          data-track-category="autopromo"
          data-track-action="click_{{ labelFiltered }}"
          data-track-label="{{ text }}_{{ href }}"
      {% endif %}
      href="{{ href }}" {{ linkAttributes|join(' ')|raw }}
      data-link-tracking="{{ tracking }}"
        {% if signupService is not null %}
            data-signupservice="{{ signupService }}"
        {% endif %}
      data-wide>{{ text }}</a>
    {% else %}
      <button class="button {{ classes|join(' ') }}" {{ attributes|join(' ')|raw }} data-block="button">
        <span>{{ text }}</span>
        {% if altText %}
            <span>{{ altText }}</span>
        {% endif %}
      </button>
{% endif %}
