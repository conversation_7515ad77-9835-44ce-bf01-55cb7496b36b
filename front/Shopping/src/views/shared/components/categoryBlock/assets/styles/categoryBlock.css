.categoryBlock {
	padding: var(--space-15) 0;
	margin: 0 calc(-1 * var(--space-15));

	@media (--media-min-tablet) {
		margin: 0;
	}

	&-title {
		margin: var(--space-10);
		text-align: center;
		font-family: var(--ff-heading);
		font-size: var(--fz-22);
		font-variation-settings: 'wght' var(--fw-700);

		@media (--media-min-desktop) {
			text-align: left;
			font-size: var(--fz-32);
		}
	}

	&-wrapper {
		overflow-x: auto;
		padding: var(--space-10) 0;
		margin-bottom: var(--space-10);
		-webkit-overflow-scrolling: touch;
	}

	&-list {
		display: inline-flex;
		justify-content: space-evenly;
		padding: 0 var(--space-5);

		@media (--media-min-tablet) {
			padding: 0;
		}

		@media (--media-min-desktop) {
			display: flex;
			justify-content: space-between;
			flex-wrap: wrap;
		}
	}

	&-card {
		--_gap: var(--space-5);

		width: 250px;
		margin: 0 var(--_gap);
		flex-shrink: 0;

		@media (--media-min-tablet) {
			width: 170px;
		}

		@media (--media-min-desktop) {
			width: calc(50% - var(--_gap));
			margin: var(--_gap) 0;
		}
	}

	&-buttonWrapper {
		display: flex;
		justify-content: center;
		padding: 0 20px;
	}

	&-button {
		background-color: var(--c-white);
		color: var(--c-black);
	}
}
