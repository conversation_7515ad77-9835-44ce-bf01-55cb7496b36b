{% import "@Front/src/views/shared/macros/svg.html.twig" as macroSvg %}

{% block header %}
    <header id="site-header" class="site-header" data-block="site-header">

        <div class="site-headerLogo">
            <a href="/" title="Page d'accueil du site">
                {{ macroSvg.getSvgTag('logo', {
                    attributes: {
                        'aria-label': appConfig.brand.brandName,
                        'aria-hidden': 'false'
                    }
                }) }}
            </a>
        </div>
        <nav class="site-headerNav">
            <ul>
                {% for nav in appConfig.brand.headers.nav %}
                <li><a href="{{ nav.href }}">{{ nav.title }}</a></li>
                {% endfor %}
            </ul>
        </nav>
    </header>
{% endblock %}
