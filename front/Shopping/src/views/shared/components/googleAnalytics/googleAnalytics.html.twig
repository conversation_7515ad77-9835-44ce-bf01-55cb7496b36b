{% set ganAccount = appConfig.brand.google_analytics.account %}

{% set article = article|default(null) %}

{% set isHomepage = (app.request.get('_route') == 'app_shopping_home_show') %}
{% set isCategory = (app.request.get('_route') == 'pmd_shopping.category_show') %}
{% set isArticle = (app.request.get('_route') == 'pmd_shopping.article_show') %}

{% if isArticle %}
    {% set dimension13 = 'Article' %}
{% elseif (isHomepage or isCategory) %}
    {% set dimension13 = 'Category' %}
{% else %}
    {% set dimension13 = '-' %}
{% endif %}

{% apply spaceless %}
<script id="gan-init">
    window.ga = window.ga || function() {
        (ga.q = ga.q || []).push(arguments)
    }

    window.consentCheck('allConsentGiven', function() {
        const script = document.createElement('script')
        script.async = 1
        script.src = 'https://www.google-analytics.com/analytics.js'
        document.body.appendChild(script)

        ga('create', "{{ ganAccount }}", {
            'allowAnchor': true,
            'cookieDomain': "{{ appConfig.brand.domain }}",
            'cookieExpires': ********
        });

        window.pmdConsent({ type: 'gaReady' });

        const referrerCookieValue = document.cookie.match("(^|[^;]+)\s*cmp_referrer\s*=\s*([^;]+)")
        if (referrerCookieValue) {
            const referrer = referrerCookieValue.pop()
            window.ga('set', 'referrer', referrer)
            ga('set', 'dimension27', referrer);
            document.cookie = 'cmp_referrer=; expires=Thu, 01 Jan 1970 00:00:00 UTC;'
        } else {
            ga('set', 'dimension27', document.referrer || "undefined");
        }

        function getDimension12(){
            const pageCategory = window.dataLayer[0].pageCategory || '';
            const pageSubCategory = window.dataLayer[0].pageSubCategory || '';
            const pageType =  window.dataLayer[0].pageType || '';
            let value = '-'

            if(pageType === 'NewsArticle'){
                value = pageCategory;
            }
            else if(pageType === 'CollectionPage' && pageSubCategory === 'hp'){
                value = pageCategory + '_home';
            }
            else if(pageType === 'WebSite' && pageCategory === '_homepage'){
                value = 'home_page';
            }

            return value;
        }

        ga('set', 'dimension12', getDimension12());
        ga('set', 'dimension13', "{{ dimension13 }}");

		window.ga('send', 'pageview')
    })
</script>
{% endapply %}
