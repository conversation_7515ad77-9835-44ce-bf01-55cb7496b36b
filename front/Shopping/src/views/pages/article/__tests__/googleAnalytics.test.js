import GoogleAnalytics from '../assets/scripts/googleAnalytics.js'
import consentCheck from 'views/shared/components/consentCheck/assets/scripts/consent-check'

jest.mock('views/shared/components/consentCheck/assets/scripts/consent-check.js')

const getInstance = () => new GoogleAnalytics()

let googleAnalytics
let link

describe('googleAnalytics', () => {
	beforeEach(() => {
		document.body.setAttribute('data-brand-key', 'SHGFAC')
		document.body.innerHTML = `<div data-block="page-article-body" data-block="page-article-body">
            <a href="https://example.com">Example</a>
        </div>
        `

		link = document.querySelector('a')
		googleAnalytics = getInstance()

		Object.defineProperty(window, 'ga', {
			writable: true,
			value: jest.fn()
		})

		Object.defineProperty(window, 'open', {
			writable: true,
			value: jest.fn()
		})
	})

	afterEach(() => {
		document.body.innerHTML = ''
		jest.clearAllMocks()
	})

	describe('constructor', () => {
		it('should call the constructor function', () => {
			expect(googleAnalytics.brandKey).toStrictEqual('SHGFAC')
		})
	})

	describe('init', () => {
		afterEach(() => {
			expect(consentCheck).toHaveBeenCalledWith('allConsentGiven', expect.any(Function))
		})

		describe('with consent', () => {
			beforeEach(() => {
				consentCheck.mockImplementation((type, callback) => callback())
			})

			it('should call the init function', () => {
				googleAnalytics.handleClickLinksInArticleBody = jest.fn()

				googleAnalytics.init()

				expect(googleAnalytics.handleClickLinksInArticleBody).toHaveBeenCalled()
			})

			it('should call the init function without brand name', () => {
				googleAnalytics.checkConsent = jest.fn().mockReturnValue(true)
				googleAnalytics.handleClickLinksInArticleBody = jest.fn()

				googleAnalytics.brandKey = undefined
				googleAnalytics.init()

				expect(googleAnalytics.handleClickLinksInArticleBody).not.toHaveBeenCalled()
			})
		})

		describe('without consent', () => {
			it('should call the init function', () => {
				googleAnalytics.handleClickLinksInArticleBody = jest.fn()
				googleAnalytics.init()
				expect(googleAnalytics.handleClickLinksInArticleBody).not.toHaveBeenCalled()
			})
		})
	})

	describe('handleClickLinksInArticleBody', () => {
		it('should call the handleClickLinksInArticleBody function', () => {
			link.addEventListener = jest.fn()

			googleAnalytics.handleClickLinksInArticleBody()

			expect(link.addEventListener).toHaveBeenCalledWith(
				'click',
				googleAnalytics.trackLinkInArticleBody
			)
		})
	})

	describe('trackLinkInArticleBody', () => {
		const createMockEvent = (options = {}) => ({
			preventDefault: jest.fn(),
			target: {
				getAttribute: jest.fn().mockReturnValue(options.target || '_self'),
				tagName: options.tagName || 'a',
				href: 'https://example.com'
			},
			currentTarget: {
				getAttribute: jest.fn().mockReturnValue(options.target || '_self'),
				tagName: options.tagName || 'a',
				href: 'https://example.com'
			},
			...options
		})

		it('should call the trackLinkInArticleBody function', () => {
			const event = createMockEvent()

			googleAnalytics.trackLinkInArticleBody(event)

			expect(window.ga).toHaveBeenCalledWith(
				'send',
				'event',
				{
					eventAction: 'click',
					eventCategory: 'SHGFAC_SHOPPING',
					eventLabel: `link - ${event.currentTarget.href}`
				},
				{
					hitCallback: expect(window.open).toHaveBeenCalledWith(
						event.currentTarget.href,
						event.currentTarget.getAttribute()
					)
				}
			)
		})
	})

	describe('getGaEventLabelPrefix Function Tests', () => {
		it('should return "image_link" for "img" tagName', () => {
			const result = googleAnalytics.getGaEventLabelPrefix('IMG')

			expect(result).toBe('image_link')
		})

		it('should return "link" for tagName other than "img"', () => {
			const result = googleAnalytics.getGaEventLabelPrefix('A')

			expect(result).toBe('link')
		})
	})
})
