import consentCheck from '../../../../shared/components/consentCheck/assets/scripts/consent-check.js'

export default class GoogleAnalytics {
	constructor() {
		this.brandKey = document.body.getAttribute('data-brand-key')?.toUpperCase()
		this.trackLinkInArticleBody = this.trackLinkInArticleBody.bind(this)
	}

	init() {
		consentCheck('allConsentGiven', () => {
			if (this.brandKey) {
				this.handleClickLinksInArticleBody()
			}
		})
	}

	handleClickLinksInArticleBody() {
		const articleBody = document.querySelector('[data-block="page-article-body"]')
		const linksToTrack = [...articleBody.querySelectorAll('a:not([rel="nofollow"])')]

		linksToTrack.forEach((link) => {
			link.addEventListener('click', this.trackLinkInArticleBody)
		})
	}

	/**
	 * @param { MouseEvent<HTMLLinkElement> } event //
	 */
	trackLinkInArticleBody(event) {
		event.preventDefault()

		const { href } = event.currentTarget
		const target = event.currentTarget.getAttribute('target') || '_self'
		const gaEventLabelPrefix = this.getGaEventLabelPrefix(event.target.tagName)

		window.ga(
			'send',
			'event',
			{
				eventCategory: `${this.brandKey}_SHOPPING`,
				eventAction: 'click',
				eventLabel: `${gaEventLabelPrefix} - ${href}`
			},
			{
				hitCallback: window.open(href, target)
			}
		)
	}

	/**
	 * @param {string} tagName //
	 * @returns {string} //
	 */
	getGaEventLabelPrefix(tagName) {
		return tagName.toLowerCase() === 'img' ? 'image_link' : 'link'
	}
}
