.pageArticle {
	--_space-x: var(--space-10);

	padding-inline: var(--_space-x);

	@media (--media-min-desktop) {
		--_space-x: 0;
	}

	&-title {
		margin: 0;
		padding: 0;
		font-family: var(--ff-heading);
		font-variation-settings: 'wght' var(--fw-700);
		font-size: var(--fz-20);
		line-height: 1.25;

		@media (--media-min-desktop) {
			font-size: var(--fz-32);
		}
	}

	&-meta {
		font-size: var(--fz-14);
		line-height: 1.8;

		&-author {
			color: var(--c-brandAccent);
			font-variation-settings: 'wght' var(--fw-700);
		}

		&-date {
			color: var(--c-midBlueGray);
			font-variation-settings: 'wght' var(--fw-600);
		}
	}

	&-media {
		position: relative;
		margin-inline: calc(var(--_space-x) * -1);

		&-meta {
			position: absolute;
			bottom: 0;
			right: 0;
			padding: var(--space-5);
			text-align: right;
			background: rgb(248 249 250);
			opacity: 0.8;
		}

		&-copyright,
		&-legend {
			font-size: var(--fz-12);
		}
	}

	&-body {
		margin-bottom: var(--space-10);

		a {
			color: var(--c-brandAccent);
		}

		h2,
		h3 {
			font-variation-settings: 'wght' var(--fw-700);
		}

		ul,
		ol {
			list-style: disc;
			padding-left: 1em;
		}
	}

	&-lead {
		font-size: var(--fz-18);
		border-bottom: 1px solid var(--c-border);
		padding-bottom: var(--stack-y);

		@media (--media-min-desktop) {
			font-size: var(--fz-24);
		}
	}
}
