.pageGrid {
	display: flex;
	flex-direction: column;
	margin: var(--gutter-15) auto var(--gutter-30);
	padding: 0;
	max-width: 100%;

	@media (--media-min-tablet) {
		max-width: 1000px;
	}
}

.pageGrid-left,
.pageGrid-right {
	display: flex;
	flex-direction: column;
	width: 100%;
}

/* Desktop */
@media screen and (min-width: 800px) {
	.pageGrid {
		flex-direction: row;
	}

	.pageGrid-left {
		width: auto;
		flex: 1 0 300px;
		max-width: calc(100% - 300px - var(--gutter-15));
	}

	.pageGrid-right {
		width: 300px;
		margin-left: var(--gutter-15);
	}

	.pageGrid-stickyContainer {
		flex-grow: 1;
	}

	.pageGrid-bottomContainer {
		flex-grow: 0;
	}
}
