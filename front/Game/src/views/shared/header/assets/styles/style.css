.profileMenu {
	display: flex;
	transform: translateX(100%);
	background: white;
	height: 100vh;
	top: 0;
	right: 0;
	width: 100%;
	max-width: 190px;
	position: fixed;
	visibility: hidden;
	font-size: var(--fontSize-sm);
	z-index: 10;

	&-wrapper {
		position: absolute;
		inset: 0;
	}

	&-close {
		position: absolute;
		top: 10px;
		left: 10px;
		width: 12px;
		height: 12px;
		background: transparent;
		border: 0;
		cursor: pointer;

		&Icon {
			width: 12px;
			height: 12px;
			appearance: none;
			position: absolute;
		}
	}

	&.isShown {
		transform: translateX(0);
		visibility: visible;
		transition: transform 0.35s cubic-bezier(0.24, 1, 0.32, 1);
	}

	/* PROFILE ZONE */

	&-profile {
		background-color: var(--color-white-lighter);
		padding-top: 18px;
		padding-bottom: 18px;
		display: flex;
		justify-content: center;

		@media (orientation: landscape) {
			padding-top: 8px;
			padding-bottom: 8px;
		}

		&-wrapper {
			display: flex;
			flex-direction: column;
		}

		&-name {
			font-size: var(--fontSize-sm);
			font-variation-settings: 'wght' var(--fontWeight-semibold);
			color: var(--color-black);
			text-align: center;
		}

		&-avatar {
			border-radius: 50%;
			width: 55px;
			height: 55px;
			margin: 0 auto var(--margin-sm);
			box-shadow: 0 5px 7px -4px rgb(0 0 0 / 55%);

			@media (orientation: landscape) {
				width: 35px;
				height: 35px;
			}
		}

		&-defaultAvatar {
			width: 45px;
			height: 55px;
			margin: 0 auto var(--margin-sm);

			@media (orientation: landscape) {
				width: 35px;
				height: 35px;
			}
		}
	}

	/* NAVIGATION */

	&-nav {
		&-list {
			padding: 0;
			list-style: none;
			margin-top: var(--margin-xs);
			margin-left: var(--margin-mdp);

			&-item {
				padding: 15px 0;
				position: relative;

				@media (orientation: landscape) {
					padding: 10px 0;
				}

				&-link {
					color: var(--color-black);

					&:hover {
						color: var(--color-black);
					}
				}

				&:not {
					&(:last-child) {
						&::after {
							position: absolute;
							left: 0;
							background-color: var(--color-borderLight);
							display: block;
							content: '';
							bottom: 0;
							width: 156px;
							height: 1px;
						}
					}
				}
			}
		}

		&-favIcon {
			width: 20px;
			height: 20px;
			position: relative;
			top: 4px;
			margin-right: var(--margin-xs);
			fill: var(--color-premium);
		}
	}

	&-logOut {
		position: relative;
		top: 15px;
		padding: inherit;
		background: transparent;
		border: 0;
		left: 20px;
		color: var(--color-pinkLight);
		appearance: none;

		&::before {
			position: absolute;
			left: 0;
			background-color: var(--color-borderLight);
			display: block;
			content: '';
			top: -20px;
			width: 156px;
			height: 1px;
		}
	}
}

.img-profile {
	border-radius: 20px;
	width: 40px;
}

.icon-logout {
	width: 18px;
	height: 18px;
	margin-right: var(--margin-sm);
	top: 3px;
	position: relative;
}

.headerLight {
	--backgroundColor: var(--c-white);
	--fontColor: var(--c-black);
	--brandColor: var(--c-brand-1);
	--buttonTextColor: var(--c-white);
	--hoverColor: var(--c-black);

	width: auto;
}
