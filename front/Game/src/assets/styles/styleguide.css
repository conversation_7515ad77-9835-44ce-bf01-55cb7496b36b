html {
	font-size: 16px;
	line-height: 1.8;
	font-family: var(--font-primary);
	word-wrap: break-word;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background-color: var(--c-white);
	min-width: 320px;
	margin: 0;
}

#corps {
	flex-grow: 1;
	color: var(--c-primary);
	max-width: 100%;
	min-height: 100vh;
	padding: 0 var(--gutter-15);

	@media (--media-min-desktop) {
		padding: 0;
	}
}

ul {
	list-style: none;
	padding: 0;
}

fieldset {
	border: 0;
	margin: 0;
	padding: 0;
}

textarea {
	resize: vertical;
}

input,
select,
textarea,
button {
	font-size: inherit;
	font-family: inherit;
	line-height: inherit;
}

button {
	border: none;
	margin: 0;
	padding: 0;
	line-height: normal;
	background-color: transparent;
}

button,
[role='button'] {
	cursor: pointer;
	outline: 0;

	&:--hover {
		text-decoration: none;
	}
}

figure {
	margin: 0;
}

iframe {
	border: none;
}

a {
	text-decoration: none;
	color: var(--c-brand-1);

	@media (--viewport-desktop) {
		&:--hover {
			color: var(--c-brand-1);
		}
	}
}

hr {
	border: 0;
	border-top: 1px solid #e3e3e3;
}

img {
	max-width: 100%;
}

body > svg,
body > img {
	display: none;
}

rect {
	box-sizing: unset;
}

.page-main {
	background-color: var(--c-white);
	position: relative;
	padding-top: var(--gutter-15);

	@media (--viewport-desktop) {
		padding: var(--gutter-15);
	}
}

#homepage-game,
#homepage-quiz {
	display: flex;
	flex-direction: column;
	margin-bottom: 50px;

	.see-more {
		align-items: center;
		display: inline-flex;
		padding: 10px 20px;
		margin-left: auto;
		margin-right: auto;
		background-color: var(--c-brand-1);
		color: white;
		border: 1px solid var(--c-brand-1);
		border-radius: var(--radius-5);

		&:hover {
			background-color: white;
			color: var(--c-brand-1);
			border: 1px solid var(--c-brand-1);
		}
	}

	.title-list {
		font-family: var(--fontFamily-heading);
		text-align: center;
		margin: 16px 0;
	}

	&[data-count='1'] #homepage-game-list .gameList-wrapper,
	&[data-count='1'] #homepage-quiz-list .gameList-wrapper {
		justify-content: center;
	}
}

.container {
	background-color: var(--c-white);
	width: 100%;
	max-width: 1000px;
	min-width: 300px;
	margin: 0 auto;
	position: relative;
}

.footerLight {
	&-itemLink {
		text-decoration: none;
		font-size: var(--fs-12);
		font-weight: var(--fw-bold);
	}

	&-copyright {
		font-size: var(--fs-12);
	}
}
