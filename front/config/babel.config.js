const plugins = [
	'@babel/plugin-transform-runtime',
	'@babel/plugin-syntax-dynamic-import',
	'@babel/plugin-proposal-class-properties'
]

const presetReact = [
	'@babel/preset-react',
	{
		runtime: 'automatic',
		importSource: 'jsx-dom-cjs'
	}
]

module.exports = function (api) {
	api.cache(true)
	return {
		env: {
			modern: {
				presets: [
					[
						'@babel/preset-env',
						{
							// Target esmodules browsers instead of browsers list
							targets: {
								esmodules: true
							}
						}
					],
					'@babel/preset-typescript',
					presetReact
				],
				plugins
			},
			legacy: {
				presets: [
					[
						'@babel/preset-env',
						{
							// Target esmodules browsers instead of browsers list
							targets: {
								esmodules: false
							},
							useBuiltIns: 'usage',
							corejs: 3.31
						}
					],
					'@babel/preset-typescript',
					presetReact
				],
				plugins
			},
			test: {
				presets: [
					[
						'@babel/preset-env',
						{
							// Target esmodules browsers instead of browsers list
							targets: {
								esmodules: true
							}
						}
					],
					'@babel/preset-typescript',
					presetReact
				],
				plugins
			}
		}
	}
}
