module.exports = (api) => {
	return {
		plugins: [
			[
				'@csstools/postcss-global-data',
				{
					files: api.options.customConfig.variableFilePath
				}
			],
			'postcss-import',
			'postcss-url',
			[
				'postcss-preset-env',
				{
					browsers: [
						'chrome > 80',
						'firefox > 70',
						'safari > 11',
						'defaults',
						'not dead'
					].join(','),
					stage: 2,
					features: {
						'custom-properties': {
							warnings: true,
							preserve: true
						},

						// postcss-preset-env@7.8.0 enables native "postcss-nesting"
						// which should be disabled to avoid invalid CSS with "postcss-nested"
						'nesting-rules': false
					}
				}
			],
			'postcss-nested',
			[
				'postcss-custom-media',
				{
					preserve: false
				}
			]
		]
	}
}
