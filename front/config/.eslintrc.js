module.exports = {
	parser: '@typescript-eslint/parser',

	rules: {
		'object-shorthand': ['error', 'always'],
		'react/react-in-jsx-scope': 'off',
		'react/jsx-uses-react': 'off',
		'array-callback-return': 'off',
		'react/prop-types': 0,
		'react/display-name': 0,
		'react/jsx-key': 0,
		'react/no-unknown-property': [1, { ignore: ['tracking-parsed', 'innerHTML'] }],
		'@typescript-eslint/no-empty-function': 'off',
		'@typescript-eslint/no-var-requires': 'off'
	},

	env: {
		node: true,
		browser: true,
		jest: true
	},

	extends: [
		'standard',
		'plugin:react/recommended',
		'plugin:@typescript-eslint/recommended',
		'plugin:prettier/recommended'
		// 'plugin:jsdoc/recommended'
	],

	plugins: ['@typescript-eslint'],

	globals: {
		window: false,
		document: false,
		jQuery: false,
		$: false,
		globalConfigs: false
	},

	settings: {
		react: {
			version: '0' // Remove the warning of the missing React package
		}
	}
}
