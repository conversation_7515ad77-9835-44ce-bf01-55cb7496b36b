module.exports = {
	coverageDirectory: 'coverage',
	coveragePathIgnorePatterns: ['<rootDir>/dist', 'mocks/'],
	modulePaths: ['<rootDir>/src'],
	modulePathIgnorePatterns: ['<rootDir>/voi', '<rootDir>/Shopping'],
	resetModules: true,
	rootDir: '../',
	testEnvironment: 'jsdom',
	preset: 'ts-jest/presets/js-with-babel',
	verbose: true,
	reporters: ['default', 'jest-junit'],
	resetMocks: true,
	errorOnDeprecated: true,
	setupFiles: ['<rootDir>/config/setup-jest.js']
}
