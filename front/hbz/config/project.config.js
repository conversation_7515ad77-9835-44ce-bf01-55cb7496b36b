const path = require('path')
const pathSrc = path.resolve(__dirname, '../src/views')

module.exports = {
	entries: {
		site: {
			base: [`${pathSrc}/config`],
			article: [`${pathSrc}/pages/article/config`],
			articleDiaporama: [`${pathSrc}/pages/article/types/articleDiaporama/config`],
			articleSponsored: [`${pathSrc}/pages/article/types/articleSponsored/config`],
			author: [`${pathSrc}/pages/author/config`],
			category: [`${pathSrc}/pages/category/config`],
			datadogClient: [`${pathSrc}/shared/datadog/config`],
			editorial: [`${pathSrc}/pages/editorial/config`],
			error: [`${pathSrc}/pages/error/config`],
			fashionShow: [`${pathSrc}/pages/fashionShow/config`],
			home: [`${pathSrc}/pages/home/<USER>
			orphan: [`${pathSrc}/pages/orphanpage/config`],
			tag: [`${pathSrc}/pages/tag/config`],
			event: [`${pathSrc}/pages/event/config`]
		},
		storybook: {
			storybook: [`${pathSrc}/config-storybook`],
			'pages/article/components/articleOverview': [
				`${pathSrc}/pages/article/components/articleOverview/config`
			],
			'pages/home/<USER>/articlesCarousel': [
				`${pathSrc}/pages/home/<USER>/articlesCarousel/config`
			],
			'pages/author/components/authorLead': [
				`${pathSrc}/pages/author/components/authorLead/config`
			],
			'shared/articleCard': [`${pathSrc}/shared/articleCard/config`],
			'shared/articlesList': [`${pathSrc}/shared/articlesList/config`],
			'shared/callToAction': [`${pathSrc}/shared/callToAction/config`],
			'shared/footer': [`${pathSrc}/shared/footer/config`],
			'shared/genericPush/newsletterPush': [`${pathSrc}/shared/genericPush/newsletterPush/config`],
			'shared/genericPush/socialPush': [`${pathSrc}/shared/genericPush/socialPush/config`],
			'shared/genericPush/subscriptionPush': [
				`${pathSrc}/shared/genericPush/subscriptionPush/config`
			],
			'shared/header': [`${pathSrc}/shared/header/config`],
			'shared/hero': [`${pathSrc}/shared/hero/config`],
			'shared/pagination': [`${pathSrc}/shared/pagination/config`],
			'shared/quote': [`${pathSrc}/shared/quote/config`],
			'shared/storylines': [`${pathSrc}/shared/storylines/config`],
			'shared/separator': [`${pathSrc}/shared/separator/config`],
			'shared/tagsList': [`${pathSrc}/shared/tagsList/config`],
			'shared/textBlock': [`${pathSrc}/shared/textBlock/config`],
			'shared/title': [`${pathSrc}/shared/title/config`]
		}
	},
	fontPathConfig: {
		checkFiles: true,
		ie8fix: true
	}
}
