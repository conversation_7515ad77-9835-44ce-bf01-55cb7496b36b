@import url('vars');

.articlesList {
	display: flex;
	gap: 0 var(--space-10);
	overflow-x: auto;
	scroll-snap-type: x proximity;
	-webkit-overflow-scrolling: touch;

	.articleCard,
	[id^='Native_'] {
		flex: 0 0 281px;
	}
}

@media (--media-min-desktop) {
	.articlesList {
		display: grid;
		grid-template-columns: repeat(2, minmax(0, 1fr));
		gap: var(--space-45) var(--space-25);

		.coreAdsPlacer.desktopOnly {
			justify-self: flex-end;
		}
	}
}
