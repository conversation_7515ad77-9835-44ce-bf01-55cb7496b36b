{% extends "@Front/src/views/shared/genericPush/genericPush.html.twig" %}
{% import "@Front/src/views/shared/macros/svg/index.html.twig" as macroSvg %}

{% block pushContent %}
    <div class="socialPush">
        <p class="socialPush-tagline">Suivez-nous sur :</p>
        <ul class="socialPush-callToActions">
            <li class="socialPush-callToActionsItem">
                <a href="https://www.instagram.com/bazaarfrance/" title="Instagram Harper's Bazaar">
                    {{ macroSvg.getSvgTag('instagram', {
                        srOnlyText: 'Instagram Harper\'s Bazaar'
                    }) }}
                </a>
            </li>
            <li class="socialPush-callToActionsItem">
                <a href="https://www.tiktok.com/@bazaarfrance" title="TikTok Harper's Bazaar">
                    {{ macroSvg.getSvgTag('tiktok', {
                        srOnlyText: 'TikTok Harper\'s Bazaar'
                    }) }}
                </a>
            </li>
            <li class="socialPush-callToActionsItem">
                <a href="https://www.linkedin.com/company/harper’s-bazaar-france/" title="Linkedin Harper's Bazaar">
                    {{ macroSvg.getSvgTag('linkedin', {
                        srOnlyText: 'Linkedin Harper\'s Bazaar'
                    }) }}
                </a>
            </li>
        </ul>
    </div>
{% endblock %}
