{% extends '@Front/src/views/layout.html.twig' %}

{% set description = "Dans le plus pur esprit BAZAAR, notre rédaction digitale vous distille tendances, phénomènes du moment et grandes histoires avec audace et liberté. Une mode élégante et profondément ancrée dans notre époque, une beauté libre et consciente qui interroge, une culture sophistiquée et avant-gardiste mais aussi résolument pop. Questionner, exalter, sublimer, telle est notre mission." %}
{% block metaMainTitleContent %}Équipe éditoriale de Harper's Bazaar France{% endblock %}
{% block metaDescriptionContent %}{{ description }}{% endblock %}

{%- block metas_header -%}
    {{ parent() }}
{%- endblock -%}

{% block pageSpecificCss %}
    {{ parent() }}
    {% include '@web/assets/hbz/assets/templates/editorial-styles.html.twig' %}
{% endblock %}

{% block pageSpecificJs %}
    {% include '@web/assets/hbz/assets/templates/editorial-scripts.html.twig' %}
    {% include '@web/assets/hbz/assets-legacy/templates/editorial-scripts.html.twig' ignore missing %}
{% endblock %}

{% block content %}
    <div class="page editorialTeam">
        {# breadcrumb #}
        {% include '@Front/src/views/shared/breadcrumb/breadcrumb.html.twig' with {
            path: [
                {title: 'accueil', path: '/'}
            ]
        } only %}
        <section class="editorialTeam-section">
            {% include '@Front/src/views/shared/textBlock/textBlock.html.twig' with {
                titleTag: 'h1',
                title: 'La rédaction de Bazaar',
                text: 'Dans le plus pur esprit BAZAAR, notre rédaction digitale vous distille tendances, phénomènes du moment et grandes histoires avec audace et liberté. Une mode élégante et profondément ancrée dans notre époque, une beauté libre et consciente qui interroge, une culture sophistiquée et avant-gardiste mais aussi résolument pop. Questionner, exalter, sublimer, telle est notre mission.',
            } only %}
        </section>
        {% for titles in [
            ['rédaction en chef', 'direction de redaction'],
            ['rédaction']
        ] %}
            {% set sectionAuthors = authors|map(
                (author) => author.attributes|filter(
                    (attribute) => attribute.slug in titles|map(
                        (title) => title|slugify
                    )
                ) ? author : null)
            %}
            {% set sectionAuthors = sectionAuthors|filter((sectionAuthor) => sectionAuthor != null) %}
            {% if sectionAuthors %}
            <section class="editorialTeam-redaction">
                {% include '@Front/src/views/shared/separator/separator.html.twig' %}

                {% include '@Front/src/views/shared/title/title.html.twig' with {
                    tag: 'h2',
                    title: titles[0]
                } %}

                <div class="editorialTeam-team">
                    {% for author in sectionAuthors %}
                        {% include '@Front/src/views/pages/editorial/components/authorCard/authorCard.html.twig' with {author} only %}
                    {% endfor %}
                </div>
            </section>
            {% endif %}
        {% endfor %}
    </div>

{% endblock %}

{% block spriteSVG %}
    <div class="spritesvg">
        {{ parent() }}
        {{ source('@web' ~ asset('assets/hbz/assets/sprites/editorialTeam.svg'), ignore_missing = true) }}
    </div>
{% endblock %}
