@import url('vars');

.diaporama {
	&.hidden {
		display: none;
	}

	.dia {
		&-title-container {
			display: none;
		}

		&-figure {
			border: 1px solid var(--grey-2);
			margin-bottom: var(--space-20);

			img {
				width: 100%;
			}
		}

		&-figcaption {
			display: flex;
			flex-wrap: wrap;
			padding: var(--space-10);

			p {
				flex: 1 0 100%;
				font-family: var(--font-familySerif);
				font-size: var(--fs-12);
				font-style: italic;
				letter-spacing: 1px;
				line-height: 16px;
				order: 2;
			}
		}

		&-title {
			font-family: var(--font-familySansSerif);
			font-size: var(--fs-10);
			font-style: normal;
			letter-spacing: 2px;
			line-height: 15px;
			margin-bottom: var(--space-5);
			text-transform: uppercase;
		}

		&-credit {
			color: var(--grey-3);
			flex: 1 0 100%;
			font-family: var(--font-familySansSerif);
			font-size: var(--fs-10);
			margin-top: var(--space-20); /* p is conditional */
			order: 3;
			position: relative;
			text-align: right;
		}

		&-counter {
			position: absolute;
			left: 0;
		}

		&-ads {
			margin: 0 0 var(--space-20);
		}
	}
}
