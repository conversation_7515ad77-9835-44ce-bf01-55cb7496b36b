// import CSS
import './assets/styles/diaporama.css'

// import JS
import Diaporama from './assets/scripts/diaporama.js'

// import shared components
import 'Shared/pageGrid/config'

const container = document.querySelector('.diaporama')
const scrollToDiaporamaButton = document.querySelector('.diaporamaCta')
if (container) {
	const diaporama = new Diaporama({
		container,
		scrollToDiaporamaButton
	})
	diaporama.init()
}
