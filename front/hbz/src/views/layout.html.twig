<!DOCTYPE html>
{# Detect GET parameter to disable core-ads (use variable from config TWIG) #}
{% set coreAdsDisabled = app.request.query.has('puboff') or app.request.query.has('abtasty_editor') %}
{# Detect GET parameter to disable gtm (use variable from config TWIG) #}
{% set gtmDisabled = app.request.query.has('gtmoff') %}
{# Detect GET parameter to disable cmp (use variable from config TWIG) #}
{% set cmpDisabled = app.request.query.has('cmpoff') %}
{# Detect GET parameter to disable datadog (use variable from config TWIG) #}
{% set datadogDisabled = app.request.query.has('ddoff') %}
{% set isArticle = content.type|default('')|lower == 'article' %}

<html lang="fr" prefix="og: http://ogp.me/ns#">
<head>
    {%- block metas_header -%}
        <meta charset="UTF-8">
        <meta name="robots" content="max-snippet:-1">
        <meta name="robots" content="max-image-preview:large">
        <meta name="robots" content="max-video-preview:-1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta name="viewport" content="width=device-width, initial-scale=1,minimum-scale=1">
        <meta name="theme-color" content="#FFFFFF">
        <meta name="author" content="Prisma Média">
        <meta name="copyright" content="Harpers Bazaar">
        <meta name="siteName" content="harpersbazaar.fr">
    {%- endblock -%}

    {% if block('metaMainTitleContent') is defined %}
        <title>{{ block('metaMainTitleContent') }}</title>
        <meta name="title" content="{{ block('metaMainTitleContent') }}" />
        <meta property="og:title" content="{{ block('metaMainTitleContent') }}" />
    {% else %}
        <title>Static Harpers Bazaar</title>
        <meta name="title" content="Static Harpers Bazaar" />
        <meta property="og:title" content="Static Harpers Bazaar" />
    {% endif %}
    {% if block('metaDescriptionContent') is defined %}
        <meta name="description" content="{{ block('metaDescriptionContent') }}" />
        <meta property="og:description" content="{{ block('metaDescriptionContent') }}" />
    {% endif %}
    {% if block('metaPhoto') is defined %}
        <meta property="og:image" content="{{ block('metaPhoto') }}" />
    {% endif %}
    <meta property="og:url" content="{% block url %}{{ app.request.schemeAndHttpHost ~ app.request.pathInfo }}{% endblock %}">
    <meta property="og:site_name" content="HarpersBazaar.fr">

    <link rel="apple-touch-icon" href="{{ asset('assets/hbz/assets/images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="192x192" href="{{ asset('assets/hbz/assets/images/android-chrome-192x192.png') }}">
    <link rel="icon" href="{{ asset('assets/hbz/assets/images/favicon.ico') }}" />
    <link rel="manifest" href="/manifest.json">

    {# Preconnect to certain domains #}
    <link rel="preconnect" href="https://consent.harpersbazaar.fr">
    <link rel="preconnect" href="https://api.prismaconnect.fr">
    <link rel="preconnect" href="{{ domain_scds|default('') }}">
    <link rel="preconnect" href="https://www.datadoghq-browser-agent.com">
    {% if false == gtmDisabled %}
        <link rel="preconnect" href="https://www.googletagmanager.com">
    {% endif %}
    {% if false == cmpDisabled %}
        <link rel="preconnect" href="https://sourcepoint.mgr.consensu.org">
    {% endif %}

    {# preload main images #}
    {% block metas_preload_image %}{% endblock %}

    {# Fonts preload #}
    {% block preload_fonts %}
        <link rel="preload" href="{{ asset('assets/hbz/assets/fonts/linotype-didot-roman.woff2') }}" as="font" type="font/woff2" crossorigin>
        <link rel="preload" href="{{ asset('assets/hbz/assets/fonts/helvetica-now-display-regular.woff2') }}" as="font" type="font/woff2" crossorigin>
    {% endblock %}

    {# queues #}
    {# TODO intégration de batch ici ? #}
    <script>
        window.pmcstarter = window.pmcstarter || function () {
            (window.pmcstarter.q = window.pmcstarter.q || []).push(arguments);
        };
        window.coreAds = window.coreAds || {};
        window.coreAds.queue = window.coreAds.queue || [];
    </script>

    <script>
        window.pmdConsent = window.pmdConsent || function (data) {
            ;(window.pmdConsent.queue = window.pmdConsent.queue || []).push(data);
        }

        // To be optimized in T2
        window.checkPmdConsent = function (type, callback) {
            let timer;
            const checkConsent = () => {
                if (window.pmdConsent.queue && window.pmdConsent.queue.find((item) => item.type === type)) {
                    clearTimeout(timer);
                    callback instanceof Function && callback();
                } else {
                    timer = setTimeout(() => {
                        requestAnimationFrame(checkConsent);
                    }, 100);
                }
            };
            window.requestAnimationFrame(checkConsent);
        };
    </script>

    {# CSS #}
    {% block pageSpecificCss %}
        {% include '@web/assets/hbz/assets/templates/base-styles.html.twig' only %}
    {% endblock %}

    {# scripts #}
    {# scripts to load #}
    {% set sourcepointUrl = scds_url('sourcepoint/sourcepoint.min.js') %}
    {% set coreadsUrl = scds_url('advertising-core/core-ads.js') %}
    {% set pmcUrlLegacy = scds_url('pmc-starter/index.js') %}
    {% set pmcUrlModern = scds_url('pmc-starter/esm.index.js') %}

    {# Cmp sourcepoint #}
    {% if cmpDisabled == false %}
        <script defer crossorigin src="{{ preload(sourcepointUrl, {as: 'script', crossorigin: true}) }}"></script>
    {% endif %}

    {# Pmc starter #}
    <script defer crossorigin type="module" src="{{ preload(pmcUrlModern, {as: 'script', crossorigin: true}) }}"></script>
    <script defer crossorigin nomodule src="{{ pmcUrlLegacy }}"></script>

    {# scripts internes #}
    {% include '@web/assets/hbz/assets/templates/base-scripts.html.twig' %}
    {% include '@web/assets/hbz/assets-legacy/templates/base-scripts.html.twig' ignore missing %}

    {% block pageSpecificJs %}{% endblock %}

    {# coreAds #}
    {% if coreAdsDisabled == false %}
        <script>
            (function(){
                var injectCoreAds = function() {
                    var script = document.createElement('script');
                    script.defer = true;
                    script.type = 'text/javascript';
                    script.src = '{{ coreadsUrl }}';
                    script.crossorigin = 'true';
                    document.head.appendChild(script);
                };

                injectCoreAds();
            })();
        </script>
    {% endif %}

    {# génération pageHitId #}
    {{ coreads_generate_page_hit_id('hbz') }}

    <script>
        {# enable consent to GTM #}
		window ['gtag_enable_tcf_support'] = true;

        {# pageHitID dans objet global pour utilisation dans bff-bundle #}
		window.frontConfig = {
			pageHitID: window.dataLayer.filter(el => el.pageHitID !== undefined).slice(-1)[0].pageHitID
		};
    </script>

    <script type="text/javascript">
        {% set taxonomies = content.taxonomies is defined and content.taxonomies is not null ? content.taxonomies|map(item => item.toArray) : [] %}
        var dataLayer = window.dataLayer || []
        dataLayer.push({{ dataLayer|default([])|json_encode|raw }})
        dataLayer.push({ semanticTags: {{ coreads_tag_semantic(taxonomies)|json_encode|raw }} })

        function setDatalayerAdvertisement() {
            var url = new URL(window.location.href);
            var hosts = url.host.split('.')

            var dataLayerFlatten = {}
            dataLayer.map((dataLayerItem) => { dataLayerFlatten = Object.assign(dataLayerFlatten, dataLayerItem) })

            var env = 'production'
            if (dataLayerFlatten.env === 'recette') {
                env = 'staging'
            } else if (dataLayerFlatten.env === 'dev') {
                env = 'development'
            }
            dataLayer.push({
                advertisement: {
                    env,
                    hasVideos: typeof dataLayerFlatten.hasVideos !== 'undefined' && dataLayerFlatten.hasVideos !== 'none',
                    brand: {
                        name: "{{ appConfig.name|lower }}",
                        abbr: "{{ appConfig.brand|lower }}",
                        domain: "{{ appConfig.domain|lower }}",
                        subDomain: hosts[0],
                        isPrisma: true
                    },
                    page: {
                        path: dataLayerFlatten.path,
                        contentObjectId: dataLayerFlatten.contentObjectId ?? undefined,
                        pageType: dataLayerFlatten.pageType,
                        pageCategory: dataLayerFlatten.pageCategory,
                        pageSubCategory: dataLayerFlatten.pageSubCategory ? dataLayerFlatten.pageSubCategory : undefined,
                        keywords: dataLayerFlatten.keywords,
                        title: dataLayerFlatten.title ?? undefined,
                        author: dataLayerFlatten.author ?? undefined,
                        sda: {{ coreads_tag_iab(taxonomies)|json_encode|raw }}
                    }
                }
            })

        }
        setDatalayerAdvertisement();
    </script>

    {% if isArticle %}
        {% include '@CommonFront/components/parsely/data.html.twig' with {article: content} only %}
    {% endif %}
    <!-- pmd_structureddata_init -->
    {% block jsonld %}
        {% if content|default(null) %}
            <script type="application/ld+json">{{ json_ld(content) }}</script>
        {% endif %}
    {% endblock %}
    <!-- /pmd_structureddata_init -->

    {# Google Tag Managers #}
    {% if false == gtmDisabled %}
        <script>
            window.checkPmdConsent('allConsentGiven', () => {
            {% for gtmId in appConfig.google_tag_manager|merge(['GTM-NK7QRQ4', 'GTM-PTK4MRSR']) %}
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','{{ gtmId }}');
            {% endfor %}
            });
            window.checkPmdConsent('consentUserActionComplete', function() {
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.defer=true;j.src=
                    '//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','GTM-TFXFQXH');
            });
        </script>
    {% endif %}

    {# datadog #}
    {% if false == datadogDisabled %}
        {% include '@Front/src/views/shared/datadog/datadog.html.twig' only %}
    {% endif %}
</head>

<body
    data-wide-noload
    id="top"
    data-branch-deeplink="{{ app.request.attributes.get('deepLinking') }}">
    {% if false == gtmDisabled %}
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ appConfig.google_tag_manager.id }}" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    {% endif %}
    {%- block header -%}
        {% include '@Front/src/views/shared/header/header.html.twig' only %}
    {%- endblock -%}

    <main id="corps">
        {% block sas_habillage %}
        {% endblock sas_habillage %}

        <div class="site_content" id="wrapper">
            {% block sas_ban %}
                <div class="coreAdsPlacer desktopTabletOnly highBannerAds bannerAds specialBanner">
                    {% if display is defined %}
                        {{ coreads_tag('Banniere-Haute', {'device': display}) }}
                    {% else %}
                        {{ coreads_tag('Banniere-Haute', {'device': 'desktop'}) }}
                    {% endif %}
                </div>
            {% endblock sas_ban %}

            {%- block content -%}{%- endblock -%}

            {%- block insitePannel -%}
                {%- include '@Front/src/views/shared/userLoginTunnel/userLoginTunnel.html.twig' -%}
            {%- endblock -%}
        </div>
    </main>

    {%- block footer -%}
        {% include "@Front/src/views/shared/footer/footer.html.twig" only %}
    {%- endblock -%}

    {{ coreads_tag('Postitiel', {'device': ['mobile', 'tablet']}) }}

    {% block sas_out_of_ban %}
        {{ coreads_tag('Out-Of-Banner') }}
    {% endblock sas_out_of_ban %}

    {% if isArticle %}
        {% include '@CommonFront/components/parsely/finalise.html.twig' %}
    {% endif %}

    {%- block javascripts_footer -%}{%- endblock -%}

    {% block spriteSVG %}
        {{ source('@web' ~ asset('assets/hbz/assets/sprites/base.svg'), ignore_missing = true) }}
    {% endblock %}
    <!-- Mosaic -->
</body>
</html>
