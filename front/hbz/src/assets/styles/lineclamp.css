@import url('vars');

.line-clamp {
	display: block;
	position: relative;
	line-height: 1.3;
	text-overflow: ellipsis;
	overflow: hidden;

	&::after {
		content: ' ';
		text-align: right;
		bottom: 0;
		right: 0;
		width: 25%;
		display: block;
		position: absolute;
		height: calc(1em * 1.285);
		background: linear-gradient(to right, rgb(var(--white) 0), rgba(var(--white)) 75%);
	}
}

@supports (-webkit-line-clamp: 1) {
	.line-clamp::after {
		display: none;
	}
}

.line-clamp1 {
	-webkit-line-clamp: 1;
	line-clamp: 1;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	height: calc(1em * 1.285 * 1);
}

.line-clamp2 {
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	height: calc(1em * 1.285 * 2);
}

.line-clamp3 {
	-webkit-line-clamp: 3;
	line-clamp: 3;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	height: calc(1em * 1.285 * 3);
}

.line-clamp4 {
	-webkit-line-clamp: 4;
	line-clamp: 4;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	height: calc(1em * 1.285 * 4);
}

.line-clamp5 {
	-webkit-line-clamp: 5;
	line-clamp: 5;
	-webkit-box-orient: vertical;
	display: -webkit-box;
	height: calc(1em * 1.285 * 5);
}
