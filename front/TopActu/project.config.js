'use strict'

const path = require('path')

const pathFront = path.resolve(__dirname, '../')
const topactu = `${pathFront}/TopActu`
const pathSrc = `${topactu}/src`
const pathcommonSrc = `${pathFront}/common/src`
const pathNodeModules = `${pathFront}/node_modules`

module.exports = {
	webpack: {
		publicPath: '/assets/topactu',
		loaderIncludeRule: [
			topactu,
			pathSrc,
			pathcommonSrc,
			`${pathNodeModules}/@capsule`,
			`${pathNodeModules}/@prismamedia`
		],
		chunksWebpackPlugins: {
			domain: 'app.request.getSchemeAndHttpHost()',
			entriesWithDomain: []
		},
		postcss: {
			cssPrefixer: false,
			variableFilePath: [`${pathSrc}/assets/styles/vars.css`]
		},
		alias: {
			topactu,
			commonSrc: pathcommonSrc,
			Pages: `${pathSrc}/views/pages`,
			Assets: `${pathSrc}/assets`,
			Shared: `${pathSrc}/views/shared`,
			Vendors: pathNodeModules
		}
	},
	entries: {
		site: require('./entry-global.js')
	},
	fontPathConfig: {
		checkFiles: true,
		ie8fix: true
	}
}
