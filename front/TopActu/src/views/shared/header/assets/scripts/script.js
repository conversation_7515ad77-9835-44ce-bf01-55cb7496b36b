/* Pagination */
const returnButton = document.querySelector('.headerLight-homeLink')
if (returnButton && returnButton.innerHTML === 'Tous les quiz') {
	returnButton.setAttribute('id', 'quizzes-link')
} else {
	returnButton.setAttribute('id', 'games-link')
}
/* Pagination */

/* PMC connection */
window.pmcstarter((myPMC) => {
	myPMC.isConnected().then((connexion) => {
		if (connexion) {
			showConnected()
			myPMC.getProfile().then((profile) => {
				manageMenuProfile()
				enableProfileCta(profile)
			})
		} else {
			hiddeConnected()
		}
	})
})

const showConnected = () => {
	const elementAuthentitication = document.getElementById('pmc-authentification')
	const elementAuthentiticated = document.getElementById('pmc-authentificated')

	if (!elementAuthentitication.classList.contains('isHidden'))
		elementAuthentitication.classList.add('isHidden')
	if (elementAuthentiticated.classList.contains('isHidden'))
		elementAuthentiticated.classList.remove('isHidden')
}

const hiddeConnected = () => {
	const elementAuthentitication = document.getElementById('pmc-authentification')
	const elementAuthentiticated = document.getElementById('pmc-authentificated')

	if (elementAuthentitication.classList.contains('isHidden'))
		elementAuthentitication.classList.remove('isHidden')
	if (!elementAuthentiticated.classList.contains('isHidden'))
		elementAuthentiticated.classList.add('isHidden')
}

const manageMenuProfile = () => {
	const elementMenuOpen = document.getElementById('pmc-authentificated')

	const elementMenuClose = document.getElementById('profileMenu-close')
	const elementLogOut = document.querySelector('.pmc-auth-logout')

	elementMenuOpen.addEventListener('click', () => {
		document.getElementById('profileMenu').classList.add('isShown')
	})

	elementMenuClose.addEventListener('click', () => {
		document.getElementById('profileMenu').classList.remove('isShown')
	})

	elementLogOut.addEventListener('click', () => {
		hiddeConnected()
	})
}

const enableProfileCta = (profile) => {
	const profileCta = document.querySelectorAll('[data-cta-profile]')

	profileCta.forEach((element) => {
		const avatarElement = document.createElement('img')
		avatarElement.setAttribute('src', profile.avatar)
		avatarElement.classList.add('img-profile')
		avatarElement.setAttribute('alt', "Photo de profil de l'internaute")

		if (profile.avatar) {
			// Add an avatar image
			if (element.getElementsByTagName('svg').length !== 0) {
				element.getElementsByTagName('svg')[0].replaceWith(avatarElement)
			} else {
				element.prepend(avatarElement)
			}
		}

		if (element.querySelectorAll('[data-profile-name]').length !== 0)
			element.querySelectorAll('[data-profile-name]')[0].textContent = profile.firstname
	})
}
/* PMC connection */
