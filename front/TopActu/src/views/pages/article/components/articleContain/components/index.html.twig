{% set leadVideo = false %}
{% if content.videos is defined and content.videos|length %}
  {% set leadVideo = content.videos[0] %}
  {% set playerLeaderParams = {
    playerId: appConfig.brand.dmPlayer.id,
    playerProvider: leadVideo.providerName,
    playerVideoId: leadVideo.id,
    playerPosition: "Leader",
    playerPartner: "",
    playerName: appConfig.brand.name ~ " - Player Top Actu Leader Autoplay",
    playerVideoTitle: leadVideo.title
  } %}
{% endif %}
{% set nbrCharcters = app.request.query.get('utm_medium') == 'loc' ? 300 : 600 %}

{% if content.photo %}
  <figure class="page-article-media">
    {% if leadVideo != false %}
      {% if leadVideo.providerName == "Dailymotion" %}
          <div class="ratio-16-9" style="background: url({{
            pmd_image_resize_url(
            content.photo.url,
            'fit',
            1280,
            720,
            content.photo.focusPoint is defined and content.photo.focusPoint ? {
                'background-color': 'ffffff',
                'focus-point': [content.photo.focusPoint.x, content.photo.focusPoint.y]|join(','),
                'quality': 80
            } : {
                'background-color': 'ffffff',
                'quality': 80
            }
          )
          }});background-size: cover;background-position: center;background-repeat: no-repeat;">
          <div data-ads-core="{{ playerLeaderParams|serialize('json') }}"
               data-embed="default"
               data-provider="dailymotion-tac"
               class="prisma-player"
               id="prisma-player-leader"
          ></div>
        </div>
      {% endif %}
      {% if content.articleSlideshow %}
        {% include '@Front/src/views/shared/button/index.html.twig' with {
          text: 'Voir le diaporama'|trans,
          url: content.articleSlideshow.path,
          eventAction: 'Diaporama',
          eventCategory: 'Top Actu',
          eventLabel: 'Article',
          id: 'button-diapo'
        } only %}
      {% endif %}
    {% elseif content.hasAudios %}
       <div id="player-podcast" class="ads-core-audio" data-ads-core='{
            "audioFeed": "{{ content.audios[0].feed|default('') }}",
            "playerPlaylist": "{{ content.audios[0].playlist|default('') }}",
            "playerStart": "{{ content.audios[0].start|default('') }}"
            }'
            data-embed="default"
            style="height: 196px;" data-application-id>
      </div>
    {% elseif content.photo %}
      <picture class="ratio-16-9">
        {% include "@Front/src/views/shared/images/index.html.twig" with {
          "img_url": content.photo,
          "img_title": content.title,
          "img_size": {
            "320": [320, 180],
            "480": [480, 270],
            "999": [1000, 562],
            "default": [610, 343, 999]
          },
          "not_lazy": true
        }
        only %}
        </picture>
        {% if content.articleSlideshow %}
          {% include '@Front/src/views/shared/button/index.html.twig' with {
            text: 'Voir le diaporama'|trans,
            url: content.articleSlideshow.path,
            eventAction: 'Diaporama',
            eventCategory: 'Top Actu',
            eventLabel: 'Article',
            id: 'button-diapo'
          } only %}
      {% endif %}

      <figcaption class="page-article-media-meta">
        {% if content.photo.caption %}
          <span class="page-article-media-legend">{{ content.photo.caption|striptags }}</span>
        {% elseif content.title %}
          <span class="page-article-media-legend">{{ content.title }}</span>
        {% endif %}

        {% if content.photo.credit %}
          <span class="page-article-media-copyright">&copy; {{ content.photo.credit }}</span>
        {% endif %}
      </figcaption>
    {% endif %}
  </figure>
{% endif %}

<div id="articleBody" class="page-article-body" data-seeMore="true">
  {% set htmlLead = draftToHtml(content.lead.draft).html %}
  {% if htmlLead %}
    <div class="page-article-lead">
    <button class="page-article-btn" id="page-article-btnHiddenText">
				<span class="icon-play"><svg width="20" height="20" xmlns="http://www.w3.org/2000/svg"><path d="M9.972.084c5.498 0 9.972 4.455 9.972 9.93s-4.474 9.93-9.972 9.93C4.473 19.944 0 15.489 0 10.014S4.473.084 9.972.084zm0 1.26c-4.8 0-8.706 3.89-8.706 8.67 0 4.78 3.905 8.67 8.706 8.67 4.8 0 8.706-3.89 8.706-8.67 0-4.78-3.906-8.67-8.706-8.67zM8.387 6.453l5.696 3.881-5.696 3.881V6.452z" fill="#FFF" fill-rule="evenodd"/></svg></span>
					lire le contenu de l'article
		</button>
    <div class="page-article-subtitle" id="leadSubtitle">{{ htmlLead|raw }}</div>
    </div>
  {% endif %}

  <div class="tac-ads tac-ads-300x250 tac-ads-mobile tac-ads-sticky" id='mobile-ads-sticky'>
    {{ coreads_tag("Pave-Haut", {"device": ["mobile", "tablet"]})|coreads_tag_defer }}
  </div>

  {% set htmlBody = content.body.draft|default('') ? draftToHtml(content.body.draft).html : '' %}

  {% if htmlBody %}
    <div class="page-article-text gradient-overlay" id='articleText'>
      {{ coreads_inject_tag(tac_link_block_rendered(embed_video_renderer(add_in_image(htmlBody)|raw, appConfig.brand.dmPlayer.embedId)), "Pave-Haut2", nbrCharcters, 150, {"device": ["mobile", "tablet"]}, null, true)|coreads_tag_defer }}
    </div>
  {% endif %}

  {% if content.source %}
    <div class="page-article-source" id="copyright">
      <span>{{ content.source.title }}</span>
    </div>
  {% endif %}
</div>

  {% if leadVideo != false %}
    <div class="page-article-body-meta">
      {% if content.photo.credit %}
        <span class="page-article-body-copyright" id='copyright-photo'>>&copy; {{ content.photo.credit }}</span>
      {% endif %}
    </div>
  {% endif %}

{{ coreads_tag("outstream", {"device": ["desktop", "mobile", "tablet"]}) }}

{% include "@Front/src/views/shared/outbrain/index.html.twig" with {
  "id": "AR_22"
} %}
