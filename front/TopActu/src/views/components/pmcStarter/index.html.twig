<script>
  window.pmcstarter = window.pmcstarter || function () {
    (window.pmcstarter.q = window.pmcstarter.q || []).push(arguments)
  }
</script>
<script type="module" src="{{ pmcStarterUrlModerne }}"></script>
<script>(function(){var d=document;var c=d.createElement('script');if(!('noModule' in c)&&'onbeforeload' in c){var s=!1;d.addEventListener('beforeload',function(e){if(e.target===c){s=!0}else if(!e.target.hasAttribute('nomodule')||!s){return}e.preventDefault()},!0);c.type='module';c.src='.';d.head.appendChild(c);c.remove()}}())</script>
<script src="{{ pmcStarterUrlLegacy }}" nomodule></script>
