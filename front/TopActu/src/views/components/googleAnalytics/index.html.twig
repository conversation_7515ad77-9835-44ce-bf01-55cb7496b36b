<!-- Google Analytics -->
<script>
window.consentCheck('allConsentGiven', function () {

  // Load Google Analytics scripts
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

  // GAN Analytics init
	var _gaq = _gaq || []; // compatibility for old Google Analytics

	window.ga = window.ga || function () {
		(ga.q = ga.q || []).push(arguments)
	};

	window.ga.l = +new Date;

  // Create GA
  const pageHitID = (window.dataLayer.find(element => !!element.pageHitID) || {}).pageHitID
  const authId = document.cookie.match('(^|;) ?authId=([^;]*)(;|$)')

  ga('create', '{{ appConfig.brand.gan }}', 'auto');
  {% if pageTemplate == 'article' %}
  ga('set', 'dimension12', '{{ content.breadcrumb|filter(item => item.level in [2, 3])|map(p => p.title)|join("---") }}');
  ga('set', 'dimension13', 'tac-Article');
  ga('set', 'dimension16', '{{ content.path }}');
  ga('set', 'dimension17', '{{ content.mainAuthor.name|default('') }}');
  ga('set', 'dimension18', '{{ content.tagNames|join(',') }}');
  ga('set', 'dimension19', '{{ content.videoTypes is not empty and content.videos is not empty ? (content.videoTypes|map(v => v ~ '-' ~ content.videos[0].providerName)|join(', ')) : "none" }}');
  ga('set', 'dimension22', '{{ content.publishedAt|date("Y-m-d G\\:i\\:s") }}');
  ga('set', 'dimension30', 'Article:{{ content.id }}');
  {% endif %}
  ga('set', 'dimension14', window.navigator.userAgent || 'undefined');
  ga('set', 'dimension21', 'https');
  ga('set', 'dimension29', pageHitID || 'undefined');
  ga('set', 'dimension68', authId ? authId[2] : null);
  ga('t0.send', 'pageview');

  const referrerCookieValue = document.cookie.match("(^|[^;]+)\scmp_referrer\s=\s*([^;]+)")
  if (referrerCookieValue) {
    const referrer = referrerCookieValue.pop()
    window.ga('set', 'referrer', referrer)
    ga('set', 'dimension27', referrer);
    document.cookie = 'cmp_referrer=; expires=Thu, 01 Jan 1970 00:00:00 UTC;'
  } else {
    ga('set', 'dimension27', document.referrer || "undefined");
  }

  // Add gaReady to pmdConsent queue and use it to check if tracking events can be send
  window.pmdConsent({ type: 'gaReady' });
});
</script>
<!-- End Google Analytics -->
