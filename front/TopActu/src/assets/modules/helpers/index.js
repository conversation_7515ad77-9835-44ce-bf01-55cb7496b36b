import logger from '../logger'
import TrackElements from 'commonSrc/assets/scripts/utils/trackElements'

export function bindGanEventOnClick(element, payload) {
	element.addEventListener('mouseup', (event) => {
		// Check mouse button
		if (event.button === 0 || event.button === 1) {
			// Add GA event on click
			TrackElements.sendEvent({
				event: 'button_click',
				button_name: 'cta-tac-click-link',
				...payload
			})
		}
	})
}

export function bindGanEventOnLinks() {
	// Bind click on Logo
	const headerLogo = document.querySelector('.headerLight-logo')
	bindGanEventOnClick(headerLogo, { eventLabel: 'Logo' })

	// Bind all link in article
	document.querySelectorAll('.page-article-body a').forEach((el) => {
		bindGanEventOnClick(el)
	})
}

export function getQueryParams() {
	const urlSearchParams = new URLSearchParams(window.location.search)
	const params = Object.fromEntries(urlSearchParams.entries())

	return params
}

export function isClickToSeeArticle(articleId = 'page-main-article') {
	const mainArticle = document.getElementById(articleId)
	const isClickToSeeArticleAttr = mainArticle.getAttribute('data-fullContent')
	const isClickToSeeArticle = isClickToSeeArticleAttr !== 'true'

	return isClickToSeeArticle
}

export function isClickToPlayArticle(articleId = 'page-main-article') {
	const mainArticle = document.getElementById(articleId)
	const clickToAutoplayAttr = mainArticle.getAttribute('data-clicktoautoplay')
	const isClickToPlay = clickToAutoplayAttr === 'true'

	return isClickToPlay
}

export function getBrandTrigram() {
	const log = logger('helpers.js#getBrandTrigram').info
	let trigram = null

	for (const layer of window.dataLayer) {
		if (!layer.brand?.brandKey) continue

		trigram = String(layer.brand.brandKey).toUpperCase()
	}

	log('trigram', trigram)

	return trigram.toUpperCase()
}

export function getEnv() {
	const log = logger('helpers.js#getEnv').info
	let env = null

	for (const layer of window.dataLayer) {
		if (!layer.env) continue

		env = String(layer.env)
	}

	log('env', env)

	return env
}

export function getBrandTrigramOne() {
	const log = logger('helpers.js#getBrandTrigramOne').info
	let trigram = null

	for (const layer of window.dataLayer) {
		if (!layer.brand?.one) continue
		trigram = String(layer.brand.one).toUpperCase()
	}

	log('trigram one', trigram)

	return trigram.toUpperCase()
}

export function getAbbr() {
	const log = logger('helpers.js#getAbbr').info
	let trigram = null

	for (const layer of window.dataLayer) {
		if (!layer.advertisement?.brand?.abbr) continue

		trigram = String(layer.advertisement.brand.abbr).toUpperCase()
		if (trigram.includes('TAC')) {
			trigram = trigram.replace('TAC', '')
			trigram = trigram.replace('-', '')
		}
	}

	if (trigram === null) {
		document.body.classList.forEach((cl) => {
			if (cl.includes('brand-')) {
				trigram = cl.replace('brand-', '')
			}
		})
	}

	log('abbr', trigram)

	return trigram.toUpperCase()
}

export function getCookieByName(cookieName) {
	const value = '; ' + document.cookie
	const parts = value.split('; ' + cookieName + '=')

	return parts.length === 2 ? parts.pop().split(';').shift() : null
}

export function isPremiumUser() {
	const premiumCookieNames = ['rec1-pmc-premium', 'rec2-pmc-premium', 'pmc-premium']

	return premiumCookieNames.map(getCookieByName).some((value) => value !== null)
}
