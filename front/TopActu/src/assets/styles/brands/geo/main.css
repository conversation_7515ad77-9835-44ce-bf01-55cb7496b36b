@import url('fonts.css');
@import url('vars.css');
@import url('../../medias.css');

.brand-geo {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-feature-settings: 'liga', 'kern';
	letter-spacing: -0.5px;

	& .title-heading {
		color: var(--c-primary);
		font-size: var(--fs-28);
		font-weight: 600;
		line-height: 30px;
		margin-bottom: 20px;
		letter-spacing: -0.5px;

		@media (--viewport-desktop) {
			font-size: var(--fs-48);
			line-height: 52px;
		}
	}

	& .page-article-subtitle {
		font-family: var(--font-primary);
		font-size: var(--fs-20);
		font-weight: 600;
		line-height: 26px;
	}

	& .page-article-text {
		font-family: var(--font-primary);
		font-size: var(--fs-18);
		line-height: 26px;

		a {
			text-decoration: underline;
		}
	}

	h2 {
		font-size: var(--fs-26);
	}

	& a {
		@media (--viewport-desktop) {
			&:hover {
				color: var(--c-brand-1-hover);
			}
		}
	}

	& strong {
		font-weight: 600;
	}

	& .defaultBtn {
		font-weight: 500;
		text-align: center;
		line-height: 1.9rem;
		margin: 35px auto;

		&-link,
		&-cta {
			border-radius: 0.2rem;
			background-color: #75ab1b;
			color: #fff;
			letter-spacing: 0.02rem;
			outline: none;
			padding: 10px 25px;
			text-transform: uppercase;
			transition: background-color 0.2s;
		}

		a {
			font-size: 14px;
			font-weight: bold;
			font-family: var(--font-primary);

			&:hover {
				background-color: #8db548;
				color: var(--c-grey-1);
				box-shadow: 0 2px 2px 0 rgb(21 20 20 / 10%);
			}
		}
	}
}
