/* custom properties */
:root {
	/* Colors */
	--c-brand-1: #bd0406;

	/* Font-family */
	--font-primary: 'Inter', sans-serif;
	--font-secondary: 'Inter', sans-serif;

	/* Categories background colors */
	--lifestyle-bg: #ffeaea;
	--royalty-bg: #f3f6ff;
	--article-bg: #f0f2f5;

	/* Categories primary colors */
	--lifestyle-primary: #ec999b;
	--royalty-primary: #7187bc;
	--article-primary: #8b98aa;
	--entertainment-primary: #98b6ae;

	/* Categories Patterns */
	--royalty-pat-img: linear-gradient(135deg, transparent 62%, var(--royalty-primary) 65%, var(--royalty-primary) 70%, transparent 70%);
	--royalty-pat-size: 25px 25px;
	--lifestyle-pat-img: linear-gradient(70deg, transparent 39.47%, var(--lifestyle-primary) 0, var(--lifestyle-primary) 50%, transparent 0, transparent 89.47%, var(--lifestyle-primary) 0, var(--lifestyle-primary));
	--lifestyle-pat-size: 25px 68px;
	--article-pat-img: linear-gradient(160deg, transparent 39.47%, var(--article-primary) 39.47%, var(--article-primary) 50%, transparent 50%, transparent 89.47%, var(--article-primary) 89.47%, var(--article-primary) 100%);
	--article-pat-size: 55px 20px;
	--entertainment-pat-img: radial-gradient(ellipse at center, var(--entertainment-primary) 30%, transparent 20%, transparent 100%);
	--entertainment-pat-size: 20px 20px;
}
