@import url('fonts.css');
@import url('vars.css');
@import url('../../medias.css');

.brand-cap {
	& .page-main {
		font-size: 18px;
		line-height: 1.5;
	}

	& .title-heading {
		font-family: var(--font-secondary);
		text-align: center;
		font-size: var(--fs-28);
		font-weight: var(--fw-bold);
		line-height: 1.15;
		margin-bottom: 6px;

		@media (--viewport-desktop) {
			font-size: var(--fs-40);
		}
	}

	& h2 {
		font-family: var(--font-secondary);
		font-size: var(--fs-24);

		@media (--viewport-desktop) {
			font-size: var(--fs-28);
		}
	}

	& .page-article-subtitle {
		font-size: var(--fs-16);
		line-height: 1.4;

		@media (--viewport-desktop) {
			font-size: var(--fs-20);
		}
	}

	& .page-article-text {
		font-family: var(--font-primary);
		font-size: var(--fs-16);

		@media (--viewport-desktop) {
			font-size: var(--fs-18);
		}
	}

	& .defaultBtn {
		font-weight: 500;
		text-align: center;
		line-height: 1.375rem;
		margin: 35px auto;

		&-link,
		&-cta {
			display: inline-block;
			background-color: #fff;
			border: 2px solid #199ad9;
			border-color: var(--c-brand-1);
			color: var(--c-brand-1);
			transition: color 0.2s, background-color 0.2s;
			outline: none;
			padding: 10px 30px;
			letter-spacing: 0.1rem;
			justify-content: space-between;
			text-transform: uppercase;
		}

		a {
			font-size: 0.941rem;
			font-weight: bold;

			&:hover {
				color: #fff;
				background-color: var(--c-brand-1);
			}
		}
	}

	h1,
	h2 {
		font-family: var(--font-secondary);
		font-size: var(--fs-28);
	}

	#corp {
		font-family: var(--font-primary);
		font-size: var(--fs-18);
	}
}
