@import url('vars.css');
@import url('fonts.css');
@import url('../../medias.css');

.brand-tgc {
	-webkit-font-smoothing: auto;

	& .title-heading,
	& h1 {
		font-family: var(--font-primary);
		font-size: var(--fs-20);
		font-weight: 700;
		line-height: 1.5;

		@media (--viewport-desktop) {
			font-size: var(--fs-30);
		}
	}

	& h2 {
		font-family: var(--font-secondary);
		font-size: var(--fs-20);
	}

	& .page-article-text {
		font-family: var(--font-secondary);
		font-size: var(--fs-16);
		font-weight: 400;
		line-height: 22px;

		& a {
			color: var(--c-brand-1);
			font-weight: bold;
		}
	}

	& .page-article-subtitle {
		font-weight: 900;
		font-size: var(--fs-16);
		line-height: 19px;
	}
}
