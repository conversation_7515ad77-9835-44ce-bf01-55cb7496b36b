@import url('fonts.css');
@import url('vars.css');
@import url('../../medias.css');

.brand-tel {
	-webkit-font-smoothing: auto;

	& .defaultBtn {
		display: flex;
		align-content: center;
		width: 100%;

		&-link,
		&-cta {
			padding: 16px 0 15px;
			line-height: 19px;
			font-weight: 600;
			display: block;
			border-radius: 50px;
			text-align: center;
			font-family: inherit;
			width: 100%;
			max-width: 300px;
			color: #fff;
			background: linear-gradient(135deg, #ff004b, #ee5b35);
			box-shadow: 0 10px 20px 0 rgb(246 47 67 / 26%);
			margin: 20px auto;
		}

		a {
			font-weight: bold;
			font-size: 15px;
		}
	}

	h1 {
		font-size: var(--fs-24);
	}

	h2 {
		font-size: var(--fs-20);
	}

	& .page-article-subtitle {
		font-size: var(--fs-15);
		line-height: 1.5;
		font-weight: 600;
	}

	& .page-article-text {
		font-size: var(--fs-15);
		line-height: 1.5;

		& a {
			font-weight: 600;
		}
	}
}
