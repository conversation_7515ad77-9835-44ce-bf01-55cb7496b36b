@import url('fonts.css');
@import url('vars.css');
@import url('../../medias.css');

.brand-voi {
	-webkit-font-smoothing: auto;
	-moz-osx-font-smoothing: auto;

	& .title-heading {
		text-align: center;
		font-size: var(--fs-22);
		line-height: 26px;
		font-weight: 500;
		font-family: var(--font-primary);

		@media (--viewport-desktop) {
			text-align: center;
			font-size: var(--fs-42);
			line-height: 54px;
		}
	}

	& .page-article-media-legend,
	& .page-article-media-copyright {
		font-size: var(--fs-14);
	}

	& .page-article-subtitle {
		font-family: var(--font-secondary);
		font-size: var(--fs-20);
		font-weight: 500;
		line-height: 24px;

		@media (--viewport-desktop) {
			font-size: var(--fs-22);
			line-height: 32px;
		}
	}

	& .page-article-text {
		font-size: var(--fs-18);
		line-height: 26px;
		font-weight: 300;

		& a {
			text-decoration: underline;
			font-weight: 500;
		}

		& strong {
			font-weight: 500;
		}
	}

	& h2 {
		font-size: var(--fs-20);

		@media (--viewport-desktop) {
			font-size: var(--fs-26);
		}
	}

	& .defaultBtn {
		font-size: 11px;
		font-weight: 500;
		text-align: center;
		line-height: normal;

		&-link,
		&-cta {
			margin: 20px auto;
			background-color: #ed1b32;
			border: 1px solid #ed1b32;
			color: #fff;
			transition: all 0.5s ease;
			display: inline-block;
			border-radius: 25px;
			padding: 10px 15px;
			text-transform: uppercase;
		}

		a {
			font-family: Heebo, sans-serif;
			font-weight: bold;

			&:hover {
				background-color: #dd1129;
				border-color: #dd1129;
			}
		}
	}

	.header-wrapper svg {
		fill: var(--c-brand-1);
	}
}
