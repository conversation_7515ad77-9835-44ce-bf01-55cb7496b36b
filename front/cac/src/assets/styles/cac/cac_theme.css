.strat2 ul {
	list-style: none;
	font-variation-settings: 'wght' var(--fontWeight-semibold);
}

.strat2 ul li {
	margin: 5px 0;
}

.strat2 ul a {
	color: var(--color-black);
}

.recette-internaute {
	background-image: url('../../images/macaron-recette-internaute.png');
	background-size: cover;
	width: 71px;
	height: 72px;
	display: block;
}

.slider-image .label-beta[data-target='#lightbox'] {
	position: absolute;
	left: 50%;
	transform: translateX(-50%) translateY(-50%);
	top: 50%;
	margin: 0;
	opacity: 0;
	z-index: 3;
	transition: opacity 0.6s;
}

.slider-image:hover .label-beta {
	opacity: 1;
	transition: opacity 0.6s;
}

.slider-image .label-eta {
	position: absolute;
	left: 15px;
	top: 15px;
	margin: 0;
}

.mtvp-widget-player-caption .viewport {
	width: auto !important;
	height: auto !important;
}

.mtvp-widget-player-caption .viewport ul {
	width: auto !important;
	height: auto !important;
}

.mtvp-widget-player-caption .viewport ul.listWrap.mosaic li:nth-of-type(n + 6) {
	display: block;
}

.mtvp-widget-player-caption .viewport ul.listWrap.mosaic li.hover {
	display: none;
}

.mtvp-widget-player-caption .caption .mtvptisy h3 {
	display: block;
	display: flex;
	font-size: var(--fontSize-xl);
	line-height: calc(var(--fontSize-xl) + 2);
	height: calc(calc(var(--fontSize-xl) + 2) * 2);
	margin: 0 auto;
	-webkit-line-clamp: 2;
	flex-direction: column;
	overflow: hidden;
	position: relative;
	white-space: initial;
}

.mtvp-widget-player-caption .caption .mtvptisy h3::after {
	content: '';
	text-align: right;
	position: absolute;
	bottom: 0;
	right: 0;
	width: 30%;
	height: calc(calc(var(--fontSize-xl) + 2) * 2);
	background: linear-gradient(to right, rgb(255 255 255 / 0%), #fff 80%);
}
