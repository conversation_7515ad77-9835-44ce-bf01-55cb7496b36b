.input-alpha,
.form-beta .form-search {
	border-radius: var(--borderRadius-md);
	border: 1px solid var(--color-gamma);
	color: var(--color-delta);
	padding: var(--margin-sm);
	line-height: 20px;
	width: 100%;
	max-width: 100%;
}

@media (--media-max-phone) {
	.form-control {
		display: block;
		width: 100%;
	}
}

label {
	display: inline-block;
	margin-bottom: var(--margin-xs);
}

.form-alpha {
	background-color: var(--color-main);
	border-radius: var(--borderRadius-md);
}

.form-alpha .title {
	color: var(--color-white);
}

.form-alpha .form-search {
	padding: 0 var(--margin-md);
	width: 100%;
	border: 0;
	height: 50px;
}

.form-alpha .search-submit {
	padding: 0 var(--margin-md);
	height: 50px;
	position: absolute;
	border: 0;
	right: 0;
	top: 0;
	background-color: var(--color-main);
	color: var(--color-white);
}

.form-alpha .form-inputs {
	margin-top: var(--margin-md);
	position: relative;
	border: 1px solid var(--color-white);
}

.form-beta {
	position: relative;
	line-height: 1;
}

.form-beta .form-search {
	padding-right: 30px;
}

.form-beta .search-submit {
	z-index: 4;
	position: absolute;
	right: 0;
	top: 0;
	font-size: 16px;
	color: var(--color-delta);
	background-color: transparent;
	height: 42px;
	float: left;
	line-height: 1;
	border: 0;
}

.form-beta .search-submit::before {
	vertical-align: middle;
}

.form-gamma {
	position: relative;
}

.form-gamma .form-search {
	padding: 0 var(--margin-md);
	padding-right: 120px;
	border-radius: var(--borderRadius-md);
	border: 1px solid var(--color-gamma);
	height: 50px;
}

.form-gamma .form-inputs {
	position: relative;
}

.form-gamma .search-submit {
	padding-right: var(--margin-md);
	padding-left: var(--margin-md);
	position: absolute;
	right: var(--margin-md);
	top: 11px;
	color: var(--color-main);
}

.form-delta {
	position: relative;
}

.form-delta .form-search {
	width: 0;
	float: right;
	border: 1px solid transparent;
	background-color: transparent;
	transition: width 0.6s ease, border 0.1s 0.3s ease, background-color 0.1s 0.3s ease;
}

.form-delta .form-search:focus {
	width: 100%;
	border: 1px solid var(--color-gamma);
	background-color: var(--color-white);
}

.form-delta .search-submit {
	position: absolute;
	right: var(--margin-md);
	top: 50%;
	border: 0;
	background-color: transparent;
	color: var(--color-delta);
	text-decoration: none;
}

.form-delta .search-submit:hover {
	color: var(--color-main);
}

.form-delta .search-submit:active ~ .form-search,
.form-delta .search-submit:focus ~ .form-search {
	width: 100%;
	border: 1px solid var(--color-gamma);
	background-color: var(--color-white);
	transition: width 0.6s ease, border 0.6s ease, background-color 0.6s ease;
}

@media (--media-max-phone) {
	.form-delta .form-search {
		width: 100%;
		border: 1px solid var(--color-gamma);
		background-color: var(--color-white);
	}
}

.form-epsilon .form-search {
	padding: 0 var(--margin-md);
	width: 100%;
	border: 0;
	height: 50px;
}

.form-epsilon .search-submit {
	padding: 0 var(--margin-md);
	height: 50px;
	position: absolute;
	border: 0;
	right: 0;
	top: 0;
	background-color: var(--color-main);
	color: var(--color-white);
}

.form-epsilon .form-inputs {
	margin-top: var(--margin-md);
	position: relative;
	border: 10px solid rgb(0 0 0 / 20%);
	border-radius: 4px;
}

.select-alpha {
	border: 0;
	cursor: pointer;
	color: var(--color-white);
	height: 50px;
	padding: 0 var(--grid-gutter-width) 0 var(--margin-md);
	background: url('../../images/arrow-down.png') no-repeat #000 right;
}

/* CUSTOM radio COLIBRI */
.custom-radio {
	position: relative;
}

/* on masque l'élément */
.custom-radio input[type='radio'] {
	position: absolute;
	left: -9999px;
}

/* on set le label correctement */
.custom-radio input[type='radio'] + label {
	cursor: pointer;
	display: block;
	width: auto;
}

/* le :before sert à créer la radio */
.custom-radio input[type='radio'] + label::after {
	/* background: url("../images/radio.png") no-repeat 0 -40px; */
	content: '';
	display: block;
	height: 25px;
	width: 25px;
	margin: 0 auto;
}

/* Si checked onchange le content ou autre */
.custom-radio input[type='radio']:checked + label::after {
	content: '';
	display: block;
	background-position: 0% 0;
}

.custom-radio.left input[type='radio'] + label::before {
	/* background: url("../images/radio.png") no-repeat 0 -40px; */
	content: '';
	display: inline-block;
	vertical-align: middle;
	height: 25px;
	width: 25px;
	margin-right: 10px;
}

.custom-radio.left input[type='radio']:checked + label::before {
	content: '';
	background-position: 0% 0;
}

.custom-radio.left input[type='radio'] + label::after {
	display: none;
}

/* CUSTOM SELECT include compatibility with FF before 35.0 */
.custom-select {
	overflow: hidden;
	position: relative;
	border: 1px solid var(--color-gamma);
	border-radius: 5px;
}

.custom-select::after {
	border-left: 9px solid transparent;
	border-right: 9px solid transparent;
	border-top: 10px solid var(--color-gamma);
	content: '';
	height: 0;
	margin-top: -4px;
	position: absolute;
	right: 10px;
	top: 50%;
	width: 0;
	pointer-events: none;
}

.custom-select select {
	background: transparent;
	width: 115%;
	padding: 10px;
	line-height: 1;
	border: 0;
	border-radius: 0;
	appearance: none;
}

.form-label-alpha {
	font-variation-settings: 'wght' var(--fontWeight-regular);
	line-height: 1;
}

@media (--media-min-desktop) {
	.form-label-alpha {
		text-align: right;
		display: block;
	}
}

.select-wrapper-alpha {
	position: relative;
	display: block;
}

.select-wrapper-alpha select {
	border-radius: var(--borderRadius-md);
	border: 1px solid var(--color-gamma);
	color: var(--color-delta);
	padding: var(--margin-sm);
	background-color: var(--color-white);
	width: 100%;
	line-height: 20px;
	appearance: none;
}

.select-wrapper-alpha::before {
	content: '';
	display: block;
	position: absolute;
	right: 2px;
	top: 2px;
	height: 37px;
	width: 46px;
	background-color: var(--color-white);
	pointer-events: none;
}

.select-wrapper-alpha::after {
	content: '';
	pointer-events: none;
	width: 0;
	height: 0;
	position: absolute;
	right: 15px;
	top: 15px;
	border-style: solid;
	border-width: 16px 10px 0;
	border-color: var(--color-gamma) transparent transparent transparent;
}

.radio-wrapper {
	display: inline-block;
	position: relative;
	padding-right: var(--margin-sm);
}

.radio-alpha {
	margin-top: 2px !important;
	margin-right: 5px !important;
	width: 20px;
	height: 20px;
	appearance: none;
	background-position: 0 20px;
	background-color: transparent;
	border: 0;
	border-radius: 0;
}

.radio-alpha:checked {
	background-position: 0 0;
}

.radio-alpha:focus {
	outline: 0 !important;
}

.checkbox-wrapper {
	display: inline;
	position: relative;
}

.checkbox-wrapper .form-label-alpha {
	text-align: left;
	margin-top: 0;
}

.checkbox-wrapper .checkbox {
	display: block;
	padding: 0;
}

.checkbox-wrapper .checkbox .checkbox-alpha {
	width: 27px;
	height: 30px;
	appearance: none;
	background-position: 0 30px;
	background-color: transparent;
	border: 0;
	float: none;
	display: inline-block;
	vertical-align: middle;
	border-radius: 0;
	margin: 0 5px 0 0 !important;
}

.checkbox-wrapper .checkbox .checkbox-alpha:checked {
	background-position: 0 0;
}

.checkbox-wrapper .checkbox .checkbox-alpha:focus {
	outline: 0 !important;
}

label.required::after {
	content: ' *';
}

.input-file-alpha {
	height: 33px;
	display: inline-block;
	vertical-align: middle;
	width: 180px;
}

.input-file-alpha .input-button-file {
	height: 33px;
	display: inline;
	position: absolute;
	cursor: pointer;
	text-align: center;
	line-height: 33px;
}

.input-file-alpha .input-button-file::before {
	background: var(--color-main);
	border-radius: 5px;
	content: attr(data-label);
	color: #fff;
	position: absolute;
	pointer-events: none;
	left: 0;
	right: 0;
	text-align: center;
	cursor: pointer;
	width: 180px;
	z-index: 1;
}

.input-file-alpha .input-file {
	position: relative;
	height: 33px;
	opacity: 0;
	display: inline;
	cursor: pointer;
	width: 180px;
}

.input-file-alpha .input-filename {
	display: inline;
	margin-left: var(--margin-sm);
}

.input-group-addon-before {
	color: #fff;
	border-radius: var(--borderRadius-xl);
	background-color: var(--color-main);
	border: 0;
}

.input-group-addon-before:first-child {
	border-radius: var(--borderRadius-xl);
}

.input-group-addon-after {
	vertical-align: top;
	border: 0;
	background-color: transparent;
	font-size: 1.9em;
	color: var(--color-delta);
}

.input-group-btn .btn-lg {
	padding: 19px 20px;
	width: 100%;
}

@media (--media-min-phone) {
	.input-group {
		padding-bottom: 0;
	}

	.input-group-btn {
		width: 1%;
		position: relative;
		padding-left: 0 !important;
		top: 0;
	}

	.input-group-btn .btn-lg {
		width: auto;
	}
}

.picker__month,
.picker__year,
.picker__nav--next,
.picker__nav--prev {
	display: inline-block;
	padding: 0 5px;
}

.picker {
	opacity: 0;
	pointer-events: none;
	position: absolute;
	top: 100%;
	width: 100%;
	background-color: var(--color-white);
	border: 1px solid var(--color-gamma);
	padding: 5px;
}

.picker__table {
	width: 100%;
	text-align: center;
}

.picker--opened {
	z-index: 10;
	pointer-events: auto;
	opacity: 1;
}

.picker__header {
	text-align: center;
	position: relative;
	border-bottom: 1px solid var(--color-gamma);
	padding: 5px 0;
}

.picker__nav--prev::before {
	content: '';
	display: block;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 5px 10px 5px 0;
	border-color: transparent var(--color-main) transparent transparent;
}

.picker__nav--next::before {
	content: '';
	display: block;
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 5px 0 5px 10px;
	border-color: transparent transparent transparent var(--color-main);
}

.picker__nav--prev,
.picker__nav--next {
	top: 10px;
	position: absolute;
}

.picker__nav--prev {
	left: 10px;
}

.picker__nav--next {
	right: 10px;
}
