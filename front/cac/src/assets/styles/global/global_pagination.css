.pagination {
	list-style: none;
	padding: 0;
	margin: 0;
}

.pagination .item {
	border: 1px solid var(--color-gamma);
	background-color: var(--color-alpha);
	text-transform: uppercase;
	line-height: 1;
	color: var(--color-main);
	font-size: var(--fontSize-sm);
	display: inline-block;
	padding: 0;
	vertical-align: middle;
	border-radius: var(--borderRadius-md);
}

.pagination .item:hover {
	text-decoration: none;
	border-color: var(--color-main);
}

.pagination .item.active {
	text-transform: none;
	border: 0;
	background: none;
	font-size: var(--fontSize-md);
	color: var(--color-black);

	.itemLabel {
		pointer-events: none;
		cursor: default;
	}
}

.pagination .item .itemLabel {
	text-decoration: none;
	padding: 1px 5px 5px;
	display: block;
	color: var(--color-main);
}

.pagination .page-left-sm::before {
	content: '<';
	float: left;
	font-size: var(--fontSize-md);
	position: relative;
	top: -1px;
	margin-right: var(--margin-xs);
	display: inline-block;
	vertical-align: middle;
}

.pagination .page-right-sm::after {
	content: '>';
	float: right;
	font-size: var(--fontSize-md);
	position: relative;
	top: -1px;
	margin-left: var(--margin-xs);
	display: inline-block;
	vertical-align: middle;
}

.pagination .page-left-xs {
	font-size: var(--fontSize-md);
}

.pagination .page-right-xs {
	font-size: var(--fontSize-md);
}

.pagination-list-md {
	font-size: var(--fontSize-sm);
	margin-bottom: var(--margin-xs);
}

.pagination-list-md .item {
	font-size: var(--fontSize-sm);
	margin-bottom: 5px;
}

.pagination-list-md .item span,
.pagination-list-md .item a {
	padding: 10px;
	display: block;
}

.pagination-list-md .item.active {
	border: 1px solid var(--color-gamma);
	background-color: var(--color-alpha);
	font-variation-settings: 'wght' var(--fontWeight-semibold);
}

.pagination-list-sm {
	font-size: var(--fontSize-sm);
	margin-bottom: var(--margin-xs);
}

.pagination-list-sm .item {
	font-size: var(--fontSize-sm);
}

.pagination-list-sm .item.active {
	border: 1px solid var(--color-gamma);
	background-color: var(--color-alpha);
	font-variation-settings: 'wght' var(--fontWeight-semibold);
}

.pagination-list {
	font-size: var(--fontSize-sm);
	margin: var(--margin-md) 0 var(--margin-sm);
}

.pagination-list .item {
	font-size: var(--fontSize-sm);
	width: auto !important;
	height: auto !important;
}

.pagination-list .item.active {
	border: 1px solid var(--color-gamma);
	background-color: var(--color-alpha);
	font-variation-settings: 'wght' var(--fontWeight-semibold);
}
