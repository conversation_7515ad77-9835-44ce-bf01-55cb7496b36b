.popinBackdrop-1-0-2 {
	padding: 0 !important;

	.popinAlertWrapper-1-0-3 {
		max-width: 600px;
		padding: 45px var(--margin-mdp) var(--margin-mdp);

		label {
			display: flex;
			align-items: center;

			input {
				appearance: none;
				flex-shrink: 0;
				height: 17px;
				width: 17px;
				background-color: #fff;
				border: 1px solid var(--color-darkBackground);
				margin: 0 var(--margin-md) 0 0;

				&:disabled {
					opacity: 0.5;
				}

				&:checked {
					background: linear-gradient(90deg, var(--color-ctaMainDark), var(--color-ctaMain));
					position: relative;
					border-width: 1px;

					&::after {
						content: ' ';
						position: absolute;
						inset: 0;
						border: 1px solid var(--color-white);
					}
				}
			}
		}
	}

	.bigText-1-0-4 {
		color: var(--color-text);
		font-family: 'Libre Baskerville', serif;
		font-size: var(--fontSize-md);
		letter-spacing: 0;
		line-height: 24px;
		max-width: 300px;
		margin: 0 auto var(--margin-md);

		@media (--media-min-lg-tablet) {
			font-size: var(--fontSize-xl);
			line-height: 34px;
			margin: auto;
		}
	}

	.smallText-1-0-5,
	.extraSmallText-1-0-6 {
		color: var(--color-text);
		font-family: var(--fontFamily-base);
		font-size: var(--fontSize-xxs);
		line-height: 16px;
		letter-spacing: 0;
		text-align: left;

		@media (--media-min-lg-tablet) {
			font-size: var(--fontSize-xs);
			line-height: 18px;
		}
	}

	.buttonDefault-1-0-7 {
		width: 100%;

		@media (--media-min-lg-tablet) {
			width: auto;
		}
	}
}
