import Toaster from 'commonSrc/views/components/stickyBanner/config'
import StickyInsert from 'commonSrc/views/components/stickyBanner/assets/scripts/stickyInsert'
import RecipeIngredients from 'sourceViews/components/recipeIngredients/assets/scripts/script'
import { config as stickyConfig } from './sticky-config'
import { init as initCoachingNav } from 'sourceViews/components/coachingNav'
import ServiceInterface from 'sourceViews/components/serviceBar'
import commonHandlers from './utils'
import TrackElements from 'commonSrc/assets/scripts/utils/trackElements'
import 'sourceAssets/scripts/videoPlayer'
import generateScriptElement from '@capsule/generate-script-element'
import Observer from '@capsule/observer'

main()

function main() {
	pageView()
	adStickyContainer()
	loadCookModeModule()
	loadIngredientModule()
	loadFemServicesReviewer()
	initStickyInsert()
	initCoachingNav()
	initServices()
	trackingPrintButton()
}
function pageView() {
	let pageView = parseInt(window.sessionStorage.getItem('pageView'))
	if (isNaN(pageView)) {
		window.sessionStorage.setItem('pageView', 1)
		return
	}

	pageView++
	window.sessionStorage.setItem('pageView', pageView)
}

function initServices() {
	const services = new ServiceInterface()
	services.init()
}

function loadIngredientModule() {
	const recipeIngredients = document.querySelector('[data-recipeIngredients]')
	const recipeIngredientsYield = document.querySelector('[data-recipeIngredients-yield]')
	if (recipeIngredients && recipeIngredientsYield) {
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const _recipeIngredients = new RecipeIngredients(recipeIngredients, {
			input: '[data-recipeIngredients-yieldInput]',
			min: 1,
			max: 1000,
			floatRanges: {
				0.25: [0, 5],
				0.5: [5, 10]
			}
		})
	}
}

function loadCookModeModule() {}

function initStickyInsert() {
	if (window.matchMedia('(max-width: 768px)').matches) {
		const toasterDiapo = document.querySelector('#fem-diapo')
		let stickyConfigWithDiapoConfig = []
		if (toasterDiapo) {
			stickyConfigWithDiapoConfig = addDiapoConfig(stickyConfig)
		}
		const stickyInsert = new StickyInsert(new Toaster())
		stickyInsert.init({
			content: document.querySelector('[data-recipecontent]'),
			config: !toasterDiapo ? stickyConfig : stickyConfigWithDiapoConfig
		})
	}
}

function adStickyContainer() {
	let adContainer
	if (
		window.outerWidth > 990 &&
		(adContainer = document.querySelectorAll('[data-ad-sticky-container]'))
	) {
		const markedBlocks = [...document.querySelectorAll('[data-ad-sticky-marker]')]
		adContainer.forEach((elt, index) => {
			if (index < adContainer.length - 1 && adContainer.length > 1) {
				elt.style.height =
					markedBlocks.reduce(
						(totalHeight, currentBlock) => totalHeight + currentBlock.offsetHeight + 58,
						100
					) + 'px'
				elt.style.marginBottom = '15px'
			}
		})
	}
}

function loadFemServicesReviewer() {
	const reviewer = document.querySelector('fem-reviewer')

	const reviewerScript = generateScriptElement({
		src: window.globalConfigs.recipeServices.reviewer,
		onLoadCallback: submitReviewListener
	})

	const reviewerObserver = new Observer({
		elements: reviewer,
		onIntersection: () => {
			document.head.appendChild(reviewerScript)
			submitReviewListener()
		}
	})

	reviewerObserver.observe()
}

function submitReviewListener() {
	document.querySelector('fem-reviewer').addEventListener('rating:submit', function () {
		const recipeReviews = document.querySelector('.recipeReviews')
		const recipeReviewsCount = document.querySelector('.recipeReviews-count')

		if (recipeReviewsCount.innerHTML === '0') {
			recipeReviews.classList.remove('displayNone')
			recipeReviewsCount.innerHTML = '1'
		}
	})
}

function addDiapoConfig(stickyConfig) {
	const handlers = commonHandlers.handlers
	const cloneStickyConfig = stickyConfig

	const diapoConfig = {
		name: 'diaporama',
		elementSelector: '#fem-diapo',
		timerHide: 30000,
		offset: 70,
		capping: {
			name: 'pageView',
			test: (pageView) => {
				if (pageView === 1 || pageView > 2) {
					return true
				}
				return false
			}
		},
		tracking: {
			display: {
				eventCategory: 'Toaster mobile sticky',
				eventAction: 'display_toaster_diapo_recette',
				eventLabel: null,
				nonInteraction: 1
			},
			clickOnToaster: {
				eventCategory: 'Toaster mobile sticky',
				eventAction: 'clic_toaster_diapo_recette',
				eventLabel: null
			},
			close: {
				eventCategory: 'Toaster mobile sticky',
				eventAction: 'close_toaster_diapo_recette',
				eventLabel: null,
				nonInteraction: 1
			}
		},
		handlers
	}
	if (!diapoConfig) {
		return false
	}

	cloneStickyConfig.unshift(diapoConfig)
	return cloneStickyConfig
}

function trackingPrintButton() {
	const buttonPrint = document.querySelector('[data-print]')

	const elementsToTrack = [
		{
			element: buttonPrint,
			tracking: {
				click: {
					ga4: {
						event: 'button_click',
						button_name: 'cta-imprimer-la-recette',
						button_URL: window.location.href
					}
				}
			}
		}
	]

	const tracking = new TrackElements(elementsToTrack)
	tracking.init()

	buttonPrint.addEventListener('click', () => {
		// add setTimeout to prevent INP
		setTimeout(function () {
			window.print()
		}, 1)
	})
}
