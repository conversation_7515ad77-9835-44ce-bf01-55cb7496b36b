{% extends '@Front/src/views/templates/default/default.html.twig' %}

{% block metaTitleContent %}
  Cuisine actuelle : toutes vos recettes de cuisine - mes notifications - Cuisine Actuelle
{% endblock %}
{% block metaDescriptionContent %}
  Cuisine actuelle : toutes vos recettes de cuisine
{% endblock %}

{% set specificScripts = [
  scds_url('pmc-kit-components/notificationsCenterBundle.js'),
] %}

{% block scriptTops %}
	{{ parent() }}
	{{ include('@assets/modern/templates/notifications-scripts.html.twig') }}
  {{ include('@assets/legacy/templates/notifications-scripts.html.twig', ignore_missing = true) }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ include('@assets/modern/templates/notifications-styles.html.twig') }}
{% endblock %}

{% block spriteSvg %}
    <div class="spritesvg">
        {{ source('@web' ~ asset('sprites/notifications.svg'), ignore_missing = true) }}
    </div>
{% endblock %}

{% set isNotificationCenter = true %}

{% block defaultContainer %}
  <nav aria-labelledby="breadcrumb">
    <ol class="breadcrumb" id="breadcrumb" data-block="breadcrumb">
      <li class="breadcrumb-item" role="none">
        <a class="breadcrumb-link" href="" tabindex="-1" title="Revenir à mon profil">Mon profil</a>
      </li>
      <li class="breadcrumb-item" role="none">
        <a class="breadcrumb-link" href="{{ path('app_cac_notifications__invoke') }}" tabindex="-1">
          Mes préférences de communication
        </a>
      </li>
    </ol>
  </nav>

  <div class="notifications greyContainer">
    <div data-pmc-notification-center></div>
  </div>
{% endblock %}
