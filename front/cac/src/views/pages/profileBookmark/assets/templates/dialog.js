export const dialog = /* html */ `
  <dialog class="dialog-category" data-dialog style="display: none;">
    <svg class="dialog-closeButton" data-dialog-close width="14" height="14"><path d="M.592.008c-.15 0-.307.05-.42.164a.59.59 0 0 0 0 .831L6.167 7 .171 12.997a.59.59 0 0 0 0 .831.59.59 0 0 0 .832 0L7 7.832l5.997 5.996a.59.59 0 0 0 .832 0 .59.59 0 0 0 0-.831L7.832 7l5.997-5.997a.59.59 0 0 0 0-.831.59.59 0 0 0-.832 0L7 6.168 1.003.172A.572.572 0 0 0 .592.008z"/></svg>
    <div class="dialog-container">
    </div>
  </dialog>
`

export function displayForm({ type, id }) {
	if (!type) {
		return ''
	}

	const dialogTemplates = {
		new: {
			title: 'Créer une categorie',
			buttonLabel: '+ <PERSON><PERSON><PERSON>',
			hiddenInput: false
		},
		edit: {
			title: 'Modifier la categorie',
			buttonLabel: 'Valider',
			hiddenInput: true
		},
		delete: {
			title: 'Supprimer la recette de mon carnet de recette ?',
			buttonLabel: 'Supprimer',
			hiddenInput: true
		}
	}

	const { title, buttonLabel, hiddenInput } = dialogTemplates[type]

	const section =
		type !== 'delete'
			? `<section>
      <label for="category">Nom de la categorie</label>
      <input type="text" id="input-category" name="category" maxlength="50" placeholder="Ex: Idée Noël ou Végétarien"/>
      ${hiddenInput ? `<input type="hidden" id="input-id" name="boardId" value="${id}"/>` : ''}
    </section>`
			: `${
					hiddenInput
						? `<input type="hidden" id="input-id" name="boardId" value="${id}"/>`
						: ''
			  }`

	const dialog = /* html */ `
    <h2>${title}</h2>
    <form method="dialog">
    ${section}
      <footer>
        <button data-dialog-cancel class="neutral">Annuler</button>
        <button data-dialog-submitButton>${buttonLabel}</button>
      </footer>
    </form>
  `

	return dialog
}
