import consentCheck from '@capsule/consent-check'
import PmcBridge from 'commonSrc/assets/scripts/utils/pmcBridge'
import Observer from '@capsule/observer'
import generateScriptElement from '@capsule/generate-script-element'

const sponsoredContainer = document.querySelector('[data-sponsored]')

const pmcBridge = new PmcBridge()
const isPremium = await pmcBridge.isPremium()
const outbrainBlocks = [...document.querySelectorAll('.sponsoredPrisma')]

if (isPremium) {
	outbrainBlocks.forEach((item) => {
		item.style.display = 'none'
	})
} else if (sponsoredContainer) {
	consentCheck('allConsentGiven', () => {
		lazyLoadOutbrain()
	})
	consentCheck('adsConsentGiven', () => {
		lazyLoadOutbrain()
	})
}

function lazyLoadOutbrain() {
	const outbrainScript = generateScriptElement({
		src: sponsoredContainer.getAttribute('data-src')
	})
	const outbrainObserver = new Observer({
		elements: sponsoredContainer,
		onIntersection: () => {
			sponsoredContainer.appendChild(outbrainScript)
		}
	})

	outbrainObserver.observe()
}
