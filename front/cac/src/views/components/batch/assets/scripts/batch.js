import BatchSdk from '@capsule/batch-sdk'
import consentCheck from '@capsule/consent-check'

/**
 * Batch
 * @module Batch
 */
export default class Batch {
	/**
	 * Inject method to load the Batch SDK
	 */
	init() {
		consentCheck('allConsentGiven', () => {
			this.batchSdk = new BatchSdk({
				config: this.config,
				onReady: this.onBatchReady
			})

			this.batchSdk.init()
		})
	}

	/**
	 * onBatchReady
	 */
	onBatchReady() {
		document.dispatchEvent(new Event('BATCH:init'))
	}

	/**
	 * Get batch config for init
	 */
	get config() {
		return {
			setup: {
				defaultIcon: `https://www.cuisineactuelle.fr/apple-touch-icon-180x180.png`,
				smallIcon: `https://www.cuisineactuelle.fr/android-icon-96x96.png`,
				ui: { native: {} },
				...(window.location.host === 'www.cuisineactuelle.fr'
					? {
							apiKey: '6C796F7341C146749A3279FBCC5FA353',
							subdomain: 'cuisineactuelle2',
							authKey: '2.HfsfPXfJw1HHsZNWNYkJ4dNc8HS+VlVFQPcVyN5PNRQ=',
							vapidPublicKey:
								'BLmA1iJZRfOMZ6uYMd6gPzrUpX0EnmFTb6h+xgPgIYBYZ429jfK1yaX4hqt8gCRYSYKK4ujTH78SARwkwQcWe1k='
					  }
					: {
							apiKey: '0D02BB6FC9804B89A49168AC2A42F916',
							subdomain: 'cuisineactuelle1',
							authKey: '2.BqAt6DrYO2b5ypVHbCEjtUNh81CUSw7/llV59HaYQT8=',
							vapidPublicKey:
								'BBQs3kk5U82cXBjR++byEpoaWcrojTpF4uldB+DRV1d9X8xi6LkGbdEMuZtheiTKOCqJ2Ndq7n6q8ZvoUHD8tGA='
					  })
			},
			showNotification: {
				title: 'Merci pour votre inscription aux alertes Cuisine Actuelle.',
				options: {
					body: 'La rédaction sélectionne les meilleures news pour vous !',
					data: {
						title: ' ',
						alert: ' ',
						'com.batch': {
							da: {
								a: 'batch.deeplink',
								args: {
									l: 'https://www.cuisineactuelle.fr/#utm_source=edito&utm_medium=push-web&utm_campaign=welcome',
									newTab: true
								}
							}
						}
					}
				}
			}
		}
	}
}
