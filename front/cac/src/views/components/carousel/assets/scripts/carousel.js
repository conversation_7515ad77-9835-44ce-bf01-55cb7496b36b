class Carousels {
	constructor() {
		this.controls = {
			prev: null,
			next: null
		}

		this.handleIntersection = this.handleIntersection.bind(this)
		this.carouselsObserver = null
	}

	init() {
		const carousels = document.querySelectorAll('[data-carousel]')
		const options = {
			root: null,
			rootMargin: '0px 0px 0px 0px',
			threshold: 0
		}
		this.carouselsObserver = new IntersectionObserver(this.handleIntersection, options)
		carousels.forEach((carousel) => this.carouselsObserver.observe(carousel))
	}

	handleIntersection(carousels) {
		const isTouchScreen = 'ontouchstart' in window

		for (const carousel of carousels) {
			if (carousel.isIntersecting) {
				this.renderCarousel(carousel.target)
				const isSimpleslider = carousel.target.querySelector('[data-simpleslider]')
				const scroller = carousel.target.querySelector('.carousel-slides')
				const hasOverflow = scroller?.scrollWidth > scroller?.clientWidth

				if (!isTouchScreen && hasOverflow && !isSimpleslider) {
					import('./carouselControls').then(({ default: CarouselControls }) => {
						new CarouselControls(carousel.target).init()
						window.widerLink.reload()
					})
				}

				if (isSimpleslider) {
					import('./simpleSlider').then(({ default: SimpleSlider }) => {
						new SimpleSlider(carousel.target).init()
					})
				}

				this.carouselsObserver.unobserve(carousel.target)
			}
		}
	}

	renderCarousel(carousel) {
		const template = carousel.querySelector('template[data-tmp-carousel]')
		carousel.appendChild(template.content.cloneNode(true))
	}
}

const carousels = new Carousels()
carousels.init()
