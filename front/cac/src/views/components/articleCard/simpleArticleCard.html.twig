{% set url = url is defined ? url : null %}
{% set text = text is defined ? text : null %}
{% set picture = picture is defined ? picture : null %}
{% set titleTag = titleTag is defined ? titleTag : 'div' %}

{% set classes = classes|default([])|merge(['articleCard']) %}
{% set attributes = attributes|default([])|merge([
    'data-wide-target',
    'data-block="articleCard"'
]) %}

<article class="simpleArticleCard {{ classes|join(' ') }}" {{ attributes|join(' ')|raw }}>
    {% if picture %}
    <picture>
        <img
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
            data-src="{{ pmd_common_image_url_string(picture, 768, {'crop-from': 'center', ratio: 16 / 8, title: text|escape}) }}" alt="{{ text|escape }}"
            class="articleCard-image lazyload"
        >
    </picture>
    {% endif %}

    <div class="articleCard-body">
        <{{ titleTag }} class="articleCard-title">
            <a class="articleCard-link" data-wide href="{{ url }}">{{ text }}</a>
        </{{ titleTag }}>
    </div>
</article>
