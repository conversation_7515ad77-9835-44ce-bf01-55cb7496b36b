.leadImage {
	&-image,
	&-video {
		position: relative;
		z-index: 190;
	}

	&-image {
		position: relative;

		img {
			position: absolute;
			top: 0;
			display: block;
			width: 100%;
			height: 100%;
			background-color: var(--color-greyLight);
			z-index: 1;
		}
	}

	&-imageMeta {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		padding: 0 12px 12px 9px;
		font-size: 1rem;
		color: #fff;
		z-index: 3;
		text-align: right;

		@media (--media-min-desktop) {
			padding: 0 30px 20px 18px;
			font-size: 1.2rem;

			& > span {
				padding-left: 45px;
				text-align: right;
			}
		}
	}
}

.leadVideo {
	display: flex;
	flex-direction: column;

	@media (--media-min-desktop) {
		flex-direction: row;
		padding: 0 var(--margin-md);
	}

	.leadImage.isVideo {
		flex-basis: 66.66%;
		padding: 0;
		margin-bottom: 0;
	}

	.playlist {
		background-color: var(--color-alpha);
		padding-top: 7px;
		padding-left: 15px;
		flex-basis: 33.33%;
		min-height: 170px;

		&-list,
		&-list-skeleton {
			display: flex;
			list-style-type: none;
			padding: 0;
			margin: 0;
			overflow: auto hidden;

			@media (--media-min-desktop) {
				display: block;
				max-height: 260px;
				overflow: hidden auto;
			}

			@media (--media-min-desktop) {
				display: block;
				max-height: 360px;
			}
		}

		&-listItem {
			margin: 0 7px var(--space-xs) 0;

			@media (--media-min-desktop) {
				margin-right: 0;
			}
		}

		&-title {
			font-size: var(--fontSize-sm);
			font-variation-settings: 'wght' var(--fontWeight-regular);
			margin: 0 0 var(--space-sm);
		}

		.videoCard {
			display: flex;
			flex-direction: column;
			padding: 0;
			width: 140px;
			border: 0;
			border-radius: var(--borderRadius-md);
			background: 0;
			min-height: 100%;

			* {
				pointer-events: none;
			}

			@media (--media-min-desktop) {
				flex-direction: row;
				width: 100%;
				height: 80px;
				margin-right: 0;
				background: var(--color-white);
			}

			&-infos {
				display: flex;
				flex: 1;
				flex-direction: column;
				width: 100%;
				background: var(--color-white);
				padding: var(--space-xs) var(--space-sm);
				text-align: left;
				border-bottom-left-radius: var(--borderRadius-md);
				border-bottom-right-radius: var(--borderRadius-md);

				@media (--media-min-desktop) {
					border-top-right-radius: var(--borderRadius-md);
					border-bottom-right-radius: var(--borderRadius-md);
					border-bottom-left-radius: 0;
				}
			}

			&-title {
				font-size: var(--fontSize-sm);
				line-height: 20px;
				max-height: 40px;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
			}

			&-label {
				align-self: flex-start;
				font-size: var(--fontSize-xs);
				font-weight: 600;
				line-height: 16px;
				padding: 1px 4px;
				border-radius: 4px;
				color: #fff;
				margin-bottom: 6px;
			}

			&-img {
				max-width: 140px;
				max-height: 80px;
				object-fit: cover;
				border-top-left-radius: var(--borderRadius-md);
				border-top-right-radius: var(--borderRadius-md);

				@media (--media-min-desktop) {
					border-top-left-radius: var(--borderRadius-md);
					border-bottom-left-radius: var(--borderRadius-md);
					border-top-right-radius: 0;
				}
			}

			&-poster {
				display: flex;
				position: relative;
				width: 140px;
				height: 80px;
			}

			&-duration {
				position: absolute;
				left: 0;
				bottom: 0;
				font-size: var(--fontSize-xxs);
				font-variation-settings: 'wght' var(--fontWeight-medium);
				color: var(--color-white);
				background: rgb(0 0 0 / 30%);
				border-radius: 2px;
				padding: 2px 3px;
			}

			&-label-skeleton,
			&-title-skeleton,
			&-poster-skeleton {
				background: #eee;
				background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
				border-radius: 5px;
				background-size: 200% 100%;
				animation: 1.5s loadFX linear infinite;
			}

			&-label-skeleton {
				width: 50%;
				height: 20px;
				margin-bottom: 6px;
			}

			&-title-skeleton {
				width: 100%;
				height: 20px;
			}

			&-poster-skeleton {
				width: 140px;
				height: 80px;
			}

			@keyframes loadFX {
				to {
					background-position-x: -200%;
				}
			}
		}

		.videoCategory {
			&-recipe {
				&.active {
					border: 2px solid var(--color-recipe);
					border-radius: 7px;
				}

				& .videoCard-label {
					background-color: var(--color-recipe);
				}
			}

			&-technicalGesture {
				&.active {
					border: 2px solid var(--color-technicalGesture);
					border-radius: var(--borderRadius-md);
				}

				& .videoCard-label {
					background-color: var(--color-technicalGesture);
				}
			}
		}
	}
}
