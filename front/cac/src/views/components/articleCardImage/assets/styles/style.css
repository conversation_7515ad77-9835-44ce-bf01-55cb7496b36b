.articleCardImage {
	position: relative;
	width: 100%;
	cursor: pointer;
	background-color: #f8f9fa;

	&-icon {
		position: absolute;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2;
		left: 16px;
		top: 16px;

		svg {
			height: 24px;
			width: 24px;
			fill: var(--color-white);
		}
	}

	&::before {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	&-img {
		display: block;
		max-width: 100%;
		height: 100%;
		width: 100%;

		&.lazyloaded {
			position: absolute;
		}
	}
}
