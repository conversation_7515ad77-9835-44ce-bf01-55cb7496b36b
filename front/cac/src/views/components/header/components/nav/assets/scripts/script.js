import TrackElements from 'commonSrc/assets/scripts/utils/trackElements'
import getDeviceType from 'commonSrc/assets/scripts/utils/getDeviceType'

class HeaderNav {
	constructor() {
		this.headerNav = document.querySelector('[data-header-nav]')
	}

	init() {
		this.randomRecipe()
		this.trackingNav()
		this.addEventListeners()
	}

	randomRecipe() {
		const randomButton = document.querySelector('[data-random-recipe]')

		if (randomButton) {
			const randomInt = this.getRandomInt(1, 1000)
			const linkSplited = randomButton.dataset.href.split('=')
			linkSplited[1] = randomInt
			const link = linkSplited.join('=')
			randomButton.setAttribute('href', link)
		}
	}

	trackingNav() {
		const submenu = [
			...document.querySelectorAll(
				'.headerNav-listLink[data-tracking]:not(.onMobile .headerNav-listLink.pmc-auth-required)'
			),
			...document.querySelectorAll('.headerMain-cta[data-tracking]')
		]

		submenu.forEach((entry) => {
			entry.addEventListener('click', () => {
				window.ga('send', 'event', {
					eventCategory: 'Navigation menu',
					eventAction: `Clic-entree-${entry.dataset.tracking}`,
					eventLabel: getDeviceType()
				})
			})
		})

		const BtnNlMenuBurger = '[data-header-nav] [data-block="headerNav-newsletterLink"]'
		const magazineElement = document.querySelector('[data-header-magazine] a')
		const magazineElementMobile = document.querySelector('[data-header-magazine-mobile] a')
		const magazineUrl = magazineElement.getAttribute('href') || ''
		const magazineUrlMobile = magazineElement.getAttribute('href') || ''
		const elementsToTrack = [
			{
				element: document.querySelector(BtnNlMenuBurger),
				tracking: {
					click: {
						ga4: {
							event: 'button_click',
							button_name: 'cta-nl-menu-burger'
						}
					}
				}
			},
			{
				element: magazineElement,
				tracking: {
					click: {
						ga4: {
							event: 'button_click',
							button_name: 'cta-abo',
							button_URL: magazineUrl
						}
					}
				}
			},
			{
				element: magazineElementMobile,
				tracking: {
					click: {
						ga4: {
							event: 'button_click',
							button_name: 'cta-abo',
							button_URL: magazineUrlMobile
						}
					}
				}
			}
		]

		const tracking = new TrackElements(elementsToTrack)
		tracking.init()
	}

	addEventListeners() {
		this.headerNav.addEventListener('mouseover', (event) => {
			if (this.shouldAddActiveState(event.target)) {
				this.handleListLinkOver(event)
			}
		})

		let openItem = null
		this.headerNav.addEventListener('click', (event) => {
			this.displayImagesMenu(event)
			if (event.target.matches('button')) {
				if (openItem) {
					openItem.classList.remove('isOpen')
				}
				const listItem = event.target.parentElement
				if (openItem !== listItem) {
					listItem.classList.add('isOpen')
					openItem = listItem
				} else {
					openItem = null
				}
			}
		})
	}

	handleListLinkOver(event) {
		this.displayImagesMenu(event)
		const listItem = event.target.matches('.headerNav-listItem')
			? event.target
			: event.target.parentElement
		listItem.classList.add('isActive')

		listItem.addEventListener('mouseleave', this.handleMouseLeave.bind(this))
	}

	shouldAddActiveState(target) {
		return (
			target.classList.contains('hasSubmenu') ||
			(target.matches('.headerNav-listLink') &&
				target.parentElement.classList.contains('hasSubmenu'))
		)
	}

	handleMouseLeave(event) {
		event.target.classList.remove('isActive')
		event.target.removeEventListener('mouseleave', this.handleMouseLeave.bind(this))
	}

	displayImagesMenu(event) {
		const target = event.target.dataset.submenu
		const templateMenu = [...document.querySelectorAll(`[data-image='${target}']`)]
		templateMenu.forEach((template) => {
			template.after(template.content.cloneNode(true))
			template.remove()
		})
	}

	getRandomInt(min, max) {
		min = Math.ceil(min)
		max = Math.floor(max)
		return Math.floor(Math.random() * (max - min)) + min
	}
}

export default HeaderNav
