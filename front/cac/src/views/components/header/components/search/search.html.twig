{% import '@CommonFront/macros/svg/views/svg.html.twig' as macroSvg %}

<section class="headerSearch" data-header-searchbar>
  <form class="headerSearch-searchForm" action="{{ path('pmd_cac.search') }}" method="get">
    {{ macroSvg.getSvgTag('search') }}
    <input type="text"
      name="search[keyword]"
      class="headerSearch-searchInput"
      placeholder="Quelle recette recherchez-vous ?"
      autocomplete="off" />
    <input type="hidden" name="search[sort]" value="ASC" />
  </form>

  {% if app.request.get('_route') == "pmd_common_search" %}
    <div class="advancedSearch" data-tracking-v4="trigger:click//event:button_click//button_name:recherche avancée">
      {{ macroSvg.getSvgTag('settings') }}
      <span data-advancedSearch-toggle>Recherche avancée</span>
    </div>
  {% endif %}
</section>
<template data-advancedSearch>
  {% include '@Front/src/views/components/advancedSearch/advancedSearch.html.twig' %}
</template>
