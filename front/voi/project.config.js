'use strict'

const path = require('path')

const pathFront = path.resolve(__dirname, '../')
const voi = `${pathFront}/voi`
const pathSrc = `${voi}/src`
const pathcommonSrc = `${pathFront}/common/src`
const pathNodeModules = `${pathFront}/node_modules`

module.exports = {
	webpack: {
		publicPath: '/assets/voi',
		loaderIncludeRule: [
			voi,
			pathSrc,
			pathcommonSrc,
			`${pathNodeModules}/@capsule`,
			`${pathNodeModules}/@prismamedia/bvt`,
			`${pathNodeModules}/@prismamedia/diapo-in-article`,
			`${pathNodeModules}/@prismamedia/datadog-client`
		],
		chunksWebpackPlugins: {
			domain: 'app.request.getSchemeAndHttpHost()',
			entriesWithDomain: ['partials']
		},
		postcss: {
			variableFilePath: [`${pathSrc}/assets/styles/vars.css`]
		},
		alias: {
			voi,
			commonSrc: pathcommonSrc,
			Pages: `${pathSrc}/views/pages`,
			Assets: `${pathSrc}/assets`,
			Shared: `${pathSrc}/views/shared`,
			Vendors: pathNodeModules
		}
	},
	entries: {
		site: {
			main: [`${pathSrc}/views/main`],
			global: [`${pathSrc}/views/global`],
			home: [`${pathSrc}/views/pages/home/<USER>
			article: [`${pathSrc}/views/pages/article/config`],
			articleLive: [`${pathSrc}/views/pages/article/types/articleLive/config`],
			articleDiaporama: [`${pathSrc}/views/pages/article/types/articleDiaporama/config`],
			articleLiveApi: [`${pathSrc}/views/pages/articleLiveApi/config`],
			author: [`${pathSrc}/views/pages/author/config`],
			orphan: [`${pathSrc}/views/pages/orphanpage/config`],
			category: [`${pathSrc}/views/pages/category/config`],
			editorial: [`${pathSrc}/views/pages/editorial/config`],
			peopleHome: [`${pathSrc}/views/pages/people/home/<USER>
			peopleList: [`${pathSrc}/views/pages/people/list/config`],
			peopleBirthday: [`${pathSrc}/views/pages/people/birthday/config`],
			personHome: [`${pathSrc}/views/pages/person/home/<USER>
			personNews: [`${pathSrc}/views/pages/person/news/config`],
			personDiaporama: [`${pathSrc}/views/pages/person/diaporama/config`],
			monVoiciOnboarding: [`${pathSrc}/views/pages/monVoici/onboarding/config`],
			notifications: [`${pathSrc}/views/pages/notifications/config`],
			eventHome: [`${pathSrc}/views/pages/event/home/<USER>
			eventList: [`${pathSrc}/views/pages/event/list/config`],
			eventNews: [`${pathSrc}/views/pages/event/news/config`],
			eventStars: [`${pathSrc}/views/pages/event/stars/config`],
			eventVideos: [`${pathSrc}/views/pages/event/videos/config`],
			tag: [`${pathSrc}/views/pages/tag/config`],
			search: [`${pathSrc}/views/pages/search/config`],
			error: [`${pathSrc}/views/pages/error/config`],
			partials: [`${pathSrc}/views/pages/partnerWrapper/config`],
			videoContainer: [`${pathSrc}/views/shared/videoContainer/config`],
			datadogClient: [`${pathSrc}/views/shared/datadog/config`]
		}
		/**
		 * Storybook commented out because build does not support it yet
		 */
		// storybook: {
		// 	storybook: [`${pathSrc}/views/storybook.js`],
		// 	'pages/article/components/articleMain/articleRelatedPeople': [
		// 		`${pathSrc}/views/pages/article/components/articleMain/articleRelatedPeople/config`
		// 	],
		// 	'pages/article/components/articleSlideshowPush': [
		// 		`${pathSrc}/views/pages/article/components/articleSlideshowPush/config`
		// 	],
		// 	'pages/article/types/articleLive/liveLabel': [
		// 		`${pathSrc}/views/pages/article/types/articleLive/liveLabel/config`
		// 	],
		// 	'pages/article/types/articleLive/livePosts': [
		// 		`${pathSrc}/views/pages/article/types/articleLive/livePosts/config`
		// 	],
		// 	'pages/article/types/articleLive/liveSummary': [
		// 		`${pathSrc}/views/pages/article/types/articleLive/liveSummary/config`
		// 	],
		// 	'pages/category/variant/editoPushes': [
		// 		`${pathSrc}/views/pages/category/variant/editoPushes/config`
		// 	],
		// 	'pages/category/variant/newsList': [
		// 		`${pathSrc}/views/pages/category/variant/newsList/config`
		// 	],
		// 	'pages/event/home/<USER>': [
		// 		`${pathSrc}/views/pages/event/home/<USER>/config`
		// 	],
		// 	'pages/home/<USER>': [`${pathSrc}/views/pages/home/<USER>/config`],
		// 	'pages/home/<USER>': [`${pathSrc}/views/pages/home/<USER>/config`],
		// 	'pages/home/<USER>': [`${pathSrc}/views/pages/home/<USER>/config`],
		// 	'pages/people/shared/alphabetic': [
		// 		`${pathSrc}/views/pages/people/shared/alphabetic/config`
		// 	],
		// 	'pages/people/shared/peopleCardGrid': [
		// 		`${pathSrc}/views/pages/people/shared/peopleCardGrid/config`
		// 	],
		// 	'pages/person/diaporama/diapoHeader': [
		// 		`${pathSrc}/views/pages/person/diaporama/diapoHeader/config`
		// 	],
		// 	'pages/person/diaporama/diapoList': [
		// 		`${pathSrc}/views/pages/person/diaporama/diapoList/config`
		// 	],
		// 	'pages/person/home/<USER>': [
		// 		`${pathSrc}/views/pages/person/home/<USER>/config`
		// 	],
		// 	'pages/person/home/<USER>': [
		// 		`${pathSrc}/views/pages/person/home/<USER>/config`
		// 	],
		// 	'shared/articleCard': [`${pathSrc}/views/shared/articleCard/config`],
		// 	'shared/articleCarousel': [`${pathSrc}/views/shared/articleCarousel/config`],
		// 	'shared/articleImageCard': [`${pathSrc}/views/shared/articleImageCard/config`],
		// 	'shared/articleList': [`${pathSrc}/views/shared/articleList/config`],
		// 	'shared/articleQualifier': [`${pathSrc}/views/shared/articleQualifier/config`],
		// 	'shared/bookmarkButton': [`${pathSrc}/views/shared/bookmarkButton/config`],
		// 	'shared/breadcrumb': [`${pathSrc}/views/shared/breadcrumb/config`],
		// 	'shared/footer': [`${pathSrc}/views/shared/footer/config`],
		// 	'shared/header': [`${pathSrc}/views/shared/header/config`],
		// 	'shared/headingSeo': [`${pathSrc}/views/shared/headingSeo/config`],
		// 	'shared/gameCard': [`${pathSrc}/views/shared/gameCard/config`],
		// 	'shared/genericModal': [
		// 		`${pathSrc}/views/shared/genericModal/config`,
		// 		`${pathSrc}/views/shared/genericModal/storybook/config`
		// 	],
		// 	'shared/genericPushCard': [`${pathSrc}/views/shared/genericPushCard/config`],
		// 	'shared/linkbooster': [`${pathSrc}/views/shared/linkbooster/config`],
		// 	'shared/macros/callToAction': [
		// 		`${pathSrc}/views/shared/macros/callToAction/config`,
		// 		`${pathSrc}/views/shared/macros/callToAction/storybook/config`
		// 	],
		// 	'shared/macros/categoryTag': [`${pathSrc}/views/shared/macros/categoryTag/config`],
		// 	'shared/macros/iconSwitcher': [`${pathSrc}/views/shared/macros/iconSwitcher/config`],
		// 	'shared/mostViewedArticlesList': [
		// 		`${pathSrc}/views/shared/mostViewedArticlesList/config`
		// 	],
		// 	'shared/offsetCardGrid': [`${pathSrc}/views/shared/offsetCardGrid/config`],
		// 	'shared/pageGrid': [`${pathSrc}/views/shared/pageGrid/config`],
		// 	'shared/pagination': [`${pathSrc}/views/shared/pagination/config`],
		// 	'shared/personCard': [`${pathSrc}/views/shared/personCard/config`],
		// 	'shared/personLead': [`${pathSrc}/views/shared/personLead/config`],
		// 	'shared/promoBasArticle': [`${pathSrc}/views/shared/promoBasArticle/config`],
		// 	'shared/publicationDate': [`${pathSrc}/views/shared/publicationDate/config`],
		// 	'shared/relatedPeople': [`${pathSrc}/views/shared/relatedPeople/config`],
		// 	'shared/snackbar': [
		// 		`${pathSrc}/views/shared/snackbar/assets/styles/style`,
		// 		`${pathSrc}/views/shared/snackbar/storybook/config`
		// 	],
		// 	'shared/tagsList': [`${pathSrc}/views/shared/tagsList/config`],
		// 	'shared/titles': [`${pathSrc}/views/shared/titles/config`]
		// }
	}
}
