import PMCBridge from 'Assets/scripts/pmcBridge'

import { Component, navigate } from 'costro'
import TemplateEndScreen from './templates/end-screen.js'

// The step needs to extend the UserLoginStep
export default class EndScreen extends Component {
	// Selector string of the step element
	element = '.ultEndScreen'

	constructor(props) {
		super(props)
		this.pmcBridge = new PMCBridge()

		this.onClickToPreviousStep = this.onClickToPreviousStep.bind(this)
	}

	/**
	 * Required function
	 * @returns {HTMLElement}
	 */
	render() {
		return <TemplateEndScreen hash={this.route.path} />
	}

	/**
	 * After render
	 */
	afterRender() {
		this.container = document.querySelector('.ultEndScreen')
		this.backButton = this.container.querySelector('.ultEndScreen-backButton')
		this.button = this.container.querySelector('.ultEndScreen-formWithPassword')
		this.link = this.container.querySelector('.ultEndScreen-link.faq')
		this.setDomainLink(this.link)

		this.addEvents()
	}

	/**
	 * Set PMC domain link
	 * @param {HTMLElement} element - <a> tag
	 * @async
	 */
	async setDomainLink(element) {
		const href = await this.pmcBridge.getPmcDomain()
		element.setAttribute('href', `${href}/faq`)
	}

	/**
	 * Add Events
	 */
	addEvents() {
		this.backButton.addEventListener('click', this.onClickToPreviousStep)
		this.button.addEventListener('click', this.onClickToConnectToPassword)
	}

	/**
	 * previous function
	 * @param {Event} e Event listener datas
	 */
	onClickToPreviousStep(e) {
		e.preventDefault()
		navigate('/compte')
	}

	/**
	 * navigate to login pannel
	 * @param {Event} e Event listener datas
	 */
	onClickToConnectToPassword(e) {
		e.preventDefault()
		navigate('/connexion')
	}
}
