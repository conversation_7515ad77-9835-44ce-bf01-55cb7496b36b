import PMCInsiteClient from '@prismamedia/pmc-insite-client'
import ConsentBridge from 'Assets/scripts/consentBridge'
import Tracking from 'Shared/tracking/tracking'
import { isValidEmail } from './utils'
import { getRedirectToUrl } from 'Shared/userLoginTunnel/assets/scripts/utils'
import replaceDynamicValues from 'Shared/utils/replaceDynamicValues'

import TemplateHome from './templates/home.js'
import { Component, navigate } from 'costro'

// The step needs to extend the UserLoginStep
export default class Home extends Component {
	// Selector string of the step element
	element = '.ultHome'

	constructor(props) {
		super(props)
		this.timer = null
		this.redirectTo = getRedirectToUrl()
		this.autoRedirect = false

		this.tracking = new Tracking()
		this.consentBridge = new ConsentBridge()

		this.onClickOnStep = this.onClickOnStep.bind(this)
		this.onKeyUpOnStep = this.onKeyUpOnStep.bind(this)
		this.pmcInSiteSdkLoaded = this.pmcInSiteSdkLoaded.bind(this)
	}

	/**
	 * Get the storage props
	 * @returns {Object} props
	 */
	getStorageProps() {
		return JSON.parse(window.sessionStorage.getItem('voi-pmcInSiteFeatureProps')) || {}
	}

	/**
	 * Before render
	 */
	beforeRender() {
		const storageProps = this.getStorageProps()
		const featureData = {
			header: {
				intro: 'Accédez gratuitement à de nombreux avantages',
				signupService: 'VOI_COMPTE-PMC_BOUTON-PROFIL_SITE-VOI_SE-CONNECTER',
				entry: 'default'
			},
			star: {
				intro: 'Les dernières actus de vos stars préférées dans votre boite mail !',
				signupService: '{signupService}',
				entry: 'star',
				redirectTo: '{redirectTo}'
			}
		}

		this.featureEntry = featureData[storageProps.entry || 'header']

		// Search and replace dynamic value with their associated props
		if (storageProps.dynamicValues) {
			this.featureEntry = replaceDynamicValues({
				dynamicValues: storageProps.dynamicValues,
				featureEntry: this.featureEntry
			})
		}

		this.redirectTo = this.featureEntry.redirectTo || getRedirectToUrl()
		this.autoRedirect = ['comments', 'star'].includes(this.featureEntry.entry)
	}

	/**
	 * Required function
	 * @returns {HTMLElement}
	 */
	render() {
		return <TemplateHome featureEntry={this.featureEntry} />
	}

	/**
	 * Before destroy
	 */
	beforeDestroy() {
		// Set data to the store before the destroy
		// Data will be used by next steps
		this.setStore({
			email: this.container.querySelector('#pmc-email').value,
			signupService: this.featureEntry.signupService,
			entry: this.featureEntry.entry,
			redirectTo: this.redirectTo,
			autoRedirect: this.autoRedirect
		})
	}

	/**
	 * After render
	 */
	afterRender() {
		PMCInsiteClient('addLegalBlock', { id: 'pmc-cgu' }).then(() => {
			this.container = document.querySelector(this.element)
			this.email = this.container.querySelector('#pmc-email')
			this.emailError = this.container.querySelector('#pmc-emailError')
			this.button = this.container.querySelector('.ultHome-formButton')

			this.addEvents()
		})
	}

	/* Adding events to the DOM elements. */
	addEvents() {
		const pmcInsiteLoaded = window.sessionStorage.getItem('voi-pmcInSiteLoaded')
		this.container.addEventListener('keyup', this.onKeyUpOnStep)
		this.button.addEventListener('click', this.onClickOnStep)
		const signupService = this.featureEntry.signupService
		const queryVars = {
			redirectTo: this.redirectTo
		}

		// Only to be set if true to prevent unwanted behavior
		// If set to false, it is true also: all query strings are read as strings
		if (this.autoRedirect) {
			queryVars.autoRedirect = this.autoRedirect
		}

		PMCInsiteClient('bindSocialNetworkAPI', {
			socialNetwork: 'google',
			buttonId: 'pmc-google',
			signupService,
			queryVars,
			onConnectionSuccess: async () => {
				const button = document.getElementById('pmc-google')
				this.socialNetworkTracking(button.getAttribute('data-social-network'))
				await this.updateConsentConfig()
			},
			customizeBtn: {
				width: 280,
				size: 'large',
				text: 'continue_with',
				logo_alignement: 'center'
			}
		})

		PMCInsiteClient('bindSocialNetworkAPI', {
			socialNetwork: 'apple',
			buttonId: 'pmc-apple',
			signupService,
			queryVars,
			onConnectionSuccess: async () => {
				const button = document.getElementById('pmc-apple')
				this.socialNetworkTracking(button.getAttribute('data-social-network'))
				await this.updateConsentConfig()
			}
		})

		const googleLoad = this.socialNetworkLoading({
			event: 'PMC::INSITE::ENABLED::GG',
			key: pmcInsiteLoaded
		})
		const appleLoad = this.socialNetworkLoading({
			event: 'PMC::INSITE::ENABLED::AP',
			key: pmcInsiteLoaded
		})

		if (pmcInsiteLoaded) {
			this.pmcInSiteSdkLoaded()
		} else {
			Promise.all([googleLoad, appleLoad]).then(this.pmcInSiteSdkLoaded)
		}
	}

	/**
	 * Hide the skeleton loader for the social media icons when SDKs are ready
	 */
	pmcInSiteSdkLoaded() {
		;[...this.container.querySelectorAll('#pmc-google, #pmc-apple')].forEach((item) => {
			item.closest('li').querySelector('.skeletonLoader').classList.add('hidden')
		})

		window.sessionStorage.setItem('voi-pmcInSiteLoaded', true)
	}

	/**
	 * Function that validates the targeted button and call the onClickOnSubmit function
	 */
	onClickOnStep(e) {
		e.preventDefault()
		this.onClickOnSubmit(e)
	}

	/**
	 * Check social network loading
	 * @param {Object} options object options
	 * @param {String} options.event event name for each social network
	 * @param {Boolean} options.key key in Storage for loop user action
	 * @returns {<Promise|Any>} resolve promise
	 */
	socialNetworkLoading({ event, key }) {
		return new Promise((resolve) => {
			if (key) {
				window.sessionStorage.removeItem('voi-pmcInSiteLoaded', true)
				resolve()
			}
			document.addEventListener(event, () => {
				resolve()
			})
		})
	}

	/**
	 * It tracks the user's social network connection.
	 * @param {String} socialNetwork - The social network that the user has connected to.
	 */
	socialNetworkTracking(socialNetwork) {
		this.tracking.onSocialNetworkConnect({ socialNetwork })
	}

	/**
	 * Function that set the consent and the config of user
	 * @returns {Boolean} true boolean to finish function
	 */
	async updateConsentConfig() {
		if (this.featureEntry.entry === 'star') {
			await this.consentBridge.setConsent()
			await this.consentBridge.updateConsentConfig()
		}
		return true
	}

	/**
	 * Function that checks if user enters a valid email in input area
	 */
	onKeyUpOnStep() {
		clearTimeout(this.timer)
		this.timer = setTimeout(() => {
			const emailValue = this.email.value

			this.emailError.innerHTML = ''
			this.button.setAttribute('disabled', true)
			this.email.classList.remove('error')

			if (typeof isValidEmail(emailValue) === 'boolean') {
				this.button.removeAttribute('disabled')
				this.email.classList.add('active')
				this.email.classList.remove('error')
			} else if (emailValue.length) {
				this.button.setAttribute('disabled', true)
				this.email.classList.add('error')
				this.emailError.innerHTML = isValidEmail(emailValue)
			}
		}, 600)
	}

	/**
	 * Function that checks if the user's email is available
	 * @param {Event} e Event data
	 */
	onClickOnSubmit(e) {
		e.preventDefault()

		const email = this.container.querySelector('#pmc-email').value
		this.button.classList.add('insite-loading')
		this.tracking.onContinueWithEmail()

		PMCInsiteClient('checkEmailAvailability', { email }).then((res) => {
			this.button.classList.remove('insite-loading')
			if (res) {
				navigate('/inscription')
			} else {
				navigate('/connexion')
			}
		})
	}
}
