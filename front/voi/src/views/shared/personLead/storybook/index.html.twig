{% set Data = {
    author: {
		activeOnBrandKeys: ['VOI'],
		avatar: {
			caption: '<PERSON> Buys',
			copyright: null,
			original: {
				height: null,
				parameters: null,
				url: 'https://www.voici.fr/imgre/fit/~1~laura buys~2022~09~28~5749c8ab-24d9-4f47-ac81-f989e5078d9e.png/800x600/quality/80/laura-buys.jpg',
				width: null,
			},
			title: '<PERSON> Buys',
			urlTemplate:
				'https://www.voici.fr/imgre/{transformation}/~1~laura buys~2022~09~28~5749c8ab-24d9-4f47-ac81-f989e5078d9e.png/{width}x{height}/{parameters}/{title}.{format}',
		},
		description:
			'Refoulée au concours de l\'institut <PERSON>, j\'essaie désormais de passer les grilles du palais de <PERSON>. En attendant d\'entrer dans la cour des plus grands, je dévoile les secrets de couloirs (et d\'histoires) de la maison royale. Des malheurs de Meghan  à la guerre entre William et Harry en passant par l\'incroyable classe de Kate Middleton\u2026 Je suis la Mère Castor des éditions Windsor. ',
		existsOnBrandKeys: ['TEL', 'TLS', 'VOI'],
		fullname: 'Laura Buys',
		hasPublicProfile: true,
			boneId: '6952499b-242d-4aa8-81de-15b68ac357db',
			id: '6952499b-242d-4aa8-81de-15b68ac357db',
			indexed: '2023-03-14T16:31:30+0000',
			metas: {
				mainNodeId: null,
				objectId: null,
				type: 'author',
				visibility: 'visible',
			},
			modified: '2023-03-06T14:58:48+0000',
			path: '/auteur/laura-buys',
			paths: ['/auteur/laura-buys'],
			published: '2017-07-06T18:51:48+0000',
			source: 'voi',
			type: 'author',
		resume: 'Coordinatrice web',
		social: {
			email: '<EMAIL>',
			facebook: null,
			google: null,
			instagram: null,
			pinterest: null,
			snapchat: null,
			twitter: null,
		},
	},
	person: {
		awards: [],
		biography:
			'<p>Plus connue en tant qu\'<strong>épouse d\'Emmanuel Macron</strong>, homme politique, Brigitte Macron est une ancienne professeure de français. Issue d\'une famille bourgeoise, Brigitte Trogneux grandit  à Amiens.</p> <p>Durant douze années, elle enseigne dans sa ville d\'origine. Elle anime également le club de théâtre au sein du lycée La Providence, auquel participe Emmanuel Macron. Le ministre affirme d\'ailleurs que son go\u00fbt pour la littérature est né, en grande partie, grâce  à cette femme de lettres.</p> <p>L\'homme politique épouse Brigitte en 2007, en dépit de leur différence d\'âge. Le mariage est célébré au Touquet, o\u00f9 le couple se rend régulièrement pour se retrouver en famille. Brigitte Macron a déj à trois enfants, nés d\'une précédente union.</p> <p>Brigitte Macron reste très discrète sur sa vie privée. Pourtant,  à l\'instar de son mari, elle ne peut manquer d\'exprimer sa joie en apprenant sa nomination au poste de Ministre de l\'éco-no-mie, de l\'In-dus-trie et du Numé-rique. Durant les débats très animés autour de la « Loi Macron », le couple se voit attribuer un logement de fonction pour que le ministre puisse participer et suivre l\'actualité de la scène politique.</p> <p>Le 16 novembre 2016, elle est aux côtés de son mari lorsqu\'il annonce sa candidature  à la présidence de la république.  <strong>Le 7 mai 2017, il remporte l\'élection présidentielle avec plus de 66% des suffrages</strong>, et devient le plus jeune Président de la République française.  En juin 2019, Brigitte Macron succède  à Bernadette Chirac  à la présidence de la Fondation Hôpitaux de Paris-Hôpitaux de France.</p> <p>Côté vie privée, elle épouse en 1974, André Louis Auzières, ensemble le couple a trois enfants :  Sébastien, Laurence et Tiphaine. Son ex-mari meurt en décembre 2019.</p>',
		borned: '1953-04-13',
		category: {
			depth: 1,
			format: 'CATEGORY',
				boneId: '8b442792-4f99-d4c4-89ee-aafcab5c9010',
				id: '8b442792-4f99-d4c4-89ee-aafcab5c9010',
				indexed: '2023-05-03T11:05:23+0000',
				metas: {
					mainNodeId: null,
					objectId: null,
					type: 'frontpage',
					visibility: 'visible',
				},
				modified: '2012-03-19T13:17:35+0000',
				path: '/bios-people',
				published: '2017-05-24T07:09:33+0000',
				slug: 'bios-people',
				source: 'voi',
				type: 'category',
			title: 'Bios People',
		},
		country: 'France',
		died: null,
		firstname: 'Brigitte',
		fullname: 'Brigitte Macron',
		gender: 'femme',
		jobs: 'Professeur',
		lastname: 'Macron',
		mostShared: {
			count: 1,
			date: '1986-01-25T00:00:00+0000',
		},
		mostViewed: {
			lastMonth: 6000,
			lastWeek: 4000,
			yesterday: 0,
		},
		pages: [
			{
				category: {
					depth: 0,
					format: 'EVENT',
						boneId: '5b24c437-583b-4b0f-a21d-e21d42853006',
						id: '5b24c437-583b-4b0f-a21d-e21d42853006',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							type: 'frontpage',
							visibility: 'visible',
						},
						modified: '2023-04-21T20:29:14+0000',
						path: '/',
						published: '2018-05-02T20:11:11+0000',
						slug: 'gal',
						source: 'voi',
						type: 'category',
					title: 'GAL',
				},
				children: [],
				description: 'La guerre des clans divise la famille du Taulier, Laeticia Hallyday, Laura Smet',
				draftBody:
					'{"blocks":[{"key":"6gg5m","text":"","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":[]}],"entityMap":[]}',
				format: 'EVENT',
				medias: {
					imageCount: 1,
					images: [
						{
							caption: null,
							copyright: 'Non renseigné',
							original: {
								height: 638,
								parameters: null,
								url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fgal.2F2018.2F09.2F05.2Fe9ad7889-1ca5-464f-b566-e8c1722ed5c6.2Ejpeg/2766x638/quality/80/voi.jpeg',
								width: 2766,
							},
							title: 'voi',
							urlTemplate:
								'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fgal.2F2018.2F09.2F05.2Fe9ad7889-1ca5-464f-b566-e8c1722ed5c6.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
						},
					],
					videoCount: 0,
					videos: [],
				},
				partnerTitle: 'Johnny Hallyday : deux ans après',
				path: [
					{
						depth: 0,
						format: 'EVENT',
							boneId: '5b24c437-583b-4b0f-a21d-e21d42853006',
							id: '5b24c437-583b-4b0f-a21d-e21d42853006',
							indexed: '2023-05-03T11:05:23+0000',
							metas: {
								mainNodeId: null,
								objectId: null,
								type: 'frontpage',
								visibility: 'visible',
							},
							modified: '2023-04-21T20:29:14+0000',
							path: '/',
							published: '2018-05-02T20:11:11+0000',
							slug: 'gal',
							source: 'voi',
							type: 'category',
						title: 'GAL',
					},
					{
						depth: 1,
						format: 'EVENT',
							boneId: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
							id: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
							indexed: '2023-05-03T11:05:23+0000',
							metas: {
								mainNodeId: null,
								objectId: null,
								type: 'rubrique',
								visibility: 'visible',
							},
							modified: '2023-02-17T12:26:18+0000',
							path: '/evenements',
							published: '2018-05-02T20:05:06+0000',
							slug: 'evenements',
							source: 'voi',
							type: 'category',
						title: 'Evènements',
					},
				],
					boneId: '488d6b80-d126-4925-b33f-2190760f10c1',
					id: '488d6b80-d126-4925-b33f-2190760f10c1',
					indexed: '2023-05-03T11:05:23+0000',
					metas: {
						mainNodeId: null,
						objectId: null,
						type: 'home_evenement',
						visibility: 'visible',
					},
					modified: '2021-06-02T15:23:10+0000',
					path: '/evenements/johnny_hallyday',
					paths: ['/evenements/johnny_hallyday'],
					published: '2018-05-02T20:11:20+0000',
					source: 'voi',
					type: 'event',
				tags: [],
				title: 'Johnny 2 ans après',
			},
			{
				category: {
					depth: 0,
					format: 'EVENT',
						boneId: '5b24c437-583b-4b0f-a21d-e21d42853006',
						id: '5b24c437-583b-4b0f-a21d-e21d42853006',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							type: 'frontpage',
							visibility: 'visible',
						},
						modified: '2023-04-21T20:29:14+0000',
						path: '/',
						published: '2018-05-02T20:11:11+0000',
						slug: 'gal',
						source: 'voi',
						type: 'category',
					title: 'GAL',
				},
				children: [],
				description: "Suivez toute l'actualité de la campagne présidentielle 2022",
				draftBody:
					'{"blocks":[{"key":"1sagt","text":"Les résultats sont enfin tombés ! Ce dimanche 24 avril, les Françaises et Français ont réélu Emmanuel Macron avec 58,80% des voix contre 41,20% pour Marine Le Pen au second tour de cette présidentielle 2022. La fin d\'un scrutin bouleversé par la guerre en Ukraine, et pour lequel 11 candidats ont tenté de prendre la place du président sortant : Jean-Luc Mélenchon, Eric Zemmour, Valérie Pécresse, Yannick Jadot, Anne Hidalgo, Philippe Poutou, Fabien Roussel, Nathalie Arthaud, Jean Lassalle, Nicolas Dupont-Aignan et donc Marine Le Pen. Discours d\'Emmanuel Macron, réactions de ses adversaires et coulisses de cette soirée historique : suivez l\'après-présidentielle sur Gala.fr. ","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":[]}],"entityMap":[]}',
				format: 'EVENT',
				medias: {
					imageCount: 0,
					images: [],
					videoCount: 0,
					videos: [],
				},
				partnerTitle: "L'actualité de la campagne présidentielle 2022",
				path: [
					{
						depth: 0,
						format: 'EVENT',
							boneId: '5b24c437-583b-4b0f-a21d-e21d42853006',
							id: '5b24c437-583b-4b0f-a21d-e21d42853006',
							indexed: '2023-05-03T11:05:23+0000',
							metas: {
								mainNodeId: null,
								objectId: null,
								type: 'frontpage',
								visibility: 'visible',
							},
							modified: '2023-04-21T20:29:14+0000',
							path: '/',
							published: '2018-05-02T20:11:11+0000',
							slug: 'gal',
							source: 'voi',
							type: 'category',
						title: 'GAL',
					},
					{
						depth: 1,
						format: 'EVENT',
							boneId: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
							id: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
							indexed: '2023-05-03T11:05:23+0000',
							metas: {
								mainNodeId: null,
								objectId: null,
								type: 'rubrique',
								visibility: 'visible',
							},
							modified: '2023-02-17T12:26:18+0000',
							path: '/evenements',
							published: '2018-05-02T20:05:06+0000',
							slug: 'evenements',
							source: 'voi',
							type: 'category',
						title: 'Evènements',
					},
				],
					boneId: 'd125ce39-6247-4c82-b35e-aa38870dc005',
					id: 'd125ce39-6247-4c82-b35e-aa38870dc005',
					indexed: '2023-05-03T11:05:23+0000',
					metas: {
						mainNodeId: null,
						objectId: null,
						type: 'home_evenement',
						visibility: 'visible',
					},
					modified: '2023-04-13T09:56:14+0000',
					path: '/evenements/presidentielle-2022',
					paths: ['/evenements/presidentielle-2022'],
					published: '2022-01-28T15:17:31+0000',
					source: 'voi',
					type: 'event',
				tags: [
					{
						label: 'Politique',
						path: 'Politique',
					url: {
							path: '/page/tag/politique',
						},
					},
				],
				title: 'PRéSIDENTIELLE 2022',
			},
		],
		path: [
			{
				depth: 0,
				format: 'CATEGORY',
					boneId: '8b442792-d4c4-4f99-89ee-aafcab5c9010',
					id: '8b442792-d4c4-4f99-89ee-aafcab5c9010',
					indexed: '2023-05-03T11:05:23+0000',
					metas: {
						mainNodeId: null,
						objectId: null,
						type: 'rubrique',
						visibility: 'visible',
					},
					modified: '2017-05-24T07:09:23+0000',
					path: '/',
					published: '2017-05-24T07:09:23+0000',
					slug: 'voici',
					source: 'voi',
					type: 'category',
				title: 'Voici',
			},
			{
				depth: 1,
				format: 'CATEGORY',
					boneId: '8b442792-4f99-d4c4-89ee-aafcab5c9010',
					id: '8b442792-4f99-d4c4-89ee-aafcab5c9010',
					indexed: '2023-05-03T11:05:23+0000',
					metas: {
						mainNodeId: null,
						objectId: null,
						type: 'frontpage',
						visibility: 'visible',
					},
					modified: '2012-03-19T13:17:35+0000',
					path: '/bios-people',
					published: '2017-05-24T07:09:33+0000',
					slug: 'bios-people',
					source: 'voi',
					type: 'category',
				title: 'Bios People',
			},
		],
		picture: {
			caption: null,
			copyright: null,
			original: {
				height: 1536,
				parameters: null,
				url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F28f7c2ab-2f8c-49c4-a25c-6e4e566d450c.2Ejpeg/2048x1536/quality/80/brigitte-macron.jpeg',
				width: 2048,
			},
			title: 'brigitte-macron',
			urlTemplate:
				'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F28f7c2ab-2f8c-49c4-a25c-6e4e566d450c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
		},
		pplId: 9362,
		realFirstname: 'Brigitte',
		realLastname: 'Trogneux',
		relations: [
			{
				order: 0,
				person: {
					firstname: 'Emmanuel',
					fullname: 'Emmanuel Macron',
					jobs: 'Président de la République, homme politique',
					lastname: 'Macron',
					picture: {
						caption: null,
						copyright: 'Clément Prioli/Starface',
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ffc6b0376-a961-4617-9c95-de31dee24325.2Ejpeg/2048x1536/quality/80/emmanuel-macron.jpeg',
							width: 2048,
						},
						title: 'emmanuel-macron',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ffc6b0376-a961-4617-9c95-de31dee24325.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 9143,
					realFirstname: null,
					realLastname: null,
						boneId: '5e86b0f0-6ce4-4537-98d9-bd4d6ddbb669',
						id: '5e86b0f0-6ce4-4537-98d9-bd4d6ddbb669',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 9143,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/emmanuel-macron',
						published: '2017-06-30T14:12:00+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est le mari de',
			},
			{
				order: 2,
				person: {
					firstname: 'Laurence',
					fullname: 'Laurence Auzière',
					jobs: 'Cardiologue',
					lastname: 'Auzière',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe5eb88ff-dbe4-4377-a4b8-b45fbfc7bcd2.2Ejpeg/2048x1536/quality/80/laurence-auziere.jpeg',
							width: 2048,
						},
						title: 'laurence-auziere',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe5eb88ff-dbe4-4377-a4b8-b45fbfc7bcd2.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12278,
					realFirstname: null,
					realLastname: null,
						boneId: 'efc1dcd1-02fe-445e-ba52-171b116e660b',
						id: 'efc1dcd1-02fe-445e-ba52-171b116e660b',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12278,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/laurence-auziere',
						published: '2017-06-30T14:31:24+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est la fille de',
			},
			{
				order: 3,
				person: {
					firstname: 'Tiphaine',
					fullname: 'Tiphaine Auzière',
					jobs: 'Avocate',
					lastname: 'Auzière',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fed303827-0488-4648-bc83-68685f3a2ccb.2Ejpeg/2048x1536/quality/80/tiphaine-auziere.jpeg',
							width: 2048,
						},
						title: 'tiphaine-auziere',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fed303827-0488-4648-bc83-68685f3a2ccb.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 11983,
					realFirstname: null,
					realLastname: null,
						boneId: '458087f7-0377-4264-bcb2-8fc037f031e1',
						id: '458087f7-0377-4264-bcb2-8fc037f031e1',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 11983,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/tiphaine-auziere',
						published: '2017-06-30T14:29:19+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est la fille de',
			},
			{
				order: 4,
				person: {
					firstname: 'Sébastien',
					fullname: 'Sébastien Auzière',
					jobs: 'Ingénieur',
					lastname: 'Auzière',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F42ee0c9c-448c-4ac2-830f-e2b6c3bae821.2Ejpeg/2048x1536/quality/80/sebastien-auziere.jpeg',
							width: 2048,
						},
						title: 'sebastien-auziere',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F42ee0c9c-448c-4ac2-830f-e2b6c3bae821.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12274,
					realFirstname: null,
					realLastname: null,
						boneId: '8e1699ef-5c73-41f0-a459-a8694173b273',
						id: '8e1699ef-5c73-41f0-a459-a8694173b273',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12274,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/sebastien-auziere',
						published: '2017-06-30T14:31:14+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est le fils de',
			},
			{
				order: 5,
				person: {
					firstname: 'Manuel',
					fullname: 'Manuel Valls',
					jobs: 'homme politique',
					lastname: 'Valls',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F5873cb8d-fc78-44ae-82d5-4922af512ff3.2Ejpeg/2048x1536/quality/80/manuel-valls.jpeg',
							width: 2048,
						},
						title: 'manuel-valls',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F5873cb8d-fc78-44ae-82d5-4922af512ff3.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 2042,
					realFirstname: null,
					realLastname: null,
						boneId: '7168258a-cb2d-4047-bc3c-9eaf84d8b9fa',
						id: '7168258a-cb2d-4047-bc3c-9eaf84d8b9fa',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 2042,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/manuel-valls',
						published: '2017-07-05T13:09:36+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 6,
				person: {
					firstname: 'François',
					fullname: 'François Hollande',
					jobs: 'homme politique',
					lastname: 'Hollande',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F0e96e6fe-28e6-4b99-a593-7061e7ac727c.2Ejpeg/2048x1536/quality/80/francois-hollande.jpeg',
							width: 2048,
						},
						title: 'francois-hollande',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F0e96e6fe-28e6-4b99-a593-7061e7ac727c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 591,
					realFirstname: null,
					realLastname: null,
						boneId: 'c1278293-2f44-4d95-a00e-079f30a9a2de',
						id: 'c1278293-2f44-4d95-a00e-079f30a9a2de',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 591,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/francois-hollande',
						published: '2017-06-30T13:17:22+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 7,
				person: {
					firstname: 'Anne',
					fullname: 'Anne Gravoin',
					jobs: 'violoniste',
					lastname: 'Gravoin',
					picture: {
						caption: null,
						copyright: 'ALLARD/POOL/SIPA',
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F5a5fb63e-7445-4141-a1fa-e90de2df0b4a.2Ejpeg/2048x1536/quality/80/anne-gravoin.jpeg',
							width: 2048,
						},
						title: 'anne-gravoin',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F5a5fb63e-7445-4141-a1fa-e90de2df0b4a.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 2800,
					realFirstname: null,
					realLastname: null,
						boneId: '417d7e9c-cb66-47c3-8bc0-4fc86975ff51',
						id: '417d7e9c-cb66-47c3-8bc0-4fc86975ff51',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 2800,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-11-07T10:52:55+0000',
						path: '/bios-people/anne-gravoin',
						published: '2017-06-30T13:41:54+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 8,
				person: {
					firstname: 'Ségolène',
					fullname: 'Ségolène Royal',
					jobs: 'Femme politique',
					lastname: 'Royal',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 173,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4133ba27-07c5-4611-aa78-6747e647b9f6.2Epng/250x173/quality/80/segolene-royal.png',
							width: 250,
						},
						title: 'segolene-royal',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4133ba27-07c5-4611-aa78-6747e647b9f6.2Epng/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 257,
					realFirstname: 'Marie-Ségolène',
					realLastname: 'Royal',
						boneId: '94af0064-bb1a-4164-ad40-4febf9d73414',
						id: '94af0064-bb1a-4164-ad40-4febf9d73414',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 257,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/segolene-royal',
						published: '2017-06-30T13:14:42+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 9,
				person: {
					firstname: 'Véronique',
					fullname: 'Véronique Cazeneuve',
					jobs: 'Editrice',
					lastname: 'Cazeneuve',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F8e33ccc7-5ca9-4479-9e8a-563d83fff0a7.2Ejpeg/2048x1536/quality/80/veronique-cazeneuve.jpeg',
							width: 2048,
						},
						title: 'veronique-cazeneuve',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F8e33ccc7-5ca9-4479-9e8a-563d83fff0a7.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 10936,
					realFirstname: null,
					realLastname: null,
						boneId: '6d97a82d-8105-4f2b-9244-e3a0be01b53e',
						id: '6d97a82d-8105-4f2b-9244-e3a0be01b53e',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 10936,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/veronique-cazeneuve',
						published: '2017-06-30T14:22:17+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 10,
				person: {
					firstname: 'Christelle',
					fullname: 'Christelle Lyon',
					jobs: null,
					lastname: 'Lyon',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F788d0f5d-e5f1-4e9b-a4ef-989c9a9d0dcb.2Ejpeg/2048x1536/quality/80/christelle-lyon.jpeg',
							width: 2048,
						},
						title: 'christelle-lyon',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F788d0f5d-e5f1-4e9b-a4ef-989c9a9d0dcb.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 10797,
					realFirstname: null,
					realLastname: null,
						boneId: '569da2ac-3799-404a-abad-675d5aab0d66',
						id: '569da2ac-3799-404a-abad-675d5aab0d66',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 10797,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/christelle-lyon',
						published: '2017-06-30T14:20:11+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 11,
				person: {
					firstname: 'Pauline',
					fullname: 'Pauline Doussau de Bazignan',
					jobs: 'Artiste-peintre, Assistante parlementaire',
					lastname: 'Doussau de Bazignan',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fdaddc67f-92f7-4d5b-888a-f399a96a8655.2Ejpeg/2048x1536/quality/80/pauline-doussau-de-bazignan.jpeg',
							width: 2048,
						},
						title: 'pauline-doussau-de-bazignan',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fdaddc67f-92f7-4d5b-888a-f399a96a8655.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 10946,
					realFirstname: null,
					realLastname: null,
						boneId: '9a101fc3-85de-4d2d-8be8-d0e9d11e975f',
						id: '9a101fc3-85de-4d2d-8be8-d0e9d11e975f',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 10946,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/pauline-doussau-de-bazignan',
						published: '2017-06-30T14:22:44+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 12,
				person: {
					firstname: 'Hortense',
					fullname: 'Hortense de Labriffe',
					jobs: 'Collaboratrice politique, Administratrice',
					lastname: 'de Labriffe',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe27f5f23-28c1-4a96-aebd-8f84130ebbb8.2Ejpeg/2048x1536/quality/80/hortense-de-labriffe.jpeg',
							width: 2048,
						},
						title: 'hortense-de-labriffe',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe27f5f23-28c1-4a96-aebd-8f84130ebbb8.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 9801,
					realFirstname: null,
					realLastname: null,
						boneId: '6e2c030a-512f-46af-8572-3cf03581b716',
						id: '6e2c030a-512f-46af-8572-3cf03581b716',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 9801,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-10-31T10:04:34+0000',
						path: '/bios-people/hortense-de-labriffe',
						published: '2017-06-30T14:14:29+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 13,
				person: {
					firstname: 'Louis',
					fullname: 'Louis Aliot',
					jobs: 'homme politique',
					lastname: 'Aliot',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4d37c25f-9e16-4899-9de4-a37b35856b58.2Ejpeg/2048x1536/quality/80/louis-aliot.jpeg',
							width: 2048,
						},
						title: 'louis-aliot',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4d37c25f-9e16-4899-9de4-a37b35856b58.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 9783,
					realFirstname: null,
					realLastname: null,
						boneId: '8d4cda19-ea63-43c8-b66d-02af1fbca610',
						id: '8d4cda19-ea63-43c8-b66d-02af1fbca610',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 9783,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/louis-aliot',
						published: '2017-06-30T14:13:34+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 14,
				person: {
					firstname: 'Fleur',
					fullname: 'Fleur Pellerin',
					jobs: 'Femme politique',
					lastname: 'Pellerin',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc514fc11-7083-4f20-b792-6a0192d0dc27.2Ejpeg/2048x1536/quality/80/fleur-pellerin.jpeg',
							width: 2048,
						},
						title: 'fleur-pellerin',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc514fc11-7083-4f20-b792-6a0192d0dc27.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 9127,
					realFirstname: null,
					realLastname: null,
						boneId: '5e219f80-4d22-435a-a0a4-020b2b6f28f6',
						id: '5e219f80-4d22-435a-a0a4-020b2b6f28f6',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 9127,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/fleur-pellerin',
						published: '2017-06-30T14:11:21+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 15,
				person: {
					firstname: 'Arnaud',
					fullname: 'Arnaud Montebourg',
					jobs: 'homme politique',
					lastname: 'Montebourg',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F70296055-82c7-4790-9498-8e394f35ce55.2Ejpeg/2048x1536/quality/80/arnaud-montebourg.jpeg',
							width: 2048,
						},
						title: 'arnaud-montebourg',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F70296055-82c7-4790-9498-8e394f35ce55.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 2743,
					realFirstname: null,
					realLastname: null,
						boneId: 'cbaa07eb-58ac-4a04-b54d-dbe791e5c3cc',
						id: 'cbaa07eb-58ac-4a04-b54d-dbe791e5c3cc',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 2743,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/arnaud-montebourg',
						published: '2017-06-30T13:41:51+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 16,
				person: {
					firstname: 'Laurent',
					fullname: 'Laurent Gerra',
					jobs: 'imitateur, scénariste, Humoriste',
					lastname: 'Gerra',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 173,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc7b75158-a4f7-46c1-ad93-dec1b46ba41c.2Ejpeg/250x173/quality/80/laurent-gerra.jpeg',
							width: 250,
						},
						title: 'laurent-gerra',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc7b75158-a4f7-46c1-ad93-dec1b46ba41c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 2715,
					realFirstname: null,
					realLastname: null,
						boneId: '2b44cbb4-7806-4b51-9360-9f91c911982f',
						id: '2b44cbb4-7806-4b51-9360-9f91c911982f',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 2715,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/laurent-gerra',
						published: '2017-06-30T13:43:25+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 17,
				person: {
					firstname: 'Charlotte',
					fullname: 'Charlotte Wauquiez',
					jobs: null,
					lastname: 'Wauquiez',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F05.2F16.2F3e0bdebb-e3c4-49a1-89f8-d6a32d0dc005.2Ejpeg/2048x1536/quality/80/charlotte-wauquiez.jpeg',
							width: 2048,
						},
						title: 'charlotte-wauquiez',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F05.2F16.2F3e0bdebb-e3c4-49a1-89f8-d6a32d0dc005.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 11256,
					realFirstname: 'Charlotte',
					realLastname: 'Deregnaucourt',
						boneId: 'ca536a0c-4250-4630-a945-d96b5df0af3a',
						id: 'ca536a0c-4250-4630-a945-d96b5df0af3a',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 11256,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/charlotte-wauquiez',
						published: '2017-06-30T14:23:45+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 18,
				person: {
					firstname: 'Stéphanie',
					fullname: 'Stéphanie Chevrier',
					jobs: 'Editrice',
					lastname: 'Chevrier',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F06.2F13.2F01684bc4-6a9e-4052-a5a7-23e88d899ae5.2Ejpeg/2048x1536/quality/80/stephanie-chevrier.jpeg',
							width: 2048,
						},
						title: 'stephanie-chevrier',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F06.2F13.2F01684bc4-6a9e-4052-a5a7-23e88d899ae5.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 11592,
					realFirstname: null,
					realLastname: null,
						boneId: '1c6a3396-8bd2-40e2-8f68-9b9308ffd06a',
						id: '1c6a3396-8bd2-40e2-8f68-9b9308ffd06a',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 11592,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/stephanie-chevrier',
						published: '2017-06-30T14:25:13+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 20,
				person: {
					firstname: 'Penelope',
					fullname: 'Penelope Fillon',
					jobs: null,
					lastname: 'Fillon',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F08f8501a-1aee-4050-9110-7d03786f4383.2Ejpeg/2048x1536/quality/80/penelope-fillon.jpeg',
							width: 2048,
						},
						title: 'penelope-fillon',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F08f8501a-1aee-4050-9110-7d03786f4383.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 11838,
					realFirstname: null,
					realLastname: null,
						boneId: '07ab3750-3144-4dcf-aa04-0a26524ae8a4',
						id: '07ab3750-3144-4dcf-aa04-0a26524ae8a4',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 11838,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/penelope-fillon',
						published: '2017-06-30T14:27:54+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 21,
				person: {
					firstname: 'Gabrielle',
					fullname: 'Gabrielle Guallar',
					jobs: null,
					lastname: 'Guallar',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fff4d5ea3-310e-46ea-8077-c453fb3b8563.2Ejpeg/2048x1536/quality/80/gabrielle-guallar.jpeg',
							width: 2048,
						},
						title: 'gabrielle-guallar',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fff4d5ea3-310e-46ea-8077-c453fb3b8563.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 11921,
					realFirstname: null,
					realLastname: null,
						boneId: '4c254663-fe2c-4125-beb8-d244b5579b14',
						id: '4c254663-fe2c-4125-beb8-d244b5579b14',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 11921,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/gabrielle-guallar',
						published: '2017-06-30T14:27:14+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 24,
				person: {
					firstname: 'Richard',
					fullname: 'Richard Ferrand',
					jobs: 'homme politique',
					lastname: 'Ferrand',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4b0f4075-73c6-4dfd-869b-9ad0590e3189.2Ejpeg/2048x1536/quality/80/richard-ferrand.jpeg',
							width: 2048,
						},
						title: 'richard-ferrand',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F4b0f4075-73c6-4dfd-869b-9ad0590e3189.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12324,
					realFirstname: null,
					realLastname: null,
						boneId: '51de2245-8359-41a0-af62-fe586e110b48',
						id: '51de2245-8359-41a0-af62-fe586e110b48',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12324,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/richard-ferrand',
						published: '2017-06-30T14:30:55+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 27,
				person: {
					firstname: 'Sibeth',
					fullname: 'Sibeth Ndiaye',
					jobs: 'Femme politique',
					lastname: 'Ndiaye',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ff1d3035d-d8a0-43b4-8b5e-6d0971073bd6.2Ejpeg/2048x1536/quality/80/sibeth-ndiaye.jpeg',
							width: 2048,
						},
						title: 'sibeth-ndiaye',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ff1d3035d-d8a0-43b4-8b5e-6d0971073bd6.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12337,
					realFirstname: null,
					realLastname: null,
						boneId: '26ddbb3d-8eae-4f82-b5b5-08395805bef5',
						id: '26ddbb3d-8eae-4f82-b5b5-08395805bef5',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12337,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/sibeth-ndiaye',
						published: '2017-06-30T14:31:39+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'A collaboré avec',
			},
			{
				order: 28,
				person: {
					firstname: 'Elisabeth',
					fullname: 'Elisabeth Bayrou',
					jobs: 'Professeure',
					lastname: 'Bayrou',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F08.2F10.2Fe98ed9fd-4ad3-4d31-98c2-aa3dd821cd2c.2Ejpeg/2048x1536/quality/80/elisabeth-bayrou.jpeg',
							width: 2048,
						},
						title: 'elisabeth-bayrou',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F08.2F10.2Fe98ed9fd-4ad3-4d31-98c2-aa3dd821cd2c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12414,
					realFirstname: null,
					realLastname: null,
						boneId: 'bd71a659-a581-477f-800b-c178d5a7b87f',
						id: 'bd71a659-a581-477f-800b-c178d5a7b87f',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12414,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/elisabeth-bayrou',
						published: '2017-07-03T12:10:31+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 30,
				person: {
					firstname: 'Françoise',
					fullname: 'Françoise Noguès',
					jobs: 'Médecin',
					lastname: 'Noguès',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ff3223c93-aac3-4730-a9a9-1460e3001750.2Ejpeg/2048x1536/quality/80/francoise-nogues.jpeg',
							width: 2048,
						},
						title: 'francoise-nogues',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Ff3223c93-aac3-4730-a9a9-1460e3001750.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12380,
					realFirstname: null,
					realLastname: null,
						boneId: 'd215ee5b-6684-4cb1-a546-674a184cf596',
						id: 'd215ee5b-6684-4cb1-a546-674a184cf596',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12380,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/francoise-nogues',
						published: '2017-06-30T14:33:35+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est la belle-mère de',
			},
			{
				order: 31,
				person: {
					firstname: 'Caroline',
					fullname: 'Caroline Collomb',
					jobs: null,
					lastname: 'Collomb',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F0be0f9a1-d16d-4eec-9d50-2edbd5e0fa02.2Ejpeg/2048x1536/quality/80/caroline-collomb.jpeg',
							width: 2048,
						},
						title: 'caroline-collomb',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F0be0f9a1-d16d-4eec-9d50-2edbd5e0fa02.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12378,
					realFirstname: 'Caroline',
					realLastname: 'Rougé',
						boneId: 'ece84fa3-81d5-45b6-96e0-922fbd33c824',
						id: 'ece84fa3-81d5-45b6-96e0-922fbd33c824',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12378,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-16T07:55:55+0000',
						path: '/bios-people/caroline-collomb',
						published: '2017-06-30T14:33:19+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 32,
				person: {
					firstname: 'Maria',
					fullname: 'Maria Vadillo',
					jobs: 'Professeur',
					lastname: 'Vadillo',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F08.2F10.2Ff1a19908-ebb7-4435-8a55-ab47c5938ccd.2Ejpeg/2048x1536/quality/80/maria-vadillo.jpeg',
							width: 2048,
						},
						title: 'maria-vadillo',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F08.2F10.2Ff1a19908-ebb7-4435-8a55-ab47c5938ccd.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12377,
					realFirstname: null,
					realLastname: null,
						boneId: 'b18ae148-2d7b-41e6-9562-fecf852c7678',
						id: 'b18ae148-2d7b-41e6-9562-fecf852c7678',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12377,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/maria-vadillo',
						published: '2017-07-03T12:09:35+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 33,
				person: {
					firstname: 'Florence',
					fullname: 'Florence Hulot',
					jobs: "Hôtesse de l'air, Conseillère municipale",
					lastname: 'Hulot',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F29eee541-6224-4afe-a09b-b897d0ce0d11.2Ejpeg/2048x1536/quality/80/florence-hulot.jpeg',
							width: 2048,
						},
						title: 'florence-hulot',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F29eee541-6224-4afe-a09b-b897d0ce0d11.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12375,
					realFirstname: 'Florence',
					realLastname: 'Lasserre',
						boneId: 'ef3755cb-1c13-468e-a62f-b83200c65558',
						id: 'ef3755cb-1c13-468e-a62f-b83200c65558',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12375,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/florence-hulot',
						published: '2017-06-30T14:33:08+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 36,
				person: {
					firstname: 'Jean-Michel',
					fullname: 'Jean-Michel Macron',
					jobs: 'Médecin',
					lastname: 'Macron',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fbde9eb1c-27b4-416d-a228-7e8102eda425.2Ejpeg/2048x1536/quality/80/jean-michel-macron.jpeg',
							width: 2048,
						},
						title: 'jean-michel-macron',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fbde9eb1c-27b4-416d-a228-7e8102eda425.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 12352,
					realFirstname: null,
					realLastname: null,
						boneId: '4766eec1-9b54-4c53-acb9-062d6843dd7f',
						id: '4766eec1-9b54-4c53-acb9-062d6843dd7f',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 12352,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/jean-michel-macron',
						published: '2017-06-30T14:31:12+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Est le beau-père de',
			},
			{
				order: 38,
				person: {
					firstname: 'Marie',
					fullname: 'Marie Sara',
					jobs: null,
					lastname: 'Sara',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe01cfe76-0fb8-46f4-8022-b88814c70906.2Ejpeg/2048x1536/quality/80/marie-sara.jpeg',
							width: 2048,
						},
						title: 'marie-sara',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fe01cfe76-0fb8-46f4-8022-b88814c70906.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 813,
					realFirstname: null,
					realLastname: null,
						boneId: 'ab8eec78-cdd2-4b0e-8156-43dea1a1724d',
						id: 'ab8eec78-cdd2-4b0e-8156-43dea1a1724d',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 813,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/marie-sara',
						published: '2017-07-03T09:12:56+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 40,
				person: {
					firstname: 'Alexandre',
					fullname: 'Alexandre Benalla',
					jobs: 'Garde du corps',
					lastname: 'Benalla',
					picture: {
						caption: null,
						copyright: null,
						original: {
							height: 1536,
							parameters: null,
							url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F07.2F25.2F8d4a56cd-aa8f-4137-b15b-689bfff7e7d0.2Ejpeg/2048x1536/quality/80/alexandre-benalla.jpeg',
							width: 2048,
						},
						title: 'alexandre-benalla',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2018.2F07.2F25.2F8d4a56cd-aa8f-4137-b15b-689bfff7e7d0.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
					},
					pplId: 13104,
					realFirstname: null,
					realLastname: null,
						boneId: '413d3978-200f-4c9f-90dd-1182430569ff',
						id: '413d3978-200f-4c9f-90dd-1182430569ff',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 13104,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/alexandre-benalla',
						published: '2018-07-25T08:12:02+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
			{
				order: 42,
				person: {
					firstname: 'Patrice',
					fullname: 'Patrice Roques',
					jobs: 'homme politique',
					lastname: 'Roques',
					picture: {
						caption: null,
						copyright: ' Shootpix/ABACA',
						original: {
							height: 2000,
							parameters: {
								'focus-point': '1260,804',
							},
							url: 'https://www.voici.fr/imgre/fit/~1~voi~2022~08~03~d3083983-f72a-43c3-b272-1ca68625b6d6.jpeg/3000x2000/quality/80/patrice-roques.jpeg',
							width: 3000,
						},
						title: 'patrice-roques',
						urlTemplate:
							'https://www.voici.fr/imgre/{transformation}/~1~voi~2022~08~03~d3083983-f72a-43c3-b272-1ca68625b6d6.jpeg/{width}x{height}/{parameters}/focus-point/1260%2C804/{title}.{format}',
					},
					pplId: 13701,
					realFirstname: null,
					realLastname: null,
						boneId: '807178ba-d270-43a9-bd47-822259452b64',
						id: '807178ba-d270-43a9-bd47-822259452b64',
						indexed: '2023-05-03T11:05:23+0000',
						metas: {
							mainNodeId: null,
							objectId: null,
							pplId: 13701,
							type: 'star_v2',
							visibility: 'visible',
						},
						modified: '2022-09-13T09:56:17+0000',
						path: '/bios-people/patrice-roques',
						published: '2019-04-04T12:13:02+0000',
						source: 'voi',
						type: 'person',
				},
				type: 'Autres',
			},
		],
			boneId: 'f45e8f1b-b2a3-4ccc-85fe-71758ac85338',
			id: 'f45e8f1b-b2a3-4ccc-85fe-71758ac85338',
			indexed: '2023-05-03T11:05:23+0000',
			metas: {
				mainNodeId: null,
				objectId: null,
				pplId: 9362,
				type: 'star_v2',
				visibility: 'visible',
			},
			modified: '2022-09-13T09:56:17+0000',
			path: '/bios-people/brigitte-macron',
			paths: ['/bios-people/brigitte-macron', '/bios-people/brigitte-macron/'],
			published: '2017-06-30T14:09:47+0000',
			source: 'voi',
			type: 'person',
		showbtnDiapo: true,
		zodiacSign: {
			id: 1,
			label: 'Bélier',
			slug: 'aries',
		},
	}
} %}

{% include '@Front/src/views/shared/personLead/personLead.html.twig' with {
    person: args.isJournalist == 'true' ? Data.author : Data.person,
    isJournalist: args.isJournalist == 'true' ? true : false
} only %}
