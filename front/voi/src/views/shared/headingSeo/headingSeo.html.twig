{% set colorModifier = colorModifier is defined ? colorModifier : 'brand-sec' %}
{% set headingClassModifier = headingClassModifier is defined ? headingClassModifier : '' %}
{% set startText = startText is defined ? startText : '' %}
{% set tagModifier = tagModifier|default('h2') %}

<{{ tagModifier }} class="heading-section {{ headingClassModifier }}">
    {% autoescape false %}
    {% if startText is defined %}
        <span class="heading-sectionStart heading-sectionStart-{{ colorModifier }}">{{ startText }}</span>
    {% endif %}
    {% if endText is defined %}
        <span class="heading-sectionEnd">{{ endText }}</span>
    {% endif %}
    {% endautoescape %}
</{{ tagModifier }}>
