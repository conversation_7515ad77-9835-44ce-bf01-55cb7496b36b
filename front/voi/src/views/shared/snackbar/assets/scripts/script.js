import Tracking from 'Shared/tracking/tracking'
import consentCheck from 'Shared/utils/consent-check'

/**
 * ArticleSnackbar
 * @module Shared/Snackbar
 */
export default class Snackbar {
	/**
	 * Set default variables as properties
	 */
	constructor() {
		this.snackbar = document.querySelector('.snackbar')
		this.pageType = document.querySelector('[data-page-type]')?.getAttribute('data-page-type')
		this.articleType = this.snackbar.getAttribute('data-article-type')
		this.articleUrl = this.snackbar
			.querySelector('.snackbar-articleTitleLink')
			.getAttribute('href')
		this.triggerPoint = Math.round((document.body.clientHeight / 100) * 20)
		this.isDisplayed = false
		this.tracking = new Tracking()
	}

	/**
	 * Initialize the module
	 */
	init() {
		if (window.matchMedia('(max-width:767px)').matches) {
			consentCheck('gaReady', () =>
				this.tracking.onDisplaySnackbar(this.articleType, this.articleUrl)
			)
		} else {
			this.toggleActiveClass(window.scrollY)

			// Add event listener for document onScroll actions
			window.addEventListener(
				'scroll',
				() => {
					this.toggleActiveClass(window.scrollY)
				},
				{ passive: true }
			)
		}

		// Add event listener for articleSnackbar onClick actions
		this.snackbar.addEventListener('click', () => {
			this.tracking.onClickSnackbar(this.articleType, this.articleUrl)
		})
	}

	/**
	 * Toggle active class
	 * @param {Number} scrollDistance Scroll distance
	 */
	toggleActiveClass(scrollDistance) {
		if (scrollDistance >= this.triggerPoint) {
			this.snackbar.classList.add('active')
			!this.isDisplayed &&
				this.tracking.onDisplaySnackbar(this.articleType, this.pageType ?? 'other')
			this.isDisplayed = true
		} else {
			this.snackbar.classList.remove('active')
			this.isDisplayed = false
		}
	}
}
