.header-magazineSub {
	position: relative;
	display: flex;
	align-items: center;
	height: 100%;

	a {
		position: relative;
		display: flex;
		align-items: center;
		white-space: nowrap;
		height: 100%;
		padding: 0 var(--space-xs);
		color: var(--brandRed);
		font-family: var(--font-openSans);
		font-weight: 600;

		.icon-magazine {
			stroke: var(--brandRed);
			height: 17px;
			width: 17px;
			margin-right: var(--space-xs);
		}
	}
}

/* Desktop */
@media (--media-min-lg) {
	.header-magazineSub {
		padding: 0;

		a {
			padding: var(--space-md) var(--space-md);
			color: var(--white);
			font-size: var(--fs-18);
			position: relative;
			text-transform: initial;
			z-index: 1;

			.icon-magazine {
				stroke: var(--white);
				height: 22px;
				width: 22px;
				margin-right: var(--space-sm);
			}

			&::after {
				content: '';
				position: absolute;
				bottom: var(--space-xs);
				left: 15px;
				height: 3px;
				background-color: var(--white);
				width: 0;
				opacity: 0;
				transition: width 0.3s ease, opacity 0.3s ease;
			}

			&:hover {
				&::after {
					width: 50%;
					opacity: 1;
				}
			}
		}
	}
}
