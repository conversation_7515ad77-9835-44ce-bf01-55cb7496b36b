/**
 * ArticleCarousel
 * @module Shared/ArticleCarousel
 */
export default class ArticleCarousel {
	/**
	 * Set default variables as properties
	 */
	constructor(carousel) {
		this.carousel = {
			items: {
				container: carousel.querySelector('.articleCarousel-list'),
				first: carousel.querySelector('.articleCarousel-listItem:first-child'),
				last: carousel.querySelector('.articleCarousel-listItem:last-child')
			},
			buttons: {
				container: carousel.querySelector('.articleCarousel-buttons'),
				previous: carousel.querySelector('.articleCarousel-buttonsPrevious'),
				next: carousel.querySelector('.articleCarousel-buttonsNext')
			}
		}

		this.onClickCarouselButtons = this.onClickCarouselButtons.bind(this)
	}

	/**
	 * Initialize the module
	 */
	init() {
		this.addEvents()
	}

	/**
	 * Add event listeners
	 */
	addEvents() {
		this.carousel.buttons.container.addEventListener('click', this.onClickCarouselButtons)
	}

	/**
	 * On click carousel buttons' actions
	 * @param {Object} event Event listener data
	 */
	onClickCarouselButtons(event) {
		const isPreviousTarget = event.target.classList.contains('articleCarousel-buttonsPrevious')
		const scrollWidth = this.carousel.items.first.offsetWidth * 3 + 90
		const scrollFromTheLeftBy = isPreviousTarget ? -scrollWidth : scrollWidth

		this.carousel.items.container.scrollBy({
			left: scrollFromTheLeftBy,
			behavior: 'smooth'
		})

		setTimeout(() => this.updateCarouselButtons(), 500)
	}

	/**
	 * Update carousel buttons' statuses
	 */
	updateCarouselButtons() {
		const positionLeft = this.carousel.items.container.getBoundingClientRect().left
		const positionRight = this.carousel.items.container.getBoundingClientRect().right

		this.carousel.items.first.getBoundingClientRect().left - positionLeft === 0
			? this.carousel.buttons.previous.setAttribute('disabled', '')
			: this.carousel.buttons.previous.removeAttribute('disabled')

		this.carousel.items.last.getBoundingClientRect().right - positionRight === 0
			? this.carousel.buttons.next.setAttribute('disabled', '')
			: this.carousel.buttons.next.removeAttribute('disabled')
	}
}
