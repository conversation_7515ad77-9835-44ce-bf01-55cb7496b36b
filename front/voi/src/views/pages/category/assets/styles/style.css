.category {
	&-breadcrumb {
		margin-bottom: var(--space-md);
	}

	&-leadArticlesList {
		list-style-type: none;
		padding: 0;
	}

	&-leadArticlesListItem,
	&-ads {
		margin-bottom: var(--space-md);
	}
}

@media (--media-min-md) {
	.category {
		&-leadArticlesList {
			max-height: 1400px;
			display: flex;
			flex-flow: column wrap;
		}

		&-leadArticlesListItem {
			width: calc(50% - var(--space-md));
			margin-bottom: var(--space-lg);

			/* First two childs */
			&:nth-child(-n + 2) {
				max-height: 850px;
				margin-right: var(--space-lg);
			}

			/* All childs starting at 3 */
			&:nth-child(n + 3) {
				max-height: 625px;
			}
		}
	}
}

@media (--media-min-lg) {
	.category {
		&-leadArticlesList {
			max-height: 1700px;
		}
	}
}
