{% set Data = {
    category: {
        resource: {
            slug: 'beaute',
        },
    },
    categoryExternalLinkArticles: [
        [
            {
                authors: [
                {
                    activeOnBrandKeys: ['GAL', 'VOI'],
                    avatar: {
                    caption: '<PERSON>',
                    copyright: null,
                    original: {
                        height: null,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/800x600/quality/80/sarah-polak.jpg',
                        width: null,
                    },
                    title: '<PERSON>',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
                    description:
                    '<PERSON><PERSON><PERSON> de maquillage, ma première préoccupation est de découvrir les pépites de demain. Merci à mon 6e sens et mes 16 875 heures passées sur mon téléphone. Pas d\'inquiétude, je m\'aère l\'esprit en testant des machines de fitness loufoques en sachant pertinemment que je vais le regretter. Clin d\'oeil à mes courbatures d\'une semaine. \n',
                    existsOnBrandKeys: ['GAL', 'VOI'],
                    fullname: 'Sarah Polak',
                    hasPublicProfile: true,
                    resource: {
                    boneId: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    id: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    indexed: '2023-04-07T12:03:16+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'author',
                        visibility: 'visible',
                    },
                    modified: '2023-03-23T11:14:11+0000',
                    path: '/auteur/sarah-polak',
                    paths: ['/auteur/sarah-polak'],
                    published: '2021-04-01T08:03:24+0000',
                    source: 'voi',
                    type: 'author',
                },
                    resume: 'Rédactrice beauté',
                    social: {
                    email: '<EMAIL>',
                    facebook: '',
                    google: '',
                    instagram: '',
                    pinterest: '',
                    snapchat: '',
                    twitter: '',
                },
                },
            ],
                category: {
                depth: 2,
                format: 'CATEGORY',
                resource: {
                    boneId: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    id: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    indexed: '2023-04-07T12:03:16+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'rubrique',
                        visibility: 'visible',
                    },
                    modified: '2023-03-29T10:35:21+0000',
                    path: '/beaute',
                    published: '2017-06-29T03:26:23+0000',
                    slug: 'beaute',
                    source: 'voi',
                    type: 'category',
                },
                title: 'Beauté',
            },
                contentTypes: ['video'],
                draftLead:
                '{"blocks":[{"key":"30viu","text":"Hier, Brigitte Macron s\'est rendue au Musée d\'Art Moderne de Paris pour inaugurer l\'exposition de l\'artiste franco-norvégienne Anna-Eva Bergman aux c\u00f4tés de la reine Sonja de Norvège. \u00c0 cette occasion, la Première dame innove capillairement parlant. ","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}}',
                headlined: null,
                liveEndedAt: null,
                livePosts: [],
                liveStartedAt: null,
                medias: {
                imageCount: 1,
                images: [
                    {
                        caption: 'Brigitte Macron ',
                        copyright: 'Carlos Alvarez / Contributeur',
                        original: {
                        height: 1080,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~30~5fce6554-8fd0-4830-af1d-5dd82c1d0567.png/1920x1080/quality/80/brigitte-macron-a-69-ans-la-premiere-dame-ose-une-coiffure-rajeunissante-avec-un-accessoire-special.png',
                        width: 1920,
                    },
                        title:
                        'brigitte-macron-a-69-ans-la-premiere-dame-ose-une-coiffure-rajeunissante-avec-un-accessoire-special',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~30~5fce6554-8fd0-4830-af1d-5dd82c1d0567.png/{width}x{height}/{parameters}/{title}.{format}',
                    },
                ],
                videoCount: 1,
                videos: [
                    {
                        contentUrl: 'https://www.dailymotion.com/video/k3JxAW3HqLlYwVyJdIh',
                        duration: 70,
                        embed:
                        '<div class="article-video-embed article-video-embed-dailymotion" style="left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;"><iframe src="https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh" style="border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;" allowfullscreen scrolling="no" allow="encrypted-media; autoplay"></iframe></div>',
                        embedUrl: 'https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh',
                        feed: null,
                        iframely:
                        '{"links":{"player":[{"type":"text/html","rel":["player","html5","ssl","iframely"],"href":"https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh","media":{"aspect-ratio":1.7777777777777777,"scrolling":"no"},"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>"}],"thumbnail":[{"type":"image/jpeg","rel":["thumbnail","ssl"],"media":{"height":1080,"width":1920},"href":"https://s1.dmcdn.net/v/UbcMb1Zpzd7S2bOrP"}]},"rel":["player","html5","ssl","iframely"],"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>","meta":{"channel":"news","owner":"xbzwps","owner.username":"Voici","owner.screenname":"VOICI","id":"k6WUznTEwxsXk4yJdIh","private_id":"k6WUznTEwxsXk4yJdIh","updated_time":1674566087,"title":"Quelles coupes de cheveux quand on porte des lunettes ?","width":1920,"height":1080,"thumbnail_url":"https://s1.dmcdn.net/v/UbcMb1Zpzd7S2bOrP","duration":70,"site":"Dailymotion","public_id":"x8hj2qt","embed_url":"https://www.dailymotion.com/embed/video/k6WUznTEwxsXk4yJdIh","canonical":"https://www.dailymotion.com/video/k3JxAW3HqLlYwVyJdIh","medium":"video"}}',
                        picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1080,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUbcMb1Zpzd7S2bOrP/1920x1080/quality/80/quelles-coupes-de-cheveux-quand-on-porte-des-lunettes.jpg',
                            width: 1920,
                        },
                        title: 'quelles-coupes-de-cheveux-quand-on-porte-des-lunettes',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUbcMb1Zpzd7S2bOrP/{width}x{height}/{parameters}/{title}.{format}',
                    },
                        playlist: null,
                        provider: 'dailymotion',
                        providerId: 'k6WUznTEwxsXk4yJdIh',
                        sourceUrl: null,
                        start: null,
                        title: 'Quelles coupes de cheveux quand on porte des lunettes ?',
                    },
                ],
            },
                mostShared: {
                count: 1,
                date: '1986-01-25T00:00:00+0000',
            },
                mostViewed: {
                lastMonth: 200000,
                lastWeek: 200000,
                yesterday: 500,
            },
                pages: [],
                partnerTitle: null,
                published: '2023-03-30T13:39:28+0000',
                qualifiers: [],
                relatedPeople: [
                {
                    order: 1,
                    person: {
                    firstname: 'Brigitte',
                    fullname: 'Brigitte Macron',
                    jobs: 'Professeur',
                    lastname: 'Macron',
                    picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1536,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F28f7c2ab-2f8c-49c4-a25c-6e4e566d450c.2Ejpeg/2048x1536/quality/80/brigitte-macron.jpeg',
                            width: 2048,
                        },
                        title: 'brigitte-macron',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F28f7c2ab-2f8c-49c4-a25c-6e4e566d450c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 9362,
                    realFirstname: 'Brigitte',
                    realLastname: 'Trogneux',
                    resource: {
                        boneId: 'f45e8f1b-b2a3-4ccc-85fe-71758ac85338',
                        id: 'f45e8f1b-b2a3-4ccc-85fe-71758ac85338',
                        indexed: '2023-04-07T12:03:16+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 9362,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-09-13T09:56:17+0000',
                        path: '/bios-people/brigitte-macron',
                        published: '2017-06-30T14:09:47+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
            ],
                resource: {
                boneId: '5a18422c-949d-4741-8d02-38c2111c9dff',
                id: '5a18422c-949d-4741-8d02-38c2111c9dff',
                indexed: '2023-04-07T12:03:16+0000',
                metas: {
                    mainNodeId: '',
                    objectId: null,
                    type: 'news_multimedia',
                    visibility: 'visible',
                },
                modified: '2023-03-30T13:56:55+0000',
                path: null,
                paths: [],
                published: '2023-03-30T13:39:28+0000',
                source: 'voi',
                type: 'article',
            },
                secondCategory: null,
                slideshow: null,
                subhead: 'Brigitte Macron',
                tags: [
                {
                    label: 'beauté',
                    path: 'beauté',
                    resource: {
                    path: '/page/tag/beaute',
                },
                },
                {
                    label: 'coupe de cheveux',
                    path: 'coupe de cheveux',
                    url: {
                    path: '/page/tag/coupe-de-cheveux',
                },
                },
                {
                    label: 'cheveux',
                    path: 'cheveux',
                    resource: {
                    path: '/page/tag/cheveux',
                },
                },
                {
                    label: 'coiffure',
                    path: 'coiffure',
                    resource: {
                    path: '/page/tag/coiffure',
                },
                },
                {
                    label: 'queue-de-cheval',
                    path: 'queue-de-cheval',
                    resource: {
                    path: '/page/tag/queue-de-cheval',
                },
                },
                {
                    label: 'look',
                    path: 'look',
                    resource: {
                    path: '/page/tag/look',
                },
                },
            ],
                textLead:
                "Hier, Brigitte Macron s'est rendue au Musée d'Art Moderne de Paris pour inaugurer l\'exposition de l\'artiste franco-norvégienne Anna-Eva Bergman aux c\u00f4tés de la reine Sonja de Norvège. \u00c0 cette occasion, la Première dame innove capillairement parlant.",
                title: '\u00c7a change',
                titleSeo:
                'Brigitte Macron\u : à 69 ans, la Première dame ose une coiffure rajeunissante avec un accessoire spécial',
            },
            {
                authors: [
                {
                    activeOnBrandKeys: ['GAL', 'VOI'],
                    avatar: {
                    caption: 'Sarah Polak',
                    copyright: null,
                    original: {
                        height: null,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/800x600/quality/80/sarah-polak.jpg',
                        width: null,
                    },
                    title: 'Sarah Polak',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
                    description:
                    'Férue de maquillage, ma première préoccupation est de découvrir les pépites de demain. Merci à mon 6e sens et mes 16 875 heures passées sur mon téléphone. Pas d\'inquiétude, je m\'aère l\'esprit en testant des machines de fitness loufoques en sachant pertinemment que je vais le regretter. Clin d\'oeil à mes courbatures d\'une semaine. \n',
                    existsOnBrandKeys: ['GAL', 'VOI'],
                    fullname: 'Sarah Polak',
                    hasPublicProfile: true,
                    resource: {
                    boneId: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    id: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    indexed: '2023-04-07T12:03:23+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'author',
                        visibility: 'visible',
                    },
                    modified: '2023-03-23T11:14:11+0000',
                    path: '/auteur/sarah-polak',
                    paths: ['/auteur/sarah-polak'],
                    published: '2021-04-01T08:03:24+0000',
                    source: 'voi',
                    type: 'author',
                },
                    resume: 'Rédactrice beauté',
                    social: {
                    email: '<EMAIL>',
                    facebook: '',
                    google: '',
                    instagram: '',
                    pinterest: '',
                    snapchat: '',
                    twitter: '',
                },
                },
            ],
                category: {
                depth: 2,
                format: 'CATEGORY',
                resource: {
                    boneId: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    id: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    indexed: '2023-04-07T12:03:23+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'rubrique',
                        visibility: 'visible',
                    },
                    modified: '2023-03-29T10:35:21+0000',
                    path: '/beaute',
                    published: '2017-06-29T03:26:23+0000',
                    slug: 'beaute',
                    source: 'voi',
                    type: 'category',
                },
                title: 'Beauté',
            },
                contentTypes: ['video'],
                draftLead:
                '{"blocks":[{"key":"3lij6","text":"La bande-annonce du film Asteroid City a été dévoilée par Focus Features. Et surprise, Scarlett Johansson est complètement méconnaissable. Brune aux cheveux courts, l\'actrice a l\'allure d\'une femme fatale dans les années 50. On adore ! ","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":25,"length":13,"style":"ITALIC"}],"entityRanges":[],"data":{}}],"entityMap":{}}',
                headlined: null,
                liveEndedAt: null,
                livePosts: [],
                liveStartedAt: null,
                medias: {
                imageCount: 1,
                images: [
                    {
                        caption: 'Scarlett Johansson',
                        copyright: 'Vera Anderson / Contributeur',
                        original: {
                        height: 1080,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~30~b0ff607c-c9a3-4431-bfc9-4b8e20a5630d.png/1920x1080/quality/80/scarlett-johansson-meconnaissable-avec-sa-pixie-cut-coloree-en-brune-l-actrice-a-des-airs-d-audrey-hepburn.png',
                        width: 1920,
                    },
                        title:
                        'scarlett-johansson-meconnaissable-avec-sa-pixie-cut-coloree-en-brune-l-actrice-a-des-airs-d-audrey-hepburn',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~30~b0ff607c-c9a3-4431-bfc9-4b8e20a5630d.png/{width}x{height}/{parameters}/{title}.{format}',
                    },
                ],
                videoCount: 1,
                videos: [
                    {
                        contentUrl: 'https://www.dailymotion.com/video/k5jIRWVeBXELWJygJJO',
                        duration: 63,
                        embed:
                        '<div class="article-video-embed article-video-embed-dailymotion" style="left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;"><iframe src="https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO" style="border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;" allowfullscreen scrolling="no" allow="encrypted-media; autoplay"></iframe></div>',
                        embedUrl: 'https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO',
                        feed: null,
                        iframely:
                        '{"links":{"player":[{"type":"text/html","rel":["player","html5","ssl","iframely"],"href":"https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO","media":{"aspect-ratio":1.7777777777777777,"scrolling":"no"},"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>"}],"thumbnail":[{"type":"image/jpeg","rel":["thumbnail","ssl"],"media":{"height":1080,"width":1920},"href":"https://s1.dmcdn.net/v/UBj1i1Z60t7MHUmyK"}]},"rel":["player","html5","ssl","iframely"],"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>","meta":{"channel":"news","owner":"xbzwps","owner.username":"Voici","owner.screenname":"VOICI","id":"k5qJvzQ6YzkFv3ygJJO","private_id":"k5qJvzQ6YzkFv3ygJJO","updated_time":1662400922,"title":"Cheveux après 60 ans : les 4 meilleures coiffures courtes pour para\u00eetre plus jeune et \u00eatre vraiment tendance","width":1920,"height":1080,"thumbnail_url":"https://s1.dmcdn.net/v/UBj1i1Z60t7MHUmyK","duration":63,"site":"Dailymotion","public_id":"x8dhkrw","embed_url":"https://www.dailymotion.com/embed/video/k5qJvzQ6YzkFv3ygJJO","canonical":"https://www.dailymotion.com/video/k5jIRWVeBXELWJygJJO","medium":"video"}}',
                        picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1080,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUBj1i1Z60t7MHUmyK/1920x1080/quality/80/cheveux-apres-60-ans-les-4-meilleures-coiffures-courtes-pour-paraitre-plus-jeune-et-etre-vraiment-tendance.jpg',
                            width: 1920,
                        },
                        title:
                        'cheveux-apres-60-ans-les-4-meilleures-coiffures-courtes-pour-paraitre-plus-jeune-et-etre-vraiment-tendance',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUBj1i1Z60t7MHUmyK/{width}x{height}/{parameters}/{title}.{format}',
                    },
                        playlist: null,
                        provider: 'dailymotion',
                        providerId: 'k5qJvzQ6YzkFv3ygJJO',
                        sourceUrl: null,
                        start: null,
                        title:
                        'Cheveux après 60 ans : les 4 meilleures coiffures courtes pour para\u00eetre plus jeune et \u00eatre vraiment tendance',
                    },
                ],
            },
                mostShared: {
                count: 1,
                date: '1986-01-25T00:00:00+0000',
            },
                mostViewed: {
                lastMonth: 20000,
                lastWeek: 20000,
                yesterday: 200,
            },
                pages: [],
                partnerTitle: null,
                published: '2023-03-30T09:44:00+0000',
                qualifiers: [],
                relatedPeople: [
                {
                    order: 1,
                    person: {
                    firstname: 'Scarlett',
                    fullname: 'Scarlett Johansson',
                    jobs: 'actrice',
                    lastname: 'Johansson',
                    picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 173,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F166e1f0d-79da-4193-a45f-c2be271d597c.2Ejpeg/250x173/quality/80/scarlett-johansson.jpeg',
                            width: 250,
                        },
                        title: 'scarlett-johansson',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F166e1f0d-79da-4193-a45f-c2be271d597c.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 1221,
                    realFirstname: 'Scarlett Ingrid',
                    realLastname: 'Johansson',
                    resource: {
                        boneId: 'cd52f756-c84c-4a15-9221-3a1299188929',
                        id: 'cd52f756-c84c-4a15-9221-3a1299188929',
                        indexed: '2023-04-07T12:03:23+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 1221,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-10-03T08:12:23+0000',
                        path: '/bios-people/scarlett-johansson',
                        published: '2017-06-30T13:23:56+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
            ],
                resource: {
                boneId: 'da24a11a-a02f-4cfc-ad0d-8d9818c07a40',
                id: 'da24a11a-a02f-4cfc-ad0d-8d9818c07a40',
                indexed: '2023-04-07T12:03:23+0000',
                metas: {
                    mainNodeId: '',
                    objectId: null,
                    type: 'news_multimedia',
                    visibility: 'visible',
                },
                modified: '2023-03-30T09:44:00+0000',
                path: null,
                paths: [],
                published: '2023-03-30T09:44:00+0000',
                source: 'voi',
                type: 'article',
            },
                secondCategory: null,
                slideshow: null,
                subhead: 'Scarlett Johansson',
                tags: [
                {
                    label: 'beauté',
                    path: 'beauté',
                    resource: {
                    path: '/page/tag/beaute',
                },
                },
                {
                    label: 'coloration cheveux',
                    path: 'coloration cheveux',
                    resource: {
                    path: '/page/tag/coloration-cheveux',
                },
                },
                {
                    label: 'coupe de cheveux',
                    path: 'coupe de cheveux',
                    resource: {
                    path: '/page/tag/coupe-de-cheveux',
                },
                },
                {
                    label: 'cheveux',
                    path: 'cheveux',
                    resource: {
                    path: '/page/tag/cheveux',
                },
                },
                {
                    label: 'Blond',
                    path: 'Blond',
                    resource: {
                    path: '/page/tag/blond',
                },
                },
            ],
                textLead:
                "La bande-annonce du film Asteroid City a été dévoilée par Focus Features. Et surprise, Scarlett Johansson est complètement méconnaissable. Brune aux cheveux courts, l'actrice a l'allure d'une femme fatale dans les années 50. On adore !",
                title: 'Femme fatale',
                titleSeo:
                "Scarlett Johansson méconnaissable\u : avec sa pixie cut colorée en brune, l'actrice à des airs d'Audrey Hepburn",
            },
            {
                authors: [
                {
                    activeOnBrandKeys: ['GAL', 'VOI'],
                    avatar: {
                    caption: 'Sarah Polak',
                    copyright: null,
                    original: {
                        height: null,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/800x600/quality/80/sarah-polak.jpg',
                        width: null,
                    },
                    title: 'Sarah Polak',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
                    description:
                    'Férue de maquillage, ma première préoccupation est de découvrir les pépites de demain. Merci à mon 6e sens et mes 16 875 heures passées sur mon téléphone. Pas d\'inquiétude, je m\'aère l\'esprit en testant des machines de fitness loufoques en sachant pertinemment que je vais le regretter. Clin d\'oeil à mes courbatures d\'une semaine. \n',
                    existsOnBrandKeys: ['GAL', 'VOI'],
                    fullname: 'Sarah Polak',
                    hasPublicProfile: true,
                    resource: {
                    boneId: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    id: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    indexed: '2023-04-07T09:45:58+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'author',
                        visibility: 'visible',
                    },
                    modified: '2023-03-23T11:14:11+0000',
                    path: '/auteur/sarah-polak',
                    paths: ['/auteur/sarah-polak'],
                    published: '2021-04-01T08:03:24+0000',
                    source: 'voi',
                    type: 'author',
                },
                    resume: 'Rédactrice beauté',
                    social: {
                    email: '<EMAIL>',
                    facebook: '',
                    google: '',
                    instagram: '',
                    pinterest: '',
                    snapchat: '',
                    twitter: '',
                },
                },
            ],
                category: {
                depth: 2,
                format: 'CATEGORY',
                resource: {
                    boneId: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    id: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    indexed: '2023-04-07T09:45:58+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'rubrique',
                        visibility: 'visible',
                    },
                    modified: '2023-03-29T10:35:21+0000',
                    path: '/beaute',
                    published: '2017-06-29T03:26:23+0000',
                    slug: 'beaute',
                    source: 'voi',
                    type: 'category',
                },
                title: 'Beauté',
            },
                contentTypes: ['video'],
                draftLead:
                '{"blocks":[{"key":"en58n","text":"Saviez-vous que certains ingrédients que vous consommez au petit-déjeuner se trouvent dans la composition de vos produits de beauté ? Preuve en est avec ces 5 soins. ","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}}',
                headlined: null,
                liveEndedAt: null,
                livePosts: [],
                liveStartedAt: null,
                medias: {
                imageCount: 1,
                images: [
                    {
                        caption: null,
                        copyright: 'The Good Brigade',
                        original: {
                        height: 1080,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~29~626f85f2-9d38-4fc6-83fd-08cfb7eadc73.png/1920x1080/quality/80/masque-creme-lait-ces-5-soins-sont-fabriques-avec-des-ingredients-que-vous-mangez-au-petit-dejeuner.png',
                        width: 1920,
                    },
                        title:
                        'masque-creme-lait-ces-5-soins-sont-fabriques-avec-des-ingredients-que-vous-mangez-au-petit-dejeuner',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~29~626f85f2-9d38-4fc6-83fd-08cfb7eadc73.png/{width}x{height}/{parameters}/{title}.{format}',
                    },
                ],
                videoCount: 1,
                videos: [
                    {
                        contentUrl: 'https://www.dailymotion.com/video/k6ejSZgZ4BUPk3yqrhn',
                        duration: 65,
                        embed:
                        '<div class="article-video-embed article-video-embed-dailymotion" style="left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;"><iframe src="https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn" style="border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;" allowfullscreen scrolling="no" allow="encrypted-media; autoplay"></iframe></div>',
                        embedUrl: 'https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn',
                        feed: null,
                        iframely:
                        '{"links":{"player":[{"type":"text/html","rel":["player","html5","ssl","iframely"],"href":"https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn","media":{"aspect-ratio":1.7777777777777777,"scrolling":"no"},"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>"}],"thumbnail":[{"type":"image/jpeg","rel":["thumbnail","ssl"],"media":{"height":1080,"width":1920},"href":"https://s1.dmcdn.net/v/UKXZn1a1qDPGr9h26"}]},"rel":["player","html5","ssl","iframely"],"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>","meta":{"channel":"news","owner":"xbzwps","owner.username":"Voici","owner.screenname":"VOICI","id":"k7b3Q6h3bQw63Jyqrhn","private_id":"k7b3Q6h3bQw63Jyqrhn","updated_time":1666622944,"title":"Belle peau : peeling, dermabrasion, exfoliation... quelles sont les différences entre ces techniques ?","width":1920,"height":1080,"thumbnail_url":"https://s1.dmcdn.net/v/UKXZn1a1qDPGr9h26","duration":65,"site":"Dailymotion","public_id":"x8ev4z5","embed_url":"https://www.dailymotion.com/embed/video/k7b3Q6h3bQw63Jyqrhn","canonical":"https://www.dailymotion.com/video/k6ejSZgZ4BUPk3yqrhn","medium":"video"}}',
                        picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1080,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUKXZn1a1qDPGr9h26/1920x1080/quality/80/belle-peau-peeling-dermabrasion-exfoliation-quelles-sont-les-differences-entre-ces-techniques.jpg',
                            width: 1920,
                        },
                        title: 'belle-peau-peeling-dermabrasion-exfoliation-quelles-sont-les-differences-entre-ces-techniques',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUKXZn1a1qDPGr9h26/{width}x{height}/{parameters}/{title}.{format}',
                    },
                        playlist: null,
                        provider: 'dailymotion',
                        providerId: 'k7b3Q6h3bQw63Jyqrhn',
                        sourceUrl: null,
                        start: null,
                        title:
                        'Belle peau : peeling, dermabrasion, exfoliation... quelles sont les différences entre ces techniques ?',
                    },
                ],
            },
                mostShared: {
                count: 1,
                date: '1986-01-25T00:00:00+0000',
            },
                mostViewed: {
                lastMonth: 100,
                lastWeek: 100,
                yesterday: 0,
            },
                pages: [
                {
                    category: {
                    depth: 0,
                    format: 'EVENT',
                    resource: {
                        boneId: '8e1f2dcb-8fb8-40f8-b234-3c0d3397e781',
                        id: '8e1f2dcb-8fb8-40f8-b234-3c0d3397e781',
                        indexed: '2023-04-07T09:45:58+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            type: 'frontpage',
                            visibility: 'visible',
                        },
                        modified: '2023-04-01T07:43:15+0000',
                        path: '/',
                        published: '2018-05-02T20:05:19+0000',
                        slug: 'voi',
                        source: 'voi',
                        type: 'category',
                    },
                    title: 'VOI',
                },
                    children: [],
                    description:
                    'Pop-up Voici : un rendez-vous exceptionnel mode, food et beauté gratuit pour les lectrices',
                    draftBody:
                    '{"blocks":[{"key":"9i8fm","text":"La rédaction de Voici vous invite à vivre une expérience unique en plein c\u0153ur de Paris les 12 et 13 mai 2023 avec ses marques fétiches.  Au programme\u ? Des rencontres mode, beauté, food pour f\u00eater l\'été avant l\'heure ... et tout est gratuit\u !","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":0,"length":242,"style":"BOLD"}],"entityRanges":[],"data":{}}],"entityMap":{}}',
                    format: 'EVENT',
                    medias: {
                    imageCount: 1,
                    images: [
                        {
                            caption: 'Le Pop-Up Voici',
                            copyright: 'Lulu',
                            original: {
                            height: 500,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~28~9a7b24bc-c66d-4e90-b73d-08dc5e2b3028.png/1000x500/quality/80/voi.png',
                            width: 1000,
                        },
                            title: 'voi',
                            urlTemplate:
                            'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~28~9a7b24bc-c66d-4e90-b73d-08dc5e2b3028.png/{width}x{height}/{parameters}/{title}.{format}',
                        },
                    ],
                    videoCount: 0,
                    videos: [],
                },
                    partnerTitle:
                    "Tous nos coups de coeur mode, food et beauté à partager en attendant l'été",
                    path: [
                    {
                        depth: 0,
                        format: 'EVENT',
                        resource: {
                        boneId: '8e1f2dcb-8fb8-40f8-b234-3c0d3397e781',
                        id: '8e1f2dcb-8fb8-40f8-b234-3c0d3397e781',
                        indexed: '2023-04-07T09:45:58+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            type: 'frontpage',
                            visibility: 'visible',
                        },
                        modified: '2023-04-01T07:43:15+0000',
                        path: '/',
                        published: '2018-05-02T20:05:19+0000',
                        slug: 'voi',
                        source: 'voi',
                        type: 'category',
                    },
                        title: 'VOI',
                    },
                    {
                        depth: 1,
                        format: 'EVENT',
                        resource: {
                        boneId: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
                        id: '1dfc815e-693d-4af4-894a-762ce94cdc3b',
                        indexed: '2023-04-07T09:45:58+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            type: 'rubrique',
                            visibility: 'visible',
                        },
                        modified: '2023-02-17T12:26:18+0000',
                        path: '/evenements',
                        published: '2018-05-02T20:05:06+0000',
                        slug: 'evenements',
                        source: 'voi',
                        type: 'category',
                    },
                        title: 'Evènements',
                    },
                ],
                    resource: {
                    boneId: '17b2c08b-fac4-4a38-bad6-29919fb03a21',
                    id: '17b2c08b-fac4-4a38-bad6-29919fb03a21',
                    indexed: '2023-04-07T09:45:58+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'home_evenement',
                        visibility: 'visible',
                    },
                    modified: '2023-03-31T10:49:24+0000',
                    path: '/evenements/pop-up-voici',
                    paths: ['/evenements/pop-up-voici'],
                    published: '2021-11-02T13:23:07+0000',
                    source: 'voi',
                    type: 'event',
                },
                    tags: [
                    {
                        label: 'Pop up',
                        path: 'Pop up',
                        resource: {
                        path: '/page/tag/pop-up',
                    },
                    },
                    {
                        label: 'gratuit',
                        path: 'gratuit',
                        resource: {
                        path: '/page/tag/gratuit',
                    },
                    },
                    {
                        label: 'cuisine',
                        path: 'cuisine',
                        resource: {
                        path: '/page/tag/cuisine',
                    },
                    },
                    {
                        label: 'food',
                        path: 'food',
                        resource: {
                        path: '/page/tag/food',
                    },
                    },
                    {
                        label: 'ateliers',
                        path: 'ateliers',
                        resource: {
                        path: '/page/tag/ateliers',
                    },
                    },
                    {
                        label: 'DIY',
                        path: 'DIY',
                        resource: {
                        path: '/page/tag/diy',
                    },
                    },
                    {
                        label: 'No\u00ebl',
                        path: 'No\u00ebl',
                        resource: {
                        path: '/page/tag/noel',
                    },
                    },
                    {
                        label: 'cadeaux',
                        path: 'cadeaux',
                        resource: {
                        path: '/page/tag/cadeaux',
                    },
                    },
                    {
                        label: 'cadeaux no\u00ebl',
                        path: 'cadeaux no\u00ebl',
                        resource: {
                        path: '/page/tag/cadeaux-noel',
                    },
                    },
                    {
                        label: 'événement',
                        path: 'événement',
                        resource: {
                        path: '/page/tag/evenement',
                    },
                    },
                    {
                        label: 'rendez-vous',
                        path: 'rendez-vous',
                        resource: {
                        path: '/page/tag/rendez-vous',
                    },
                    },
                    {
                        label: 'lectrices',
                        path: 'lectrices',
                        resource: {
                        path: '/page/tag/lectrices',
                    },
                    },
                    {
                        label: 'rencontre-lectrices',
                        path: 'rencontre-lectrices',
                        resource: {
                        path: '/page/tag/rencontre-lectrices',
                    },
                    },
                    {
                        label: 'été',
                        path: 'été',
                        resource: {
                        path: '/page/tag/ete',
                    },
                    },
                    {
                        label: 'f\u00eate',
                        path: 'f\u00eate',
                        resource: {
                        path: '/page/tag/fete',
                    },
                    },
                    {
                        label: 'mode',
                        path: 'mode',
                        resource: {
                        path: '/page/tag/mode',
                    },
                    },
                    {
                        label: 'beauté',
                        path: 'beauté',
                        resource: {
                        path: '/page/tag/beaute',
                    },
                    },
                ],
                    title: 'Pop-up Voici',
                },
            ],
                partnerTitle: null,
                published: '2023-03-30T04:51:00+0000',
                qualifiers: [],
                relatedPeople: [],
                resource: {
                boneId: '3bc5b7b0-54f8-4a02-a712-6fede3c2bbe3',
                id: '3bc5b7b0-54f8-4a02-a712-6fede3c2bbe3',
                indexed: '2023-04-07T09:45:58+0000',
                metas: {
                    mainNodeId: '',
                    objectId: null,
                    type: 'news_multimedia',
                    visibility: 'visible',
                },
                modified: '2023-03-31T10:41:36+0000',
                path: null,
                paths: [],
                published: '2023-03-30T04:51:00+0000',
                source: 'voi',
                type: 'article',
            },
                secondCategory: null,
                slideshow: null,
                subhead: '',
                tags: [
                {
                    label: 'beauté',
                    path: 'beauté',
                    resource: {
                    path: '/page/tag/beaute',
                },
                },
                {
                    label: 'soin',
                    path: 'soin',
                    resource: {
                    path: '/page/tag/soin',
                },
                },
                {
                    label: 'soin visage',
                    path: 'soin visage',
                    resource: {
                    path: '/page/tag/soin-visage',
                },
                },
                {
                    label: 'cheveux',
                    path: 'cheveux',
                    resource: {
                    path: '/page/tag/cheveux',
                },
                },
                {
                    label: 'astuces beauté',
                    path: 'astuces beauté',
                    resource: {
                    path: '/page/tag/astuces-beaute',
                },
                },
            ],
                textLead:
                'Saviez-vous que certains ingrédients que vous consommez au petit-déjeuner se trouvent dans la composition de vos produits de beauté ? Preuve en est avec ces 5 soins.',
                title: 'Consommer sans modération',
                titleSeo:
                'Masque, crème, lait .. ces 5 soins sont fabriqués avec des ingrédients que vous mangez au petit-déjeuner',
            },
            {
                authors: [
                {
                    activeOnBrandKeys: ['GAL', 'VOI'],
                    avatar: {
                    caption: 'Sarah Polak',
                    copyright: null,
                    original: {
                        height: null,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/800x600/quality/80/sarah-polak.jpg',
                        width: null,
                    },
                    title: 'Sarah Polak',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
                    description:
                    'Férue de maquillage, ma première préoccupation est de découvrir les pépites de demain. Merci à mon 6e sens et mes 16 875 heures passées sur mon téléphone. Pas d\'inquiétude, je m\'aère l\'esprit en testant des machines de fitness loufoques en sachant pertinemment que je vais le regretter. Clin d\'oeil à mes courbatures d\'une semaine. \n',
                    existsOnBrandKeys: ['GAL', 'VOI'],
                    fullname: 'Sarah Polak',
                    hasPublicProfile: true,
                    resource: {
                    boneId: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    id: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    indexed: '2023-04-07T12:03:23+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'author',
                        visibility: 'visible',
                    },
                    modified: '2023-03-23T11:14:11+0000',
                    path: '/auteur/sarah-polak',
                    paths: ['/auteur/sarah-polak'],
                    published: '2021-04-01T08:03:24+0000',
                    source: 'voi',
                    type: 'author',
                },
                    resume: 'Rédactrice beauté',
                    social: {
                    email: '<EMAIL>',
                    facebook: '',
                    google: '',
                    instagram: '',
                    pinterest: '',
                    snapchat: '',
                    twitter: '',
                },
                },
            ],
                category: {
                depth: 2,
                format: 'CATEGORY',
                resource: {
                    boneId: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    id: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    indexed: '2023-04-07T12:03:23+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'rubrique',
                        visibility: 'visible',
                    },
                    modified: '2023-03-29T10:35:21+0000',
                    path: '/beaute',
                    published: '2017-06-29T03:26:23+0000',
                    slug: 'beaute',
                    source: 'voi',
                    type: 'category',
                },
                title: 'Beauté',
            },
                contentTypes: ['video'],
                draftLead:
                '{"blocks":[{"key":"3cha","text":"Julia Roberts, Philippine Leroy-Beaulieu ou encore Naomi Campbell, elles sont nombreuses à avoir adopté la frange après 50 ans. Et on comprend pourquoi ! C\'est le détail capillaire qui rajeunit instantanément. ","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}}',
                headlined: null,
                liveEndedAt: null,
                livePosts: [],
                liveStartedAt: null,
                medias: {
                imageCount: 1,
                images: [
                    {
                        caption: 'Penelope Cruz',
                        copyright: 'Juan Naharro Gimenez / Contributeur',
                        original: {
                        height: 1080,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~29~da7c134e-8d99-4555-bd30-f7a9342fa72e.png/1920x1080/quality/80/coupe-de-cheveux-apres-50-ans-ce-style-est-le-meilleur-pour-donner-un-coup-de-jeune-a-votre-visage.png',
                        width: 1920,
                    },
                        title:
                        'coupe-de-cheveux-apres-50-ans-ce-style-est-le-meilleur-pour-donner-un-coup-de-jeune-a-votre-visage',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~29~da7c134e-8d99-4555-bd30-f7a9342fa72e.png/{width}x{height}/{parameters}/{title}.{format}',
                    },
                ],
                videoCount: 1,
                videos: [
                    {
                        contentUrl: 'https://www.dailymotion.com/video/k3JxAW3HqLlYwVyJdIh',
                        duration: 70,
                        embed:
                        '<div class="article-video-embed article-video-embed-dailymotion" style="left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;"><iframe src="https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh" style="border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;" allowfullscreen scrolling="no" allow="encrypted-media; autoplay"></iframe></div>',
                        embedUrl: 'https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh',
                        feed: null,
                        iframely:
                        '{"links":{"player":[{"type":"text/html","rel":["player","html5","ssl","iframely"],"href":"https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh","media":{"aspect-ratio":1.7777777777777777,"scrolling":"no"},"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>"}],"thumbnail":[{"type":"image/jpeg","rel":["thumbnail","ssl"],"media":{"height":1080,"width":1920},"href":"https://s1.dmcdn.net/v/UbcMb1Zpzd7S2bOrP"}]},"rel":["player","html5","ssl","iframely"],"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>","meta":{"channel":"news","owner":"xbzwps","owner.username":"Voici","owner.screenname":"VOICI","id":"k364iqXGlmEnPLyJdIh","private_id":"k364iqXGlmEnPLyJdIh","updated_time":1674566087,"title":"Quelles coupes de cheveux quand on porte des lunettes ?","width":1920,"height":1080,"thumbnail_url":"https://s1.dmcdn.net/v/UbcMb1Zpzd7S2bOrP","duration":70,"site":"Dailymotion","public_id":"x8hj2qt","embed_url":"https://www.dailymotion.com/embed/video/k364iqXGlmEnPLyJdIh","canonical":"https://www.dailymotion.com/video/k3JxAW3HqLlYwVyJdIh","medium":"video"}}',
                        picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1080,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUbcMb1Zpzd7S2bOrP/1920x1080/quality/80/quelles-coupes-de-cheveux-quand-on-porte-des-lunettes.jpg',
                            width: 1920,
                        },
                        title: 'quelles-coupes-de-cheveux-quand-on-porte-des-lunettes',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUbcMb1Zpzd7S2bOrP/{width}x{height}/{parameters}/{title}.{format}',
                    },
                        playlist: null,
                        provider: 'dailymotion',
                        providerId: 'k364iqXGlmEnPLyJdIh',
                        sourceUrl: null,
                        start: null,
                        title: 'Quelles coupes de cheveux quand on porte des lunettes ?',
                    },
                ],
            },
                mostShared: {
                count: 1,
                date: '1986-01-25T00:00:00+0000',
            },
                mostViewed: {
                lastMonth: 70000,
                lastWeek: 70000,
                yesterday: 1000,
            },
                pages: [],
                partnerTitle: null,
                published: '2023-03-29T18:11:00+0000',
                qualifiers: [],
                relatedPeople: [
                {
                    order: 1,
                    person: {
                    firstname: 'Julia',
                    fullname: 'Julia Roberts',
                    jobs: 'productrice, actrice',
                    lastname: 'Roberts',
                    picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 960,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F1ef15ead-1ff1-460a-9b32-07972898c15e.2Ejpeg/700x960/quality/80/julia-roberts.jpeg',
                            width: 700,
                        },
                        title: 'julia-roberts',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F1ef15ead-1ff1-460a-9b32-07972898c15e.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 796,
                    realFirstname: 'Julia, Fiona',
                    realLastname: 'Roberts',
                    resource: {
                        boneId: 'bb41fe47-ed36-4ded-967c-e1999f242b7c',
                        id: 'bb41fe47-ed36-4ded-967c-e1999f242b7c',
                        indexed: '2023-04-07T12:03:23+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 796,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-10-10T15:57:01+0000',
                        path: '/bios-people/julia-roberts',
                        published: '2017-07-03T09:12:06+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
                {
                    order: 2,
                    person: {
                    firstname: 'Philippine',
                    fullname: 'Philippine Leroy-Beaulieu',
                    jobs: 'actrice',
                    lastname: 'Leroy-Beaulieu',
                    picture: {
                        caption: null,
                        copyright: 'AGENCE / BESTIMAGE',
                        original: {
                            height: 1536,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fvoi.2F2020.2F10.2F06.2F16dbe4d5-17d9-4807-9fca-74469ed87b6d.2Ejpeg/2048x1536/quality/80/philippine-leroy-beaulieu.jpeg',
                            width: 2048,
                        },
                        title: 'philippine-leroy-beaulieu',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fvoi.2F2020.2F10.2F06.2F16dbe4d5-17d9-4807-9fca-74469ed87b6d.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 14275,
                    realFirstname: null,
                    realLastname: null,
                    resource: {
                        boneId: '2b97f6b1-9bcd-4b98-90bf-645fa0b1b7fb',
                        id: '2b97f6b1-9bcd-4b98-90bf-645fa0b1b7fb',
                        indexed: '2023-04-07T12:03:23+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 14275,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-09-13T09:56:17+0000',
                        path: '/bios-people/philippine-leroy-beaulieu',
                        published: '2020-01-15T16:08:07+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
                {
                    order: 3,
                    person: {
                    firstname: 'Naomi',
                    fullname: 'Naomi Campbell',
                    jobs: 'chanteuse, Mannequin, actrice',
                    lastname: 'Campbell',
                    picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 173,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc721f441-bb07-4f99-a8b6-55947b6afec9.2Ejpeg/250x173/quality/80/naomi-campbell.jpeg',
                            width: 250,
                        },
                        title: 'naomi-campbell',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2Fc721f441-bb07-4f99-a8b6-55947b6afec9.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 1217,
                    realFirstname: null,
                    realLastname: null,
                    resource: {
                        boneId: '8d2d71c5-1a64-45b5-be36-ed09bba7939a',
                        id: '8d2d71c5-1a64-45b5-be36-ed09bba7939a',
                        indexed: '2023-04-07T12:03:23+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 1217,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-09-30T09:08:43+0000',
                        path: '/bios-people/naomi-campbell',
                        published: '2017-06-30T13:23:42+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
            ],
                resource: {
                boneId: 'afa14baa-09e7-4b33-b786-0f3fa527e46a',
                id: 'afa14baa-09e7-4b33-b786-0f3fa527e46a',
                indexed: '2023-04-07T12:03:23+0000',
                metas: {
                    mainNodeId: '',
                    objectId: null,
                    type: 'news_multimedia',
                    visibility: 'visible',
                },
                modified: '2023-03-29T18:11:00+0000',
                path: null,
                paths: [],
                published: '2023-03-29T18:11:00+0000',
                source: 'voi',
                type: 'article',
            },
                secondCategory: null,
                slideshow: null,
                subhead: 'Julia Roberts',
                tags: [
                {
                    label: 'beauté',
                    path: 'beauté',
                    resource: {
                    path: '/page/tag/beaute',
                },
                },
                {
                    label: 'cheveux',
                    path: 'cheveux',
                    resource: {
                    path: '/page/tag/cheveux',
                },
                },
                {
                    label: 'coiffure',
                    path: 'coiffure',
                    resource: {
                    path: '/page/tag/coiffure',
                },
                },
                {
                    label: 'coupe de cheveux',
                    path: 'coupe de cheveux',
                    resource: {
                    path: '/page/tag/coupe-de-cheveux',
                },
                },
                {
                    label: 'Frange',
                    path: 'Frange',
                    resource: {
                    path: '/page/tag/frange',
                },
                },
            ],
                textLead:
                "Julia Roberts, Philippine Leroy-Beaulieu ou encore Naomi Campbell, elles sont nombreuses à avoir adopté la frange après 50 ans. Et on comprend pourquoi ! C'est le détail capillaire qui rajeunit instantanément.",
                title: 'Cut cut',
                titleSeo:
                'Coupe de cheveux après 50 ans\u : ce style est le meilleur pour donner un coup de jeune à votre visage',
            },
            {
                authors: [
                {
                    activeOnBrandKeys: ['GAL', 'VOI'],
                    avatar: {
                    caption: 'Sarah Polak',
                    copyright: null,
                    original: {
                        height: null,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/800x600/quality/80/sarah-polak.jpg',
                        width: null,
                    },
                    title: 'Sarah Polak',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/~1~sarah polak~2023~03~23~e1e5cd6b-be6f-4e0a-9b99-d3ec9ff43e96.jpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
                    description:
                    'Férue de maquillage, ma première préoccupation est de découvrir les pépites de demain. Merci à mon 6e sens et mes 16 875 heures passées sur mon téléphone. Pas d\'inquiétude, je m\'aère l\'esprit en testant des machines de fitness loufoques en sachant pertinemment que je vais le regretter. Clin d\'oeil à mes courbatures d\'une semaine. \n',
                    existsOnBrandKeys: ['GAL', 'VOI'],
                    fullname: 'Sarah Polak',
                    hasPublicProfile: true,
                    resource: {
                    boneId: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    id: '087f2942-7d98-48e5-9e7b-a33e8bb085c6',
                    indexed: '2023-04-07T18:40:35+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'author',
                        visibility: 'visible',
                    },
                    modified: '2023-03-23T11:14:11+0000',
                    path: '/auteur/sarah-polak',
                    paths: ['/auteur/sarah-polak'],
                    published: '2021-04-01T08:03:24+0000',
                    source: 'voi',
                    type: 'author',
                },
                    resume: 'Rédactrice beauté',
                    social: {
                    email: '<EMAIL>',
                    facebook: '',
                    google: '',
                    instagram: '',
                    pinterest: '',
                    snapchat: '',
                    twitter: '',
                },
                },
            ],
                category: {
                depth: 2,
                format: 'CATEGORY',
                resource: {
                    boneId: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    id: 'a25affa2-e0c0-4cdc-be19-044dd6e820bf',
                    indexed: '2023-04-07T18:40:35+0000',
                    metas: {
                        mainNodeId: null,
                        objectId: null,
                        type: 'rubrique',
                        visibility: 'visible',
                    },
                    modified: '2023-03-29T10:35:21+0000',
                    path: '/beaute',
                    published: '2017-06-29T03:26:23+0000',
                    slug: 'beaute',
                    source: 'voi',
                    type: 'category',
                },
                title: 'Beauté',
            },
                contentTypes: ['video', 'slideshow'],
                draftLead:
                '{"blocks":[{"key":"3f84c","text":"\u00c0 la première du film Air au Regency Village Theatre à Los Angeles, Jennifer Lopez s\'est illustrée en beauté au bras de son mari Ben Affleck. L\'occasion pour la célèbre chanteuse de dévoiler une version romantique du chignon de mariée. Tutoriel. ","type":"unstyled","depth":0,"inlineStyleRanges":[{"offset":22,"length":3,"style":"ITALIC"}],"entityRanges":[],"data":{}}],"entityMap":{}}',
                headlined: null,
                liveEndedAt: null,
                livePosts: [],
                liveStartedAt: null,
                medias: {
                imageCount: 1,
                images: [
                    {
                        caption: 'Jennifer Lopez ',
                        copyright: 'bestimage',
                        original: {
                        height: 1080,
                        parameters: null,
                        url: 'https://www.voici.fr/imgre/fit/~1~voi~2023~03~28~ce0de7c5-3344-4e5b-b55c-fddd2ea8c76a.png/1920x1080/quality/80/jennifer-lopez-elle-adopte-le-chignon-de-mariee-le-plus-desirable-du-moment-voici-comment-le-reproduire.png',
                        width: 1920,
                    },
                        title:
                        'jennifer-lopez-elle-adopte-le-chignon-de-mariee-le-plus-desirable-du-moment-voici-comment-le-reproduire',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/~1~voi~2023~03~28~ce0de7c5-3344-4e5b-b55c-fddd2ea8c76a.png/{width}x{height}/{parameters}/{title}.{format}',
                    },
                ],
                videoCount: 1,
                videos: [
                    {
                        contentUrl: 'https://www.dailymotion.com/video/k5jIRWVeBXELWJygJJO',
                        duration: 63,
                        embed:
                        '<div class="article-video-embed article-video-embed-dailymotion" style="left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;"><iframe src="https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO" style="border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;" allowfullscreen scrolling="no" allow="encrypted-media; autoplay"></iframe></div>',
                        embedUrl: 'https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO',
                        feed: null,
                        iframely:
                        '{"links":{"player":[{"type":"text/html","rel":["player","html5","ssl","iframely"],"href":"https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO","media":{"aspect-ratio":1.7777777777777777,"scrolling":"no"},"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>"}],"thumbnail":[{"type":"image/jpeg","rel":["thumbnail","ssl"],"media":{"height":1080,"width":1920},"href":"https://s1.dmcdn.net/v/UBj1i1Z60t7MHUmyK"}]},"rel":["player","html5","ssl","iframely"],"html":"<div style=\\"left: 0; width: 100%; height: 0; position: relative; padding-bottom: 56.0417%;\\"><iframe src=\\"https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO\\" style=\\"border: 0; top: 0; left: 0; width: 100%; height: 100%; position: absolute;\\" allowfullscreen scrolling=\\"no\\" allow=\\"encrypted-media; autoplay\\"></iframe></div>","meta":{"channel":"news","owner":"xbzwps","owner.username":"Voici","owner.screenname":"VOICI","id":"k5jIRWVeBXELWJygJJO","private_id":"k5jIRWVeBXELWJygJJO","updated_time":1662400922,"title":"Cheveux après 60 ans : les 4 meilleures coiffures courtes pour para\u00eetre plus jeune et \u00eatre vraiment tendance","width":1920,"height":1080,"thumbnail_url":"https://s1.dmcdn.net/v/UBj1i1Z60t7MHUmyK","duration":63,"site":"Dailymotion","public_id":"x8dhkrw","embed_url":"https://www.dailymotion.com/embed/video/k5jIRWVeBXELWJygJJO","canonical":"https://www.dailymotion.com/video/k5jIRWVeBXELWJygJJO","medium":"video"}}',
                        picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 1080,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUBj1i1Z60t7MHUmyK/1920x1080/quality/80/cheveux-apres-60-ans-les-4-meilleures-coiffures-courtes-pour-paraitre-plus-jeune-et-etre-vraiment-tendance.jpg',
                            width: 1920,
                        },
                        title:
                        'cheveux-apres-60-ans-les-4-meilleures-coiffures-courtes-pour-paraitre-plus-jeune-et-etre-vraiment-tendance',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/https.3A.2F.2Fs1.2Edmcdn.2Enet.2Fv.2FUBj1i1Z60t7MHUmyK/{width}x{height}/{parameters}/{title}.{format}',
                    },
                        playlist: null,
                        provider: 'dailymotion',
                        providerId: 'k5jIRWVeBXELWJygJJO',
                        sourceUrl: null,
                        start: null,
                        title:
                        'Cheveux après 60 ans : les 4 meilleures coiffures courtes pour para\u00eetre plus jeune et \u00eatre vraiment tendance',
                    },
                ],
            },
                mostShared: {
                count: 1,
                date: '1986-01-25T00:00:00+0000',
            },
                mostViewed: {
                lastMonth: 5000,
                lastWeek: 5000,
                yesterday: 0,
            },
                pages: [],
                partnerTitle: null,
                published: '2023-03-28T19:01:00+0000',
                qualifiers: [],
                relatedPeople: [
                {
                    order: 1,
                    person: {
                    firstname: 'Jennifer',
                    fullname: 'Jennifer Lopez',
                    jobs: 'chanteuse, actrice',
                    lastname: 'Lopez',
                    picture: {
                        caption: null,
                        copyright: null,
                        original: {
                            height: 173,
                            parameters: null,
                            url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F851619b8-04d7-4388-b73b-33e2ed234c68.2Ejpeg/250x173/quality/80/jennifer-lopez.jpeg',
                            width: 250,
                        },
                        title: 'jennifer-lopez',
                        urlTemplate:
                        'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fprismamedia_people.2F2017.2F06.2F30.2F851619b8-04d7-4388-b73b-33e2ed234c68.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                    },
                    pplId: 1263,
                    realFirstname: 'Jennifer',
                    realLastname: 'Lynn Lopez',
                    resource: {
                        boneId: '551dd2fd-ac68-4ba3-b918-3e3627c56f32',
                        id: '551dd2fd-ac68-4ba3-b918-3e3627c56f32',
                        indexed: '2023-04-07T18:40:35+0000',
                        metas: {
                            mainNodeId: null,
                            objectId: null,
                            pplId: 1263,
                            type: 'star_v2',
                            visibility: 'visible',
                        },
                        modified: '2022-09-13T09:56:17+0000',
                        path: '/bios-people/jennifer-lopez',
                        published: '2017-06-30T13:23:28+0000',
                        source: 'voi',
                        type: 'person',
                    },
                },
                },
            ],
                resource: {
                boneId: '8630ea60-f9b9-47aa-9614-faa8e073f665',
                id: '8630ea60-f9b9-47aa-9614-faa8e073f665',
                indexed: '2023-04-07T18:40:35+0000',
                metas: {
                    mainNodeId: '',
                    objectId: null,
                    type: 'news_multimedia',
                    visibility: 'visible',
                },
                modified: '2023-03-28T19:01:00+0000',
                path: null,
                paths: [],
                published: '2023-03-28T19:01:00+0000',
                source: 'voi',
                type: 'article',
            },
                secondCategory: null,
                slideshow: {
                imageUrl:
                'https://one.img.pmdstatic.net/fit/https.3A.2F.2Fi.2Epmdstatic.2Enet.2Fvoi.2F2023.2F03.2F28.2F44c8bd0c-77d1-4fa1-a5f7-6c21b75a4f9e.2Ejpeg/800x450/q/80/thumbnail.jpg',
                resource: {
                    href: 'https://api-diaporama.prismamediadigital.com/v1/fr/slideshows/53955.json',
                    id: 'c3a3eb529d0d7b0ce380e4b6ae83338b',
                    metas: {
                        id: 53955,
                        source: 'voi',
                        type: 'slideshow',
                    },
                    url: 'https://photo.voici.fr/jennifer-lopez-et-ben-affleck-amoureux-matt-damon-et-sa-famille-dans-les-coulisses-de-lavant-premiere-du-film-air-a-los-angeles-53955',
                },
                title:
                "Jennifer Lopez et Ben Affleck amoureux, Matt Damon et sa famille... Dans les coulisses de l'avant-première du film Air à Los Angeles",
            },
                subhead: 'Jennifer Lopez',
                tags: [
                {
                    label: 'beauté',
                    path: 'beauté',
                    resource: {
                    path: '/page/tag/beaute',
                },
                },
                {
                    label: 'idée coiffure',
                    path: 'idée coiffure',
                    resource: {
                    path: '/page/tag/idee-coiffure',
                },
                },
                {
                    label: 'coiffure',
                    path: 'coiffure',
                    resource: {
                    path: '/page/tag/coiffure',
                },
                },
                {
                    label: 'cheveux',
                    path: 'cheveux',
                    resource: {
                    path: '/page/tag/cheveux',
                },
                },
                {
                    label: 'chignon',
                    path: 'chignon',
                    resource: {
                    path: '/page/tag/chignon',
                },
                },
                {
                    label: 'mariage',
                    path: 'mariage',
                    resource: {
                    path: '/page/tag/mariage',
                },
                },
            ],
                textLead:
                "\u00c0 la première du film Air au Regency Village Theatre à Los Angeles, Jennifer Lopez s'est illustrée en beauté au bras de son mari Ben Affleck. L'occasion pour la célèbre chanteuse de dévoiler une version romantique du chignon de mariée. Tutoriel.",
                title: 'Des vibrations nostalgiques',
                titleSeo:
                'Jennifer Lopez\u : elle adopte le chignon de mariée le plus désirable du moment, voici comment le reproduire',
            },
        ],
    ],
    categoryExternalLinksTags: [
        {
            isOnline: true,
            media: null,
            medias: {
            imageCount: 1,
            images: [
                {
                    caption: null,
                    copyright: null,
                    original: {
                    height: 3499,
                    parameters: null,
                    url: 'https://www.voici.fr/imgre/fit/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fvoi.2F2019.2F02.2F04.2Fb1026a3c-1ac2-4802-bd9f-640cf7d3b3ab.2Ejpeg/5248x3499/quality/80/voi.jpeg',
                    width: 5248,
                },
                    title: 'voi',
                    urlTemplate:
                    'https://www.voici.fr/imgre/{transformation}/http.3A.2F.2Fprd2-bone-image.2Es3-website-eu-west-1.2Eamazonaws.2Ecom.2Fvoi.2F2019.2F02.2F04.2Fb1026a3c-1ac2-4802-bd9f-640cf7d3b3ab.2Ejpeg/{width}x{height}/{parameters}/{title}.{format}',
                },
            ],
            videoCount: 0,
            videos: [],
        },
            pins: [],
            slug: 'les-cheveux',
            title: 'Les cheveux',
            type: 'url',
            url: '/page/tag/cheveux',
        }
    ]
} %}

{% include '@Front/src/views/pages/category/variant/newsList/newsList.html.twig' with {
    title: Data.categoryExternalLinksTags[0].title,
    articles: Data.categoryExternalLinkArticles[0][:5],
    category: Data.category,
    seeMoreLink: '',
} only %}
