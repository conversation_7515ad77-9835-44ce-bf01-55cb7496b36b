<div class="pages">
    {% embed "@Front/src/views/shared/pageGrid/pageGrid.html.twig" with {page: "category"} %}
        {% block leftColumn %}
            <div class="pages-news">
                {% include "@Front/src/views/shared/titles/titles.html.twig" with {
                    title: article.title,
                    tag: "h1",
                    colorModifier,
                } %}

                {% for iteration in range (1, 2) %}
                    {% set startIndex = loop.first ? 0 : 5 %}
                    {% set adType = loop.first ? "Pave-Haut" : "Pave-Bas" %}

                    {% include "@Front/src/views/shared/articleList/articleList.html.twig" with {
                        articles: latestArticles[startIndex:5],
                        promoteFirst: true
                    } %}

                    <div class="pages-ads coreAdsPlacer mobileTabletOnly">
                        {{ coreads_tag(adType, {device: ["mobile", "tablet"]}) }}
                    </div>
                {% endfor %}
            </div>
        {% endblock %}

        {% block rightColumn %}
            <div class="pages-ads coreAdsPlacer p-sticky desktopOnly">
                {{ coreads_tag("Pave-Haut", {device: ["desktop"]}) }}
            </div>
        {% endblock %}
    {% endembed %}

    {% embed "@Front/src/views/shared/pageGrid/pageGrid.html.twig" with {page: "category"} %}
        {% block leftColumn %}
            {% include "@Front/src/views/shared/titles/titles.html.twig" with {
                title: "Encore + de news",
                tag: "h2",
                colorModifier
            } %}

            {% set startIndex = 10 %}
            {% for iteration in range (1, 3) %}
                {% include "@Front/src/views/shared/articleList/articleList.html.twig" with {
                    articles: latestArticles[startIndex:5],
                    promoteFirst: true
                } %}
                {# Update startIndex for next iteration #}
                {% set startIndex = startIndex + 5 %}

                {% if not loop.last %}
                    <div class="pages-ads coreAdsPlacer mobileTabletOnly">
                        {{ coreads_tag("Pave-Bas2", {device: ["mobile", "tablet"]}) }}
                    </div>
                {% endif %}
            {% endfor %}
        {% endblock %}

        {% block rightColumn %}
            <div class="pages-ads coreAdsPlacer p-sticky desktopOnly">
                {{ coreads_tag("Pave-Bas", {device: ["desktop"]}) }}
            </div>
        {% endblock %}
    {% endembed %}
</div>
