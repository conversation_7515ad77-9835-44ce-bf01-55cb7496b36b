{% set desktopCoreAdsFormat = desktopCoreAdsFormat|default('Pave-bas') %}
{% set displayOutbrain = displayOutbrain|default(false) %}
<div class="articleRebounds">
    {# SideArticles (ArticleCarousel): Common #}
    {% include '@Front/src/views/pages/article/components/articleRebounds/articleReboundsMoreNews.html.twig' with {
        article,
        sideArticles: similarArticles[:6],
        articleRelatedPersonFullname: article.person.fullname|default('')
    } %}
    {% embed '@Front/src/views/shared/pageGrid/pageGrid.html.twig' with {page: 'article'} %}
		{% block leftColumn %}
            {# PromoBasArticle: Common #}
            {% include '@Front/src/views/shared/promoBasArticle/promoBasArticle.html.twig' with {category: article.parentCategory(1).slug} %}
            {# Outbrain: Not in ArticleSlideshow #}
            {% if displayOutbrain %}
                {% include '@Front/src/views/shared/outbrain/outbrain.html.twig' with {
                    widgetId: 'AR_1',
                    dataSrc: app.request.uri
                } only %}
            {% endif %}

            {% if article.parentCategory(1).slug == 'news-people' %}
                {% include '@Front/src/views/shared/storylines/storylines.html.twig' with {
                    title: 'Instant Potins',
                    subtitle: 'Les moments people qu\'il ne fallait pas manquer',
                    colorModifier: article.parentCategory(1).slug
                } only %}
            {% endif %}

            {# SimilarArticles: Common #}
            {% if similarArticles|length > 0 %}
                <div class="articleRebounds-similarArticles">
                    {% include '@Front/src/views/shared/titles/titles.html.twig' with {
                        title: 'Sur le même thème',
                        tag: 'h2',
                        sizeModifier: 'small',
                        colorModifier: article.parentCategory(1).slug
                    } %}
                    {% include '@Front/src/views/shared/articleList/articleList.html.twig' with {
                        articles: similarArticles[6:14],
                        mobileAdFormat: 'Pave-Bas'
                    } only %}
                </div>
            {% endif %}
        {% endblock %}
        {% block rightColumn %}
            <div class="pageGrid-stickyContainer">
                {# Mobile & tablet ad: 'Pave-bas2' #}
                <div class="articleRebounds-ads coreAdsPlacer p-sticky mobileTabletOnly">
                    {{ coreads_tag('Pave-Bas2', {device: ['mobile', 'tablet']}) }}
                </div>
                <div class="articleRebounds-ads coreAdsPlacer p-sticky desktopOnly">
                    {# Desktop ad: 'Pave-bas' by default, 'Pave-bas2' in ArticleSlideshow #}
                    {{ coreads_tag(desktopCoreAdsFormat, {device: ['desktop']}) }}
                </div>
            </div>
            <div class="pageGrid-bottomContainer">
                {# BoxAboPrisma: Common #}
                <div class="articleRebounds-boxaboprisma">
                    {% include '@Front/src/views/shared/boxAboPrisma/boxAboPrisma.html.twig' %}
                </div>
            </div>
        {% endblock %}
    {% endembed %}
    {# RelatedPeople: Common #}
    {% if relatedPeople|length > 0 %}
        <div class="articleRebounds-relatedPeople">
            {% include '@Front/src/views/shared/titles/titles.html.twig' with {
                title: 'Son entourage',
                tag: 'h2',
                sizeModifier: 'small'
            } %}
            {% include '@Front/src/views/shared/relatedPeople/relatedPeople.html.twig' with {
                people: relatedPeople[:10]
            } only %}
        </div>
    {% endif %}
</div>
