{% extends '@Front/src/views/layout.html.twig' %}

{% block metaMainTitleContent %}{{ content.publicname }} - Découvrez ses publications sur Voici{% endblock %}
{% block metaDescriptionContent %}{{ content.description|u.truncate(147, '...', false) }}{% endblock %}
{% block metaPhoto %}{% if content.photo %}{{ pmd_common_photo_url(content.photo, 150, {height: 150}) }}{% endif %}{% endblock %}

{%- block metas_header -%}
    {{ parent() }}
    <link rel="canonical" href="{{ app.request.pathInfo }}">

    {% if content.photo %}
        <meta property="og:image:type" content="image/jpeg" />
        <meta property="og:image:width" content="150" />
        <meta property="og:image:height" content="150" />
    {% endif %}
{%- endblock -%}

{% import "@Front/src/views/shared/macros/responsivePicture/index.html.twig" as macroPicture %}
{% import "@Front/src/views/shared/macros/callToAction/index.html.twig" as macroCta %}

{% block pageSpecificCss %}
    {%- include '@web/assets/voi/modern/templates/author-styles.html.twig' -%}
{% endblock %}

{%- block pageSpecificJs -%}
    {% include '@web/assets/voi/modern/templates/author-scripts.html.twig' %}
    {% include '@web/assets/voi/legacy/templates/author-scripts.html.twig' ignore missing %}
{%- endblock -%}

{% block content %}
    <div class="container author" data-page-type="author">
        {%- include '@Front/src/views/shared/breadcrumb/breadcrumb.html.twig' with {path: [{title: 'Equipe éditoriale', path: path('pmd_common.authors.show')}]} -%}
        {%- include '@Front/src/views/shared/personLead/personLead.html.twig' with {
            person: content,
            isJournalist: true
        } -%}

        <div class="coreAdsPlacer withBkg mobileOnly">
            {{ coreads_tag('Pave-Haut', {'device': 'mobile'}) }}
        </div>

        {% embed "@Front/src/views/shared/pageGrid/pageGrid.html.twig" with {page: 'author'} %}
            {%- block leftColumn -%}
                {# Biographie #}
                {% if content.signature is defined and content.signature is not empty %}
                    {% include "@Front/src/views/shared/titles/titles.html.twig" with {
                        title: "Sa biographie",
                        tag: "h2"
                    } only %}
                    <p class="author-biography">
                        {{ content.signature|default('') }}
                    </p>
                {% endif %}
                {# /Biographie #}

                {# Articles #}
                {% include "@Front/src/views/shared/titles/titles.html.twig" with {
                    title: "Ses articles",
                    tag: "h2"
                } only %}
                {% include "@Front/src/views/shared/articleList/articleList.html.twig" with {
                    articles: content.contentsDocuments
                } only %}
                {# /Articles #}
            {%- endblock -%}

            {%- block rightColumn -%}
                <div class="pageGrid-stickyContainer">
                    <div class="coreAdsPlacer withBkg p-sticky desktopTabletOnly">
                        {{ coreads_tag('Pave-Haut', {'device': ['desktop', 'tablet']}) }}
                    </div>
                </div>

                {% if content.contentsDocuments.total <= 6 %}
                    {% include '@Front/src/views/shared/boxAboPrisma/boxAboPrisma.html.twig' %}
                {% endif %}
            {%- endblock -%}
        {% endembed %}
    </div>
{% endblock %}

{% block spriteSVG %}
    <div class="spritesvg">
        {{ source("@web" ~ asset("sprites/author.svg"), ignore_missing = true) }}
    </div>
{% endblock %}
