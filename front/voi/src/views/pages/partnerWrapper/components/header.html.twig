{%- include '@web/assets/voi/modern/templates/partials-styles.html.twig' -%}
<script>
    window.pmcstarter = window.pmcstarter || function () {
        (window.pmcstarter.q = window.pmcstarter.q || []).push(arguments)
    }
</script>
<div class="spritesvg">
    {{ source("@web" ~ asset("sprites/partials.svg"), ignore_missing = true) }}
</div>

<div class="partner-wrapper fonts-loaded"
{% if pmc_env != 'prd' %}
    data-pmc-starter-conf="{{ {env: pmc_env, host: app.request.host, trigram: appConfig.brand, ua: appConfig.domain}|json_encode }}"
{% endif %}
>
    {% include '@Front/src/views/shared/header/header.html.twig' %}
</div>
