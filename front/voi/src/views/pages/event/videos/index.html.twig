{% extends '@Front/src/views/layout.html.twig' %}
{% import "@Front/src/views/shared/macros/responsivePicture/index.html.twig" as picture %}

{% block metas_header %}
    {{ parent() }}
    <link rel="canonical" href="{{ category.url({'scheme': 'https', 'offset': offset}, false) }}">
{% endblock %}

{% block pageSpecificCss %}
    {% include '@web/assets/voi/modern/templates/eventVideos-styles.html.twig' %}
{% endblock %}

{% block pageSpecificJs %}
    {% include '@web/assets/voi/modern/templates/eventVideos-scripts.html.twig' %}]
    {% include '@web/assets/voi/legacy/templates/eventVideos-scripts.html.twig' ignore missing %}
{% endblock %}

{% block header %}
    {{ parent() }}
    {{- render_esi(path('pmd_voi_esi_event_header', {id: event.id, tab: tab|default('index')})) -}}
{% endblock %}

{% block content %}
    <div class="container eventVideos">
        {% include '@Front/src/views/shared/breadcrumb/breadcrumb.html.twig' with {
            path: category.path
        } only %}

        {% embed "@Front/src/views/shared/pageGrid/pageGrid.html.twig" with {page: 'event-video'} %}
            {% block leftColumn %}
                <div class="eventVideos-list">
                    {% for article in latestArticleVideos|slice(0, 30) %}
                        <div class="eventVideos-listItem">
                            {% include '@Front/src/views/shared/articleCard/articleDefaultCard.html.twig' with {
                                article,
                                imageResize: ['200x200', '164x164', '200x200', '164x164']
                            } %}
                        </div>
                    {% endfor %}
                </div>
            {% endblock %}

            {% block rightColumn %}
                <div class="pageGrid-stickyContainer">
                    <div class="coreAdsPlacer p-sticky">
                        {{ coreads_tag('Pave-Haut') }}
                    </div>
                </div>
            {% endblock %}
        {% endembed %}

        {% include '@Front/src/views/shared/pagination/pagination.html.twig' with pagination_offset_exponentials_parameters(category.contents.total, limit) %}
    </div>
{% endblock %}

{% block spriteSVG %}
    <div class="spritesvg">
        {{ source("@web" ~ asset("sprites/eventVideos.svg"), ignore_missing = true) }}
    </div>
{% endblock %}
