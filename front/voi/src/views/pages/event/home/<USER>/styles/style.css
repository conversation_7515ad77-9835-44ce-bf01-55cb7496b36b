.event {
	&Banner {
		margin: 0 -10px;
	}

	&Desc {
		font-size: var(--fs-18);
		padding: 0 var(--space-xs);
		margin-bottom: var(--space-md);
	}

	&Section {
		margin-bottom: var(--space-md);
	}

	&StarsList {
		background-color: none;
		padding: 0;
	}

	&DefaultList {
		.titleDefault {
			margin-bottom: var(--space-sm);
		}

		.articleList {
			margin-bottom: var(--space-md);
		}

		.coreAdsPlacer {
			margin-bottom: var(--space-md);
		}
	}

	&Breadcrumb {
		.breadcrumb {
			padding-bottom: var(--space-sm);
		}
	}
}

.heading-section {
	margin-bottom: var(--space-md);
}

@media (--media-min-md) {
	.event {
		&Banner {
			margin: 0;
		}

		&Desc {
			font-size: var(--fs-20);
			margin-bottom: var(--space-lg);
		}

		&Section {
			margin-bottom: var(--space-lg);
		}

		&Breadcrumb {
			.breadcrumb {
				background-color: var(--white);
				padding-bottom: var(--space-md);
			}
		}
	}
}
