import TrackElements from '../trackElements'
import pushInDatalayer from '../pushInDataLayer'
import mockIntersectionObserver from '../mocks/intesection-observer'

jest.mock('../pushInDataLayer')

describe('TrackElements', () => {
	let entries
	let mockElement
	let mockLinkElement

	beforeEach(() => {
		mockElement = document.createElement('div')
		mockLinkElement = document.createElement('a')
		mockLinkElement.href = 'https://example.com'
		mockIntersectionObserver()

		Object.defineProperty(window, 'ga', {
			writable: true,
			value: jest.fn()
		})

		Object.defineProperty(window, 'location', {
			writable: true,
			value: {
				assign: jest.fn()
			}
		})

		entries = [
			{
				element: mockElement,
				tracking: {
					click: { ga3: {}, ga4: {} },
					display: { ga3: {}, ga4: {} }
				}
			},
			{
				element: mockLinkElement,
				tracking: {
					click: { ga3: {}, ga4: {} }
				}
			}
		]
	})

	afterEach(() => {
		jest.clearAllMocks()
	})

	it('should assign an empty array if no entries was passed', () => {
		const trackElements = new TrackElements()
		expect(Array.isArray(trackElements.entries)).toEqual(true)
	})

	it('should initialize and add events to elements', () => {
		const trackElements = new TrackElements(entries)
		jest.spyOn(trackElements, 'addEvents')
		trackElements.init()

		expect(trackElements.addEvents).toHaveBeenCalledTimes(entries.length)
	})

	it('should not add events if element is null', () => {
		const trackElements = new TrackElements([{ element: null, tracking: {} }])
		jest.spyOn(console, 'error').mockImplementation(() => {})
		trackElements.init()

		expect(console.error).toHaveBeenCalledWith('Element is null in this entry :', {
			element: null,
			tracking: {}
		})
	})

	it('should not add click event listener if no click tracking', () => {
		const mockElement = document.createElement('div')
		const entry = {
			element: mockElement,
			tracking: { display: { ga3: {}, ga4: {} } }
		}
		const trackElements = new TrackElements([entry])
		jest.spyOn(mockElement, 'addEventListener')
		trackElements.addEvents(entry)
		expect(mockElement.addEventListener).not.toHaveBeenCalledWith('click', expect.any(Function))
	})

	it('should not send ga3', () => {
		const mockElement = document.createElement('a')
		const clickEvent = new MouseEvent('click', { bubbles: true })

		const entry = {
			element: mockElement,
			tracking: { click: { ga4: {} } }
		}
		const trackElements = new TrackElements([entry])
		jest.spyOn(trackElements, 'handleElementClick')
		trackElements.init()
		mockElement.dispatchEvent(clickEvent)
		expect(window.ga).not.toHaveBeenCalled()
	})

	it('should not send ga4', () => {
		const mockElement = document.createElement('button')
		const clickEvent = new MouseEvent('click', { bubbles: true })

		const entry = {
			element: mockElement,
			tracking: { click: { ga3: {} } }
		}
		const trackElements = new TrackElements([entry])
		jest.spyOn(trackElements, 'handleElementClick')
		trackElements.init()
		mockElement.dispatchEvent(clickEvent)
		expect(pushInDatalayer).not.toHaveBeenCalled()
	})

	it('should handle element click event and call GA tracking', () => {
		const trackElements = new TrackElements(entries)
		const clickEvent = new MouseEvent('click', { bubbles: true })

		jest.spyOn(location, 'assign').mockImplementation(() => {})

		trackElements.init()
		mockElement.dispatchEvent(clickEvent)

		expect(pushInDatalayer).toHaveBeenCalledWith([entries[0].tracking.click.ga4])
		expect(window.ga).toHaveBeenCalledWith('send', 'event', entries[0].tracking.click.ga3)
	})

	it('should prevent default on link click that not open new tab', () => {
		const trackElements = new TrackElements(entries)
		jest.spyOn(trackElements, 'ga4CallBack')
		const clickEvent = new MouseEvent('click', { bubbles: true })

		jest.spyOn(location, 'assign').mockImplementation(() => {})
		jest.spyOn(clickEvent, 'preventDefault')

		trackElements.init()
		mockLinkElement.dispatchEvent(clickEvent)

		expect(clickEvent.preventDefault).toHaveBeenCalled()
		expect(trackElements.ga4CallBack).toHaveBeenCalledWith(mockLinkElement.href)
		expect(pushInDatalayer).toHaveBeenCalledWith([entries[1].tracking.click.ga4])
	})

	it('should prevent default on link click that not open new tab', () => {
		mockLinkElement = document.createElement('a')
		mockLinkElement.href = 'https://example.com'
		mockLinkElement.setAttribute('target', '_blank')

		entries[1].element = mockLinkElement

		const trackElements = new TrackElements(entries)
		const clickEvent = new MouseEvent('click', { bubbles: true })

		jest.spyOn(location, 'assign').mockImplementation(() => {})
		jest.spyOn(clickEvent, 'preventDefault')

		trackElements.init()
		mockLinkElement.dispatchEvent(clickEvent)

		expect(clickEvent.preventDefault).not.toHaveBeenCalled()
		expect(location.assign).not.toHaveBeenCalled()
		expect(pushInDatalayer).toHaveBeenCalledWith([entries[1].tracking.click.ga4])
	})

	it('should observe element intersection and call GA tracking', () => {
		const trackElements = new TrackElements(entries)
		trackElements.init()

		const observerCallback = jest.fn()
		jest.spyOn(global, 'IntersectionObserver').mockImplementation((cb) => {
			observerCallback.mockImplementation(cb)
			return {
				observe: jest.fn(),
				disconnect: jest.fn()
			}
		})

		trackElements.observeIntersecting(entries[0])
		observerCallback([{ isIntersecting: true }])

		expect(pushInDatalayer).toHaveBeenCalledWith([entries[0].tracking.display.ga4])
	})

	it('should observe element intersection and not call GA tracking', () => {
		const trackElements = new TrackElements(entries)
		trackElements.init()

		const observerCallback = jest.fn()
		jest.spyOn(global, 'IntersectionObserver').mockImplementation((cb) => {
			observerCallback.mockImplementation(cb)
			return {
				observe: jest.fn(),
				disconnect: jest.fn()
			}
		})

		trackElements.observeIntersecting(entries[0])
		observerCallback([{ isIntersecting: false }])

		expect(pushInDatalayer).not.toHaveBeenCalled()
	})
})
