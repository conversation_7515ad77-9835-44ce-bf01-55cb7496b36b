{% import '@CommonFront/macros/svg/views/svg.html.twig' as macroSvg %}

{% set rating = rating is defined ? rating : 0 %}
{% set decimal = rating|round(0, 'floor') %}
{% set float = rating - rating|round(0, 'floor') %}
{% set tolerance = 0.00001 %} {# prevent precision issue in float comparison #}
{% set count = count is defined ? count : 0 %}
{% set maxStars = 5 %}
{% set showMax = showMax is defined ? showMax : false %}
{% set classes = classes is defined ? classes : [] %}

{% if rating > 0 %}
    <div class="recipeRating {{ classes|join(' ') }}">
        <span class="recipeRating-value" data-recipeRating-value>{{ rating }}</span>
        {% if showMax %}
            <span class="recipeRating-max">/<small>{{ maxStars }}</small></span>
        {% endif %}
         <span class="recipeRating-icons">
            {% for i in 1..maxStars %}
                {% set filledHalf = i == decimal + 1 and float > (0 + tolerance) ? 'star-half' : null %}
                {% set filled = i <= rating or (i == decimal + 1 and float > (0.6 + tolerance)) ? 'star' : null %}
                {% set star = filled ?? filledHalf ?? 'star-empty' %}
                {{
                    macroSvg.getSvgTag(star, {
                    attributes: {
                        class: 'recipeRating-icon',
                    }})
                }}
            {% endfor %}
        </span>
        {% if count > 0 %}
            <span class="recipeRating-count">
                <span data-recipeRating-count>{{ count }}</span>
                &nbsp;
                <span data-recipeRating-label>note{{ count > 1 ? 's' : '' }}</span>
            </span>
        {% endif %}
    </div>
{% endif %}
