import PmcBridge from 'commonSrc/assets/scripts/utils/pmcBridge'
import Auth from './modules/auth'
import Login from './modules/login'
import Profile from './modules/profile'

class AccountManager {
	async init() {
		const femLogin = document.querySelector('fem-login#main-login')
		const profile = new Profile()
		const login = new Login(profile, femLogin)
		const auth = new Auth(profile, login)
		const pmcBridge = new PmcBridge()
		const profileData = await pmcBridge.getProfile()

		profile.init(profileData)
		auth.init(profileData)
		login.init(profileData)
	}
}

export default AccountManager
