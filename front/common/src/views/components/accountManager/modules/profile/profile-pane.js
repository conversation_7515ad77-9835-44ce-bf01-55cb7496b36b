import './profile-pane.css'
import { sendTracking } from '../../utils'

const Pane = (function () {
	const selector = {
		profileName: '[data-profile-name]',
		profilePicture: '[data-profile-picture]',
		profilePane: '[data-profile-pane]',
		oldBookmarkLink: '[data-main-bookmark-link]',
		closeButton: '[data-profile-close]',
		connectedContent: '[data-profile-connected]',
		femLogin: 'fem-login'
	}

	const closeCallbacks = []
	let header = null

	return {
		init(profile, Header) {
			header = Header
			if (profile) {
				initConnected.call(this, profile)
			} else {
				initDisconnected()
			}
		},
		open() {
			document.documentElement.style.overflow = 'hidden'
			this.profilePane.classList.remove('isHidden')
			this.profilePane.classList.add('show')
			header.hideStickyHeader()
		},
		close() {
			document.documentElement.style.overflow = ''
			this.profilePane.classList.add('isHidden')
			this.profilePane.classList.remove('show')
			this.resetFemLoginAttributes()
			header.displayStickyHeader()
		},
		onClose(callback) {
			closeCallbacks.push(callback)
		},
		bindCloseButton() {
			this.closeButton.addEventListener('click', (callback) => {
				if (closeCallbacks.length) {
					closeCallbacks.forEach(callback)
				}
				this.close()
			})
		},
		bindServiceLink() {
			this.serviceLinks.forEach((link) => {
				link.addEventListener('click', this.handleServiceLinkClick.bind(this))
			})
		},

		showConnectedContent() {
			this.connectedContent.style.display = ''
		},
		handleServiceLinkClick(event) {
			event.preventDefault()
			const { target } = event
			sendTracking(target.dataset.trackingName)
			window.location.href = target.href
		},
		resetFemLoginAttributes() {
			this.femLogin.removeAttribute('redirect')
			this.femLogin.removeAttribute('signup-service')
		},
		render({ avatar, firstname, lastname }) {
			this.profilePictureEl.src =
				avatar ?? this.profilePictureEl.getAttribute('data-default-avatar')
			this.profileNameEl.innerText = this.getUserName(firstname, lastname)
		},
		getUserName(firstname, lastname) {
			if (firstname) return lastname ? `${firstname} ${lastname}` : firstname
			else return 'Mon profil'
		},
		get profilePane() {
			if (!this._profilePane) {
				this._profilePane = document.querySelector(selector.profilePane)
			}
			return this._profilePane
		},
		get connectedContent() {
			if (!this._connectedContent) {
				this._connectedContent = document.querySelector(selector.connectedContent)
			}
			return this._connectedContent
		},
		get profileNameEl() {
			if (!this._profileNameEl) {
				this._profileNameEl = document.querySelector(selector.profileName)
			}
			return this._profileNameEl
		},
		get profilePictureEl() {
			if (!this._profilePictureEl) {
				this._profilePictureEl = document.querySelector(selector.profilePicture)
			}
			return this._profilePictureEl
		},
		get closeButton() {
			if (!this._closeButton) {
				this._closeButton = document.querySelector(selector.closeButton)
			}
			return this._closeButton
		},
		get femLogin() {
			if (!this._femLogin) {
				this._femLogin = document.querySelector(selector.femLogin)
			}
			return this._femLogin
		},
		get serviceLinks() {
			if (!this._serviceLinks) {
				this._serviceLinks = [...this.profilePane.querySelectorAll('li a')].slice(0, 3)
			}
			return this._serviceLinks
		}
	}

	function initConnected(profile) {
		this.bindCloseButton()
		this.bindServiceLink()
		this.showConnectedContent()
		this.render(profile)
	}

	function initDisconnected() {}
})()

export default Pane
