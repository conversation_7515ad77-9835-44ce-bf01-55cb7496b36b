fem-login div {
	display: flex;
	height: 100%;

	svg {
		margin: auto;
		stroke: var(--color-main);
		width: 60px;
		height: 60px;
	}
}

[data-cta-profile] {
	position: relative;
}

[data-cta-profile-click-trap] {
	position: absolute;
	inset: 0;
	background: none;
	z-index: 1;
}

.profilePane {
	position: fixed;
	top: 0;
	left: 15vw;
	width: 85vw;
	height: 100vh;
	overflow-y: auto;
	background: var(--color-white);
	z-index: 2;
	transform: translateX(0);
	transition: transform 0.3s ease-in-out;

	@media (--media-min-tablet) {
		width: 480px;
		left: auto;
		right: 0;
	}

	&.isHidden {
		transform: translateX(100%);
	}

	&-closeButton {
		appearance: none;
		border: none;
		outline: none;
		background: none;
		position: absolute;
		top: 10px;
		left: 10px;
		padding: 0;

		svg {
			display: block;
			width: 14px;
			height: 14px;
		}
	}

	&-profileInfos {
		padding: 25px;
		background-color: var(--color-lightBackground);
		text-align: center;

		.pmc-profile {
			color: var(--color-text);
		}
	}

	&-profilePicture {
		display: block;
		margin: 0 auto var(--margin-sm) auto;
		width: 55px;
		height: 55px;
		border-radius: 50%;
		border: 1px solid var(--color-white);
		background-color: var(--color-white);
		box-shadow: 1px 2px 6px 0 var(--color-borderDarkest);
	}

	&-profileName {
		display: block;
		margin: 0 auto;
		text-align: center;
	}

	ul {
		list-style: none;
		padding: var(--margin-xs) var(--margin-mdp);
	}

	li {
		display: flex;
		align-items: center;
		height: 55px;
		border-bottom: 1px solid var(--color-lightBorder);

		svg {
			margin-right: var(--margin-sm);
			width: 20px;
			height: 24px;
		}

		a {
			display: flex;
			font-size: var(--fontSize-sm);
			color: var(--color-text);
		}
	}

	li a svg {
		margin: 0 0 0 var(--margin-xs);
		width: 50px;
		height: 20px;
	}

	li:last-child a {
		color: var(--color-linkAlt);
	}
}
