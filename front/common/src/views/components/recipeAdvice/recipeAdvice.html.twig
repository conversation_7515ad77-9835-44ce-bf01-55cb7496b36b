{% import '@CommonFront/macros/svg/views/svg.html.twig' as macroSvg %}
{% set adviceHtml = adviceHtml|default('') %}
{% set articleTitle = articleTitle|default('') %}

<div class="recipeAdvice" data-block="recipeAdvice">
  <div class="recipeAdvice-header">
    {% if illustration|default('') %}
      <img class="recipeAdvice-headerIllustration" src="{{ illustration }}" loading="lazy" width="48" height="48" alt="">
    {% endif %}
    <h2 class="recipeAdvice-headerTitle">
      Astuces et conseils pour {{ articleTitle }}
    </h2>
  </div>

  <div class="recipeAdvice-content">
    {{ adviceHtml|raw }}
  </div>

  <button type="button" class="recipeAdvice-button" data-showMoreButton>
    <span class="recipeAdvice-buttonLabelMore">Voir plus</span>
    <span class="recipeAdvice-buttonLabelLess">Voir moins</span>
    {{ macroSvg.getSvgTag('arrow-bottom') }}
  </button>
</div>
