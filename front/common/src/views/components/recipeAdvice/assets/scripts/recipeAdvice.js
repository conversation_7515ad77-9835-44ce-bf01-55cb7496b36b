import TrackElements from 'commonSrc/assets/scripts/utils/trackElements'

class RecipeAdvice {
	/**
	 * RecipeAdvice.
	 * Toggle content visibility for recipeAdvice component
	 * @constructor
	 * @param {{ element: HTMLDivElement | null; }} [param0={ element: null }]
	 * @param {*} param0.element
	 */
	constructor({ element } = { element: null }) {
		/** @type { HTMLDivElement | null } */
		this.recipeAdvice = element
		/** @type { HTMLDivElement | null | undefined } */
		this.content = this.recipeAdvice?.querySelector('.recipeAdvice-content')
		/** @type { HTMLButtonElement | null | undefined } */
		this.buttonShowMore = this.recipeAdvice?.querySelector('.recipeAdvice-button')
		/** @type { number } */
		this.contentRemainingPixel = this.content?.scrollHeight - this.content?.clientHeight
		/** @type { number } */
		this.THREESHOLD_PIXEL = 90
		/** @type { boolean } */
		this.isExpanded = false

		this.onToggleExpand = this.onToggleExpand.bind(this)
	}

	init() {
		if (!this.recipeAdvice) {
			console.error(`[RECIPE ADVICE] No recipeAdvice found`)
		}

		/*
			Content is cropped after a few lines, but it should display all content if the remaining portion after cropping is small.
			This prevents users from toggling the content just to see two more lines.
		*/
		if (this.contentRemainingPixel < this.THREESHOLD_PIXEL) {
			this.removeButtonShowMore()
		}

		this.addEvents()
	}

	removeButtonShowMore() {
		this.recipeAdvice?.classList.add('isExpanded')
		this.buttonShowMore?.remove()
	}

	addEvents() {
		this.buttonShowMore?.addEventListener('click', this.onToggleExpand)
	}

	onToggleExpand() {
		this.recipeAdvice.classList.toggle('isExpanded')

		TrackElements.sendEvent({
			event: 'button_click',
			button_name: this.isExpanded
				? 'astuces_et_conseils_voir_moins'
				: 'astuces_et_conseils_voir_plus'
		})

		this.isExpanded = !this.isExpanded
	}
}

export default RecipeAdvice
