.stickyBanner {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	max-width: 100vw;
	background-color: var(--color-white);
	box-shadow: -1px -2px 6px 0 rgba(157 170 177 / 50%);
	z-index: 200;
	transform: translateY(100%);
	transition: transform 0.5s ease-in-out;

	&.isShown {
		transform: translateY(0);
	}

	&-closeButton {
		appearance: none;
		padding: 0;
		border: 0;
		background: none;
		position: absolute;
		top: var(--margin-md);
		right: var(--margin-md);
		z-index: 1;
		width: 14px;
		height: 14px;
		cursor: pointer;

		svg {
			width: 14px;
			height: 14px;
		}
	}

	@media (--media-min-tablet) {
		display: none;
	}

	&.button {
		margin: 0 auto 15px;
	}
}
