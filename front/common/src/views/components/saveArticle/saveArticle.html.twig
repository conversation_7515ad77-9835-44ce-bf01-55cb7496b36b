{% import '@CommonFront/macros/svg/views/svg.html.twig' as macroSvg %}
{% set bookmark_attributes = get_bookmark_attributes(article, 'STICKY-ARTICLE') %}
{% set signupService = signupService|default('') %}
{% set notRecipe = article and article.getType() != 'Recipe' %}
{% set textBookmark = textBookmark|default('Sauvegarder') %}
{% set textBookmarked = textBookmarked|default('Sauvegardé') %}

<div class="saveBookmark">
    <button
        type="button"
        class="saveBookmark-button"
        aria-live="polite"
        data-login-required
        {% if notRecipe and article.getParentCategory(1).title ?? false %}data-board-name="{{ article.getParentCategory(1).title }}"{% endif %}
        {{ bookmark_attributes|join(' ')|raw }} data-manual-auth data-tracking="User//Like//Bottom" data-signupservice="{{ signupService }}">
        {{ macroSvg.getSvgTag('bookmark-page') }}
        {{ macroSvg.getSvgTag('tail-spin') }}
        <span class="saveBookmark-bookmark">{{ textBookmark }}</span>
        <span class="saveBookmark-bookmarked">{{ textBookmarked }}</span>
    </button>
</div>
