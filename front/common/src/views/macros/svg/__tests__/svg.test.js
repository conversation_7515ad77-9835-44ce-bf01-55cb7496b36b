import Svg from '../assets/scripts/svg.tsx'

describe('Svg module', () => {
	it('should return svg with JSX and default attributes', () => {
		const result = <Svg name="check" />

		const sampleResult = (
			<svg className="icon icon-check" aria-hidden="true" focusable="false">
				<use href="#check"></use>
			</svg>
		)
		expect(result).toStrictEqual(sampleResult)
	})

	it('should return svg with JSX with and custom srOnlyText', () => {
		const result = <Svg name="check" srOnlyText="Fermer" />

		const sampleResult = (
			<>
				<span className="sr-only">Fermer</span>
				<svg className="icon icon-check" aria-hidden="true" focusable="false">
					<use href="#check"></use>
				</svg>
			</>
		)
		expect(result).toStrictEqual(sampleResult)
	})

	it('should return svg with JSX with and custom attributes', () => {
		const result = (
			<Svg
				name="check"
				attributes={{
					'aria-label': 'Fermer'
				}}
			/>
		)

		const sampleResult = (
			<svg
				className="icon icon-check"
				aria-hidden="true"
				focusable="false"
				aria-label="Fermer"
			>
				<use href="#check"></use>
			</svg>
		)
		expect(result).toStrictEqual(sampleResult)
	})
})
