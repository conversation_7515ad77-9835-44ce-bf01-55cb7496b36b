<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  {% for content in contents %}
    <url>
      <loc>{{ app.request.schemeAndHttpHost ~ content.path }}</loc>
        {% if content.hasPhotos %}
            <image:image><image:loc>{{ pmd_common_content_image_url(content, 16 / 9, 1200) }}</image:loc></image:image>
        {% endif %}
    </url>
  {% endfor %}
</urlset>
