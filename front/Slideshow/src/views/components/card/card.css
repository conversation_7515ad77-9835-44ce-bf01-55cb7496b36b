.card {
	position: relative;
	border: 1px solid var(--color-lightest);
	border-radius: var(--radius);

	&,
	header {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	picture {
		position: relative;
		background-color: var(--color-black);
		text-align: center;
		border-radius: var(--radius);
	}

	&-shareOverlay {
		opacity: 0;
		transition: opacity 0.3s ease-in-out;
		position: absolute;
		inset: 0;
		background-color: rgb(0 0 0 / 60%);
		display: flex;

		> * {
			margin: auto;
		}
	}

	&:hover &-shareOverlay {
		opacity: 0;
	}

	img {
		display: block;
		margin: auto;
		max-width: 100%;
		width: auto;
		height: 100%;
		border-radius: var(--radius);
		max-height: fill-available; /* Safari */
	}

	&-content {
		display: flex;
		flex-direction: column;
		padding: var(--space-md);
		flex-grow: 1;
		height: fit-content; /* Safari */
	}

	&-title {
		font-family: var(--font-heading);
		line-height: 27px;
	}

	&-title > a {
		font-size: var(--text-lg);
		font-weight: bold;

		&,
		&:visited,
		&:hover {
			color: var(--color-darkest);
			text-decoration: none;
		}
	}

	&.hasWideLink &-title > a::after {
		content: '';
		display: block;
		position: absolute;
		inset: 0;
	}

	&-description,
	&-action {
		margin: var(--space-md) 0;
	}

	&-description {
		ul {
			margin-top: var(--space-md);
			padding-left: var(--space-xlg);
		}

		li + li {
			margin-top: var(--space-xsm);
		}

		p {
			line-height: 1.5;
		}

		p + p {
			margin-top: var(--space-md);
		}

		a,
		a:visited,
		a:active {
			color: var(--color-primary);
			text-decoration: none;
		}
	}

	&-action {
		order: 1;
		text-align: center;
	}

	footer {
		margin: var(--space-sm) 0 0 0;
		font-size: var(--text-md);
		color: var(--color-light);
		order: 1;

		p {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			align-items: flex-end;
			height: 100%;
		}

		p.hasOne span {
			margin-left: auto;
		}
	}
}

@media screen and (min-width: 769px) {
	.card {
		&:hover &-shareOverlay {
			opacity: 1;
		}

		&-shareOverlay {
			.sharebar-item {
				margin: 10px;
			}
		}

		&-content {
			display: grid;
			grid-template-columns: 1fr auto;
			grid-column-gap: var(--space-md);
		}

		header {
			margin: 0;
			grid-column: 1 / 2;
		}

		&-description {
			margin: 0 0 var(--space-md) 0;
			grid-column: 1 / 3;
		}

		&-action {
			margin: 0 0 var(--space-md);
			grid-row: 1 / 2;
			grid-column: 2 / 3;
			margin-left: auto;
		}

		footer {
			margin-top: var(--space-lg);
			grid-column: 1 / 3;
		}

		&-title {
			margin-bottom: var(--space-md);
		}

		&-title > a {
			font-size: var(--text-xlg);
		}
	}
}
