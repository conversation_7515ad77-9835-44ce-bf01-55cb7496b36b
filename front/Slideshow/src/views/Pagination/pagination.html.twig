{% if pagination.first is not same as (pagination.last) %}
    {% for k, level in pagination.levels %}
        {% if level is not empty %}
        <ul class="pagination">
            {% if loop.index is same as (1) %}
                {% if pagination.previous is defined %}
                    {% if pagination.current < 10 %}
                        {% set first = '&lt;&lt;' %}
                    {% else %}
                        {% set first = '...' %}
                    {% endif %}
                    <li><a href="{{ url ~ pagination.first }}">{{ first|raw }}</a></li>
                    <li><a href="{{ url ~ pagination.previous }}">&lt;</a></li>
                {% endif %}
            {% endif %}
            {% for page in level %}
                {% set current = loop.index == 1 ? 'current' : '' %}
                <li>
                    <a href="{{ url ~ page }}" class="{{ current }}">{{ page }}</a>
                </li>
            {% endfor %}
            {% if loop.index is same as (1) %}
                {% if pagination.next is defined %}
                    {% if pagination.current < 10 %}
                        {% set last = '&gt;&gt;' %}
                    {% else %}
                        {% set last = '...' %}
                    {% endif %}
                    <li class="item"><a href="{{ url ~ pagination.next }}">&gt;</a></li>
                    <li class="item"><a href="{{ url ~ pagination.last }}">{{ last|raw }}</a></li>
                {% endif %}
            {% endif %}
        </ul>
        {% endif %}
    {% endfor %}
{% endif %}
