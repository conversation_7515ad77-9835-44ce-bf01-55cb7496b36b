{% if coreadsDisabled is not defined %}
    {% set coreadsDisabled = app.request.query.has('puboff')|default(false) %}
{% endif %}
{% set cmpDisabled = app.request.query.has('cmpoff')|default(false) %}
{% set gtmDisabled = app.request.query.has('gtmoff')|default(false) %}

{# scripts to load #}
{% set pmcUrlModern = scds_url('pmc-starter/esm.index.js') %}
{% set pmcUrlLegacy = scds_url('pmc-starter/index.js') %}

{% set isSlideshow = app.request.get('_route') == 'diaporama_slideshow' %}
{% set brandKey = appConfig.brand.brandKey|default('') %}
{% set isSeeAndSoWebsite = (brandKey is same as('UKSAS') or brandKey is same as('DESAS')) %}
{% set isGala = brandKey is same as('GAL') %}
{% set noPmc = (isSeeAndSoWebsite or isGala) %}

<!doctype html>
<html lang="fr">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {% block title %}<title>{{ appConfig.brand.name }}</title>{% endblock title %}
        {% if isSlideshow %}
            <meta name="prismadmin" content='{{ get_meta_prism_admin(content)|json_encode|raw|replace({"'": "\'"}) }}' />
        {% endif %}
        {% block headMeta %}
            {% if appConfig.brand.googleWebmasterToolsCode is not empty %}
                <meta name="google-site-verification" content="{{ appConfig.brand.googleWebmasterToolsCode }}" />
            {% endif %}
            <meta name="robots" content="max-snippet:-1, max-image-preview:standard, max-video-preview:-1" />
            <link rel="canonical" href="{{ app.request.schemeAndHttpHost ~ app.request.pathinfo }}" />
        {% endblock headMeta %}

        {# Preload images #}
        {% block imagesPreload %}{% endblock %}

        {% block stylesheets %}
            <link rel="shortcut icon" href="{{ appConfig.brand.faviconPath|default('common.ico') ~ assetVersionSuffix }}" type="image/x-icon" />
        {% endblock stylesheets %}

        {% include '@Capsule/consent-check/pmd-consent.html' %}

        {% block jsHeadTop %}
            {# PMC queue #}
            {% if not noPmc %}
                <script>
                    window.pmcstarter = window.pmcstarter || function () {
                        (window.pmcstarter.q = window.pmcstarter.q || []).push(arguments)
                    }
                </script>
            {% endif %}
            {% if isGala %}
            <script>
                const connectUrl = window.location.href.includes('https://rec2', 'https://local')
                    ? 'https://rec2-api-connect.gala.fr/payment/stripe/session?'
                    : window.location.href.includes('https://rec3')
                        ? 'https://rec3-api-connect.gala.fr/payment/stripe/session?'
                        : 'https://api-connect.gala.fr/payment/stripe/session?'
                const redirectTo = 'redirectTo=' + encodeURIComponent(window.location.href)
                // PMCSTARTER MOCK EDITION
                window.pmcstarter = function(instruction, callback = () => {}) {
                    switch (instruction) {
                        case 'getPremiumOrderLink':
                            callback(connectUrl + redirectTo)
                            break
                        case 'getProfile':
                            callback(false)
                            break
                        case 'isConnected':
                            callback(false)
                            break
                        default:
                            callback(false)
                    }
                }
            </script>
            {% endif %}

            {% include '@Front/src/views/Fragments/scriptConfig.html.twig' %}

            {# Cmp sourcepoint #}
            {% if not cmpDisabled %}
                {% if abvariant == 'production' %}
                    <script type="text/javascript">window.dataLayer.push({"ab_test_variant_pub": "production"});</script>
                    <script type="module" crossorigin src="{{ scds_url('sourcepoint/sourcepoint.esm.min.js') }}"></script>
                    <script defer nomodule crossorigin src="{{ scds_url('sourcepoint/sourcepoint.min.js') }}"></script>
                {% else %}
                    <script type="text/javascript">window.dataLayer.push({"ab_test_variant_pub": "sourcepoint.load.inline"});</script>
                    <script type="module" crossorigin>
                        {{ scds_content('sourcepoint/sourcepoint.esm.min.js') }}
                    </script>
                    <script nomodule crossorigin type="text/javascript">
                        {{ scds_content('sourcepoint/sourcepoint.min.js') }}
                    </script>
                {% endif %}
            {% endif %}

            {# PMC Starter #}
            {% if not noPmc %}
                <script defer crossorigin type="module" src="{{ pmcUrlModern }}"></script>
                <script defer crossorigin nomodule src="{{ pmcUrlLegacy }}"></script>
            {% endif %}

            {{ coreads_generate_page_hit_id(brandKey) }}

            {# GA4 #}
            {% block blockGA4 %}{% endblock blockGA4 %}

            {# coreads #}
            {# TODO TO CLEAN - Condition SPECIFIC GALA #}
            {% if isGala %}
                {% include '@Front/src/views/Fragments/coreads.html.twig' %}
            {% else %}
                {% include '@CommonFront/components/coreads/coreads.html.twig' %}
            {% endif %}

            {% include '@CommonFront/components/coreads/coreadsQueue.html.twig' %}

            {# gtm #}
            {% include '@Front/src/views/Fragments/googleTagManager.html.twig' %}
            {# Google Analytics #}
            {% include '@Front/src/views/Fragments/googleAnalytics.html.twig' %}
            {# OptiDigital #}
            {% include '@CommonFront/components/optidigital/optidigital.html.twig' %}
            {# parsely #}
            {% if not isGala %}
                {% include '@Front/src/views/Fragments/parsely.html.twig' %}
            {% endif %}

        {% endblock jsHeadTop %}

        {% block jsHeadBottom %}{% endblock jsHeadBottom %}

    </head>

    <body
			id="corps"
            data-brand="{{ brandKey }}"
			data-myaf="div[data-pmdfp-format='pave_haut']"
			data-pmc-got-event="gotEvent"
			data-loader='{
				"site": {},
				"external": {
						"players": []
				},
				"other": [],
				"polyfills": [
						"IntersectionObserver",
						"fetch",
						"Element.prototype.dataset",
						"Array.prototype.forEach"
				]
		}'
		>

      <div class="site_content">
          {% include '@Front/src/views/components/sprite.svg.html.twig' %}
          <div class="coreads_banniere-haute ad-placeholder">
              {% include '@Front/src/views/components/use-icon.svg.html.twig' with {
              iconId: 'logo-pm',
              attributes: [
                  "width=100px"
              ]
              } only %}
              {{ coreads_tag('Banniere-Haute', {'device': ['tablet', 'desktop']}) }}
          </div>
          <div class="container">
            <main id="app" class="main">
                {% block header %}{% endblock header %}
                <section>
                    {% block content %}{% endblock content %}
                </section>
            </main>
            <aside class="aside">
              {% block aside %}{% endblock aside %}
            </aside>
          </div>
      </div>
			<div class="coreads_out-of-banner">{{ coreads_tag('Out-Of-Banner') }}</div>
            {% if app.request.get('_route') != 'diaporama_website' %}
                <div class="ads-core-placer" id="Footer-web_6487294d32e16" data-ads-core='{"type":"Footer-web"}'></div>
            {% endif %}
      {% include '@Front/src/views/Footer/footer.html.twig' %}

        <div class="coreads_habillage">{{ coreads_tag('Postitiel') }}</div>
      {% block jsBodyBottom %}
				{% include '@Front/src/views/Fragments/onadloaded.html.twig' %}
      {% endblock jsBodyBottom %}
      <!-- abtest_group_from_header : {{ abvariant }} -->
    </body>
</html>
