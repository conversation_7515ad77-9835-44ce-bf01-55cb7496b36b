{% extends '@Front/src/views/Layouts/front.html.twig' %}

{% set photo = content.slideshow.photo|default(null) %}
{% set slideshowsCount = content.slideshowsDocuments|length %}

{% block title %}
    <title>{{ content.title }} - {{ appConfig.brand.name }}{% if 1 < app.request.get('page', 1) %} - page {{ app.request.get('page', 1) }}{% endif %}</title>
{% endblock title %}

{% block imagesPreload %}
    {% if photo %}
        {% set imagePreloadMobile = photo.url|pmd_image_scale_url(450) %}
        {% set imagePreloadDesktop = photo.url|pmd_image_scale_url(600) %}
        <link fetchpriority="high" rel="preload" as="image" href="{{ imagePreloadMobile }}" media="screen and (max-width: 480px)">
        <link fetchpriority="high" rel="preload" as="image" href="{{ imagePreloadDesktop }}" media="screen and (min-width: 481px)">
    {% endif %}
{% endblock %}

{% block headMeta %}
    {{ parent() }}
    {% if content %}
        {% include '@CommonFront/components/parsely/data.html.twig' with {article: content, type: 'post'} only %}
    {% endif %}

    {% if appConfig.brand.facebook.appId is defined %}
        <meta property="fb:app_id" content="{{ appConfig.brand.facebook.appId }}" />
    {% endif %}
    <meta property="og:title" content="{{ content.title }}" />
    <meta property="og:type" content="article" />
    <meta property="og:site_name" content="{{ appConfig.brand.name }}" />

    <meta property="og:url" content="{{ content.path }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="{{ content.title }}" />

    {% set description = "Découvrez ce diaporama et partagez-le à vos amis" %}
    {% if photo %}
        {% if content.description is not null %} {# prevent empty descriptions #}
            {% set description = content.description|striptags %}
        {% endif %}
        <meta name="description" content="{{ description }}" />

        <meta property="og:image" content="{{ photo.url|pmd_image_scale_url(1200) }}" />
        <meta property="og:description" content="{{ description }}" />

        <meta name="twitter:image" content="{{ photo.url|pmd_image_scale_url(1200) }}" />
        <meta name="twitter:description" content="{{ description }}" />
    {% else %}
        <meta property="og:description" content="{{ description }}" />
        <meta name="twitter:description" content="{{ description }}" />
    {% endif %}
{% endblock headMeta %}

{% block stylesheets %}
{{ parent() }}
  {% set brand = appConfig.brand.brandKey|lower %}
  <link rel="stylesheet" type="text/css" href="{{ asset('/assets/slideshow/css/pages/slideshow/' ~ brand ~ '.css') ~ assetVersionSuffix }}">
{% endblock %}

{# GA4 #}
{% block blockGA4 %}
    <script defer src="{{ asset('/assets/slideshow/js/dist/slideshow.js') ~ assetVersionSuffix }}"></script>
{% endblock blockGA4 %}

{% block header %}
    {% if slideshowsDocuments %}
        {% include '@Front/src/views/pages/slideshow/components/sticky-header/sticky-header.html.twig' with {
            classes: ['diaporama-stickyHeader'],
            title: content.title,
            photo: slideshowsDocuments[0].photo,
            diapoLen: slideshowsCount
        } only %}
    {% endif %}
    {% include '@Front/src/views/components/header/header.html.twig' with {
          classes: ['diaporama-header'],
          title: content.title,
          homeButton: true
    } only %}
{% endblock header %}

{% block content %}
    <div class="ad-placeholder banniereHauteMobile onlyMobile">
        {% include '@Front/src/views/components/use-icon.svg.html.twig' with {
        iconId: 'logo-pm',
        attributes: [
            "width=100px"
        ]
        } only %}
        {{ coreads_tag('Banniere-Haute', {'device': ['mobile']}) }}
    </div>
    <div class="wrapper" data-photoflow-wrapper>
        {% for slideshows in slideshowsDocuments|batch(appConfig.brand.nbSlidesPerSection) %}
            {% include '@Front/src/views/pages/slideshow/Fragments/photoFlowSection.html.twig' with {
                id: article.publicId,
                slideshowsCount,
                slideshows,
                sectionNumber: loop.index
            } %}
        {% endfor %}
    </div>

    {% include '@Front/src/views/pages/slideshow/components/see-also/see-also.html.twig' with {
        sectionId: 'see-also' ~ content.id,
        recommendedDocuments: content.recommendedDocuments
    } %}
{% endblock content %}

{% block jsHeadBottom %}
    {{ parent() }}
    <script type="application/ld+json">
    {% if photo %}
        {% set headline = content.slideshow.title ~ ' - ' ~ content.title ~ ' - ' ~ appConfig.brand.name %}
        {% set thumbnail_url = photo.url|pmd_image_scale_url(1200) %}
    {% else %}
        {% set headline = content.title ~ ' - ' ~ appConfig.brand.name %}
        {% set thumbnail_url = '' %}
    {% endif %}
    {{
    {
        "@context": "http:\/\/schema.org",
        "@type": "ImageGallery",
        "name": headline,
        "mainEntityOfPage": app.request.schemeAndHttpHost ~ content.path,
        "Image": slideshowsDocuments|map((slideshow) => slideshow.photo.url)
    }|json_encode()|raw()
    }}
    </script>
{% endblock %}
