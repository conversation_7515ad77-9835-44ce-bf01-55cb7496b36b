class ShareButtons {
	#parent: HTMLElement

	public init(root: HTMLElement): void {
		this.#parent = root
		this.handleShareButtonClick = this.handleShareButtonClick.bind(this)
		this.#parent.addEventListener('click', this.handleShareButtonClick)
	}

	private handleShareButtonClick(event: Event): void {
		const link: HTMLAnchorElement = event.target
		if (!link.matches('a.sharebar-button')) {
			return
		}
		event.preventDefault()
		window.open(link.href, '', 'width=600, height=600')
	}
}

export default ShareButtons
