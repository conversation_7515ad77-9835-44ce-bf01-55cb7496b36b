{% set shareBarClasses = []|default(['sharebar-overlay'])|merge(type is defined ? [type] : [null])|join(' ') %}

<div class="{{ shareBarClasses }}">
	<div class="sharebar-listContainer">
		<h3 class="sharebar-title">
			{% include '@Front/src/views/components/use-icon.svg.html.twig' with {
          iconId: "share",
          attributes: ["width=20", "height=20"]
        } %}
			{{ 'SHARE'|trans({}, 'slideshow') }}
		</h3>
		<button class="sharebar-close">
			{% include '@Front/src/views/components/use-icon.svg.html.twig' with {
          iconId: "close",
          attributes: ["width=17", "height=17"]
        } %}
		</button>
		<ul class="sharebar-list">
			<li class="sharebar-item">
				<a href="{{ 'https://www.facebook.com/sharer/sharer.php?u=' ~ url }}" class="sharebar-button facebook" title="Partager sur Facebook">
					{% include '@Front/src/views/components/use-icon.svg.html.twig' with {
              iconId: "facebook",
              attributes: ["width=9", "height=17"]
            } %}
					<span>{{ 'SHARE_ON'|trans({}, 'slideshow') }} Facebook</span>
				</a>
			</li>
			<li class="sharebar-item">
				<a href="{{ 'https://twitter.com/intent/tweet?url=' ~ url ~ '&text=' ~ title|url_encode }}" class="sharebar-button twitter" title="Partager sur X">
					{% include '@Front/src/views/components/use-icon.svg.html.twig' with {
              iconId: "x",
              attributes: ["width=20", "height=16"]
            } %}
					<span>{{ 'SHARE_ON'|trans({}, 'slideshow') }} X</span>
				</a>
			</li>
			<li class="sharebar-item">
				{% set mediaParam = photo.url|default('') ? '&media=' ~ photo.url|pmd_image_fit_url(500, 500) : '' %}
				<a href="{{ 'https://www.pinterest.fr/pin/create/button/?url=' ~ app.request.pathInfo ~ mediaParam ~ '&description=' ~ title|url_encode }}" class="sharebar-button pinterest" title="Partager sur Pinterest">
					{% include '@Front/src/views/components/use-icon.svg.html.twig' with {
              iconId: "pinterest",
              attributes: ["width=17", "height=17"]
            } %}
					<span>{{ 'SHARE_ON'|trans({}, 'slideshow') }} Pinterest</span>
				</a>
			</li>
		</ul>
	</div>
</div>
