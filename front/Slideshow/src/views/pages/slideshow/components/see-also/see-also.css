.seeAlso {
	&-title {
		margin-bottom: var(--space-md);
		padding: var(--space-md) 0;
		border-bottom: 1px solid var(--color-lightest);
		font-size: var(--text-lg);
		font-weight: bold;
	}

	ul {
		display: grid;
		grid-template-columns: 1fr;
		grid-gap: var(--space-md);
	}

	li {
		list-style: none;
	}
}

@media screen and (min-width: 769px) {
	.seeAlso {
		display: grid;
		grid-template-columns: 1fr 300px;
		grid-gap: var(--space-xlg);

		&-title {
			margin-bottom: 0;
			padding: var(--space-lg) 0;
			border-bottom: 1px solid var(--color-lightest);
			font-size: var(--text-xlg);
			font-weight: bold;
		}

		&-title,
		ul,
		.outbrain-lazy-block {
			grid-column: 1 / 2;
		}

		ul {
			grid-template-columns: 1fr 1fr;
			grid-template-rows: max-content;
			grid-gap: var(--space-xlg);
		}

		.card {
			margin-bottom: 0;
			height: 100%;
		}

		&-ad {
			grid-row: 2 / 4;
		}

		.aside.seeAlso-ad > .ad-placeholder {
			top: var(--space-lg);
		}
	}
}
