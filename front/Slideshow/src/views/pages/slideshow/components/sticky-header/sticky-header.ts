import { EventEmitter } from '@prismamedia/fem-utils'
import ShareBar from '../sharebar/sharebar'

export default class StickyHeader {
	root: HTMLElement
	inFlowHeader: HTMLElement
	contentContainer: HTMLElement
	stickyHeaderHeight: number
	inFlowHeaderEnd: number
	maxProgressValue: number
	startPosition: number
	lastDiapoNum: number = 1
	#_scrollTopBtn: HTMLButtonElement = null
	#_diapoNum: HTMLSpanElement = null
	#_progressBar: HTMLProgressElement = null

	#shareBar: ShareBar

	constructor() {
		this.handleScroll = this.handleScroll.bind(this)
		this.handleResize = this.handleResize.bind(this)

		this.#shareBar = new ShareBar()

		EventEmitter.decorate(this)
	}

	public init(initArgs: { inFlowHeader: HTMLElement; contentContainer: HTMLElement }): void {
		if (!initArgs.contentContainer || !initArgs.inFlowHeader) {
			throw new Error('[sticky-header] - missing constructor arguments')
		}

		this.root = document.getElementById('sticky-header')
		this.contentContainer = initArgs.contentContainer
		this.inFlowHeader = initArgs.inFlowHeader
		this.#_scrollTopBtn = this.root.querySelector('.stickyHeader-scrollTop')

		this.#shareBar.init()

		this.maxProgressValue = parseInt(this.progressBar.getAttribute('max'), 10)

		this.setInitialValues()
		this.updateHeader()

		window.addEventListener('scroll', this.handleScroll)
		window.addEventListener('resize', this.handleResize)
		this.#_scrollTopBtn.addEventListener('click', () => window.scrollTo(0, 0))
	}

	private handleScroll(): void {
		this.updateHeader()
	}

	private handleResize(): void {
		this.setInitialValues()
		this.updateHeader()
	}

	private show(): void {
		this.root.classList.remove('isHidden')
	}

	private hide(): void {
		this.root.classList.add('isHidden')
	}

	private setInitialValues(): void {
		this.stickyHeaderHeight = this.root.offsetHeight
		this.startPosition = this.contentContainer.offsetTop
		this.inFlowHeaderEnd = this.inFlowHeader.offsetTop + this.inFlowHeader.offsetHeight

		if (window.innerWidth >= 768) {
			this.handleDiapoNumUpdate = this._handleDiapoNumUpdate
		}
	}

	private handleDiapoNumUpdate(): void {}

	private _handleDiapoNumUpdate(diapoNum: number): void {
		this.diapoNum.innerText = diapoNum.toString()
	}

	private updateDiapoNum(scrollPosition: number): void {
		const diapoNum: number = Math.ceil(this.calculateProgression(scrollPosition))
		let effectiveDiapoNum: number

		if (diapoNum !== this.lastDiapoNum) {
			effectiveDiapoNum = Math.min(this.maxProgressValue, diapoNum)
			this.emit('diapo-num:update', {
				index: effectiveDiapoNum,
				max: this.maxProgressValue
			})
			this.handleDiapoNumUpdate(effectiveDiapoNum)
		}

		this.lastDiapoNum = diapoNum
	}

	private updateHeader(): void {
		const scrollPosition: number = window.pageYOffset
		if (
			scrollPosition > this.inFlowHeaderEnd - this.stickyHeaderHeight &&
			Math.ceil(this.calculateProgression(scrollPosition)) < this.maxProgressValue
		) {
			this.show()
		} else {
			this.hide()
		}

		this.updateProgressBar(scrollPosition)
		this.updateDiapoNum(scrollPosition)
	}

	private updateProgressBar(scrollPosition: number): void {
		if (scrollPosition <= this.startPosition) {
			this.progressBar.value = 0
		} else {
			this.progressBar.value = this.calculateProgression(scrollPosition)
		}
	}

	private calculateProgression(scrollPosition: number): number {
		return (
			((scrollPosition - this.startPosition) * this.maxProgressValue) /
			this.contentContainer.offsetHeight
		)
	}

	private get progressBar(): HTMLProgressElement {
		if (!this.#_progressBar) {
			this.#_progressBar = this.root.querySelector('progress')
		}

		return this.#_progressBar
	}

	private get diapoNum(): HTMLSpanElement {
		if (!this.#_diapoNum) {
			this.#_diapoNum = this.root.querySelector('.stickyHeader-diapoNum')
		}

		return this.#_diapoNum
	}
}
