@import url('../sharebar/sharebar');

.stickyHeader {
	display: flex;
	flex-flow: column wrap;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	margin: 0 var(--neg-space-xsm);
	height: 80px;
	background-color: #fff;
	transition: transform 0.3s ease-in-out;
	border-bottom: 2px solid var(--color-lightest);
	box-shadow: 0 5px 12px 0 rgb(150 150 150 / 10%);

	&.isHidden {
		transform: translate3d(0, -100%, 0);
	}

	progress {
		display: block;
		border: 0;
		width: 100%;
	}

	progress[value] {
		appearance: none;
		height: 10px;
	}

	progress[value]::-webkit-progress-bar {
		background-color: var(--color-lightest);
	}

	progress[value]::-moz-progress-bar {
		background-color: var(--color-primary);
	}

	progress[value]::-webkit-progress-value {
		background-color: var(--color-primary);
	}

	&-logoWrapper {
		align-items: center;
		flex-grow: 1;
		margin: 0 auto;
		padding: 0 var(--space-md);
		border: 0;
		width: 100%;
		max-width: var(--site-max-width);

		> div {
			display: flex;
			flex-shrink: 0;
			align-items: center;
		}
	}

	&-logo {
		height: var(--logo-size-sticky-mobile);
		width: 100%;
	}

	&-title {
		display: none;
		font-size: var(--text-lg);
		font-weight: normal;
		overflow: hidden;
		text-overflow: ellipsis;
		max-height: 30px;
		white-space: nowrap;
	}

	&-diapoNumWrapper {
		display: none;
	}

	&-scrollTop,
	&-shareBar {
		margin-left: var(--space-xlg);
	}

	&-scrollTop {
		padding: var(--space-md);
		width: auto;
		color: #fff;
		background: var(--color-black);
		cursor: pointer;
	}

	&-allButton {
		margin-left: var(--space-lg);
		flex-shrink: 0;

		> span {
			display: none;
		}
	}
}

@media screen and (min-width: 540px) {
	.stickyHeader {
		&-diapoNumWrapper {
			display: block;
			flex-shrink: 0;
			font-size: var(--text-lg);
			color: var(--color-light);
			margin-left: var(--space-lg);
		}

		&-diapoNum {
			font-weight: bold;
			color: var(--color-primary);
		}
	}
}

@media screen and (min-width: 769px) {
	.stickyHeader {
		&-logoWrapper {
			padding: 0;
		}

		&-logo {
			margin-right: var(--space-xlg);
			height: var(--logo-size-sticky);
			padding: 0 15px;
		}

		&-title {
			display: block;
			margin-right: auto;
		}
	}
}

@media screen and (min-width: 1024px) {
	.stickyHeader {
		&-allButton > span {
			display: inline-block;
		}
	}
}
