{% set occurrence = 4 %}
{% set brandKey = appConfig.brand.brandKey|default('')|lower %}

<div class="diaporama" data-photoflow-list>
    {% for slideshow in slideshows %}
        {% set photoKey = (sectionNumber - 1) * appConfig.brand.nbSlidesPerSection + loop.index %}

        {% if slideshowsCount >= occurrence * 1.2 and photoKey != 1 and photoKey % occurrence == 1 %}
            <div class="ad-placeholder pave-haut2-desktop">
                {% include '@Front/src/views/components/use-icon.svg.html.twig' with {
                    iconId: 'logo-pm',
                    attributes: ["width=100px"]
                } only %}
                {% if brandKey != 'hbz' %}
                    {% include '@Front/src/views/components/lazy-ad/lazy-ad.html.twig' with {
                        id: "now"|date("ms") ~ "-" ~ sectionNumber ~ '-desktop',
                        type: "Pave-Haut2-Desktop"
                    } only %}
                {% endif %}
            </div>
        {% endif %}
        {% embed '@Front/src/views/components/card/card.html.twig' with {
            diapo: true,
            photoKey,
            id: id ~ '-' ~ photoKey,
            url: url|default('#'),
            adsInImage: photoKey == 2,
            title: slideshow.title|default(''),
            slideshow,
            photoLen: slideshowsCount,
            attributes: [
                'data-photoflow-item',
                'data-index=' ~ photoKey,
                'id=' ~ slideshow.title|slugify ~ '-' ~ slideshow.id
            ],
            sectionNumber
        } %}
            {% block body %}
                {% if slideshow.button.enabled|default(false) == true %}
                <div class="card-action">
                    <a href="{{ slideshow.button.url matches "~^(?:f|ht)tps?://~i" ? '' : 'http://' }}{{ slideshow.button.url }}" class="card-cta button button-primary" target="{{ slideshow.button.title }}" title="{{ slideshow.button.title }}" rel="nofollow">{{ slideshow.button.title }}</a>
                </div>
                {% endif %}
                <div class="card-description">{{ draftToHtml(slideshow.body.draft).html|default('')|raw }}</div>
            {% endblock %}
            {% block footer %}
                <p>
                    <span class="card-index">
                        {{ "PHOTO"|trans({}, 'slideshow') }} {{ photoKey }}/{{ slideshowsCount }}
                    </span>
                    {% if slideshow.photo.credit is not empty %}
                    <span class="card-photoCredits">
                        <span>&copy; {{ slideshow.photo.credit }} </span>
                    </span>
                </p>
                {% endif %}
            {% endblock %}
        {% endembed %}
    {% endfor %}
</div>
