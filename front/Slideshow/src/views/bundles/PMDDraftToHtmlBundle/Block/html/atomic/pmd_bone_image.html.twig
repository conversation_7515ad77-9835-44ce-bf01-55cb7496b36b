{% apply spaceless %}
  {% if content.href is defined and content.href is not empty %}
    <a href="{{ content.href }}"{% if content.rel %} rel="{{ content.rel }}"{% endif %}{% if content.target %} target="{{ content.target }}"{% endif %}>
  {% endif %}
  <figure data-type="{{ content.type }}" data-key="{{ content.key }}" data-site="{{ content.site }}" class="ratio-3-2">
    <img
      src="{{ pmd_common_image_url_string(content.src, 650, {title: content.caption}) }}"
      srcset="
            {{ pmd_common_image_url_string(content.src, 422, {title: content.caption}) }} 768w,
            {{ pmd_common_image_url_string(content.src, 650, {title: content.caption}) }}"
      alt="{{ content.caption != '' ? content.caption|escape : '' }}"
      data-id=""
      loading="lazy"
      width="650"
      height="375"
    />

    {% if content.caption is defined and content.caption and content.credit is defined and content.credit %}
      <figcaption class="photoCaption" data-block="photoCaption">
        <cite>
            {{ content.caption ~ '© ' ~ content.credit }}
        </cite>
      </figcaption>
    {% elseif content.caption is defined and content.caption %}
      <figcaption class="photoCaption" data-block="photoCaption">
        <cite>
          {{ content.caption }}
        </cite>
      </figcaption>
    {% elseif content.credit is defined and content.credit %}
      <figcaption class="photoCaption" data-block="photoCaption">
        {{ '© ' ~ content.credit }}
      </figcaption>
    {% endif %}
  </figure>
  {% if content.href is defined and content.href is not empty %}
    </a>
  {% endif %}
{% endapply %}
