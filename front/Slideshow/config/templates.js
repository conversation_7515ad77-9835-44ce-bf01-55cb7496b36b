const variableTemplate = ({
	fontBase,
	fontHeading,
	fontHeadingWeight,
	color,
	logoSize,
	button = {},
	buttonDesktop = {},
	radius = '0'
}) => `
:root {
  /* globals */
  --site-max-width: 1140px;
  --site-habillage-size: 1000px;

  /* logo size */
  --logo-size-sticky: ${logoSize.sticky || logoSize.desktop};
  --logo-size-mobile: ${logoSize.mobile};
  --logo-size-sticky-mobile: ${logoSize.stickyMobile || logoSize.mobile};
  --logo-size-desktop: ${logoSize.desktop};

  /* fonts */
  --font-base: ${fontBase};
  --font-heading: ${fontHeading || 'inherit'};
  --font-heading-weight: ${fontHeadingWeight || 'bold'};

  /* colors */
  --color-primary: ${color.primary};
  --color-black: #000;
  --color-light: #999;
  --color-lightest: #eee;

  --color-text-darkest: var(--color-black);
  --color-text-lightest: var(--color-light);

  /* radius */
  --radius: ${radius};

  /* spaces */
  --neg-space-xsm: -0.3125rem;
  --space-xsm: 0.3125rem;
  --space-sm: 0.625rem;
  --space-md: 0.9375rem;
  --space-lg: 1.25rem;
  --space-xlg: 1.875rem;

  /* font size */
  --text-sm: 0.875rem;
  --text-md: 0.9375rem;
  --text-lg: 1.125rem;
  --text-xlg: 1.5rem;
  --text-xxlg: 2.1875rem;

  /* button design tokens */
  --button-border: ${button.border || 'inherit'};
  --button-border-radius: ${button.borderRadius || 'inherit'};
  --button-padding: ${button.padding || '0 var(--space-md)'};
  --button-width: ${button.width || 'auto'};
  --button-height: ${button.height || '52px'};
  --button-background: ${button.background || 'var(--color-primary)'};
  --button-font-family: ${button.fontFamily || 'inherit'};
  --button-font-size: ${button.border || 'var(--text-md)'};
  --button-font-weight: ${button.fontWeight || 'bold'};
  --button-color: ${button.color || '#fff'};
  --button-text-transform: ${button.textTransform || 'inherit'};

  --button-desktop-border-radius: ${buttonDesktop.borderRadius || button.borderRadius || 'inherit'};
}
`

const fontsTemplate = (timestamp) => {
	return ({ fonts }) => {
		return fonts
			.map(
				({ name, file, property: { weight, style } }) => `
    @font-face {
      font-family: '${name}';
      src: url('/slideshow/fonts/${file}.eot?${timestamp}');
      src:
        url('/slideshow/fonts/${file}.woff2?${timestamp}') format('woff2'),
        url('/slideshow/fonts/${file}.woff?${timestamp}') format('woff'),
        url('/slideshow/fonts/${file}.eot#iefix') format('embedded-opentype'),
        url('/slideshow/fonts/${file}.svg#${file}') format('svg');
      font-weight: ${weight};
      font-style: ${style};
      font-display: swap;
    }
    `
			)
			.join('\n')
	}
}

const indexTemplate = () => `
@import './variables';
@import './fonts';
`

module.exports = {
	variableTemplate,
	fontsTemplate,
	indexTemplate
}
