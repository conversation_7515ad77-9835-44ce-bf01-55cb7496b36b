body {
	padding: 0 var(--space-xsm);
}

footer {
	margin: var(--space-xlg) 0;
}

.ad-placeholder.isMobile {
	min-height: 250px;
	margin-bottom: 15px;
}

.ad-placeholder {
	position: relative;
	background-color: var(--color-lightest);
	text-align: center;

	> svg {
		display: block;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translateX(-50%) translateY(-50%);
	}

	&.isMobile > svg + div {
		position: relative;
	}

	&:not(&.isMobile) > svg {
		display: none;
	}
}

.site_content .container {
	background-color: #fff;
}

.aside {
	display: flex;
	justify-content: center;
	margin-bottom: var(--space-md);

	& .ad-placeholder {
		min-height: 300px;
	}
}

.aside > .title {
	margin-bottom: var(--space-md);
}

@media screen and (min-width: 540px) {
	.ad-placeholder {
		&.isMobile {
			display: none;
		}

		&:not(&.isMobile) > svg {
			display: block;
		}
	}

	.site_content {
		max-width: var(--site-max-width);
		margin: 0 auto;
	}

	main > section {
		/* max-width: 730px; */
	}
}

@media screen and (min-width: 769px) {
	[data-ads-core*='Banniere-Haute'] {
		padding-top: 30px;
	}

	.site_skinrtb,
	.site_skindirect {
		max-width: var(--site-habillage-size);

		.coreads_banniere-haute {
			display: none;
		}
	}

	.site_skinvideo {
		max-width: var(--site-habillage-size);

		.coreads_banniere-haute.ad-placeholder {
			margin-top: 0;
		}
	}

	.aside {
		display: block;
	}

	.ad-placeholder {
		.aside &.isLoaded,
		&.isLoaded {
			background: none;
			min-height: 0;
		}

		&:not(.isMobile).isLoaded > svg {
			display: none;
		}
	}

	.coreads_banniere-haute.ad-placeholder {
		margin-top: var(--space-lg);
		min-height: 200px;
	}

	.ad-placeholder.pave-haut2-desktop {
		display: flex;
		min-height: 375px;
		margin-bottom: var(--space-xlg);

		> div {
			margin: auto;
			z-index: 1;
		}

		&.isLoaded svg {
			display: flex;
		}

		&.isLoaded {
			background: var(--color-lightest);
			min-height: 375px;
		}
	}

	.aside .ad-placeholder {
		position: sticky;
		top: var(--space-xlg);
		min-height: 500px;
	}

	.container {
		display: grid;
		grid-template-columns: 1fr 300px;
		grid-gap: var(--space-xlg);
	}

	.header {
		grid-column: 1 / 3;
	}

	.aside > h2 {
		display: none;
	}

	.sponsored-title {
		margin-top: var(--space-xlg);
	}
}
