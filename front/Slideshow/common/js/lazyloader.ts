/* create a NPM module !! */

// eslint-disable-next-line
let instance: <PERSON>zyloader = null

export default class Lazyloader {
	#offset: number
	#observer: IntersectionObserver
	#callbacks: Map<Element, () => void>

	public static get(): Lazyloader {
		if (instance) {
			return instance
		}

		instance = new Lazyloader()
		return instance
	}

	constructor(offset: number = null) {
		this.#offset = offset || window.innerHeight * 2
		const onIntersecting = (entries) => this.onIntersecting(entries)
		const options = {
			root: null,
			rootMargin: `0px 0px 100% 0px`,
			thresholds: [0]
		}

		this.#observer = new IntersectionObserver(onIntersecting, options)
		this.#callbacks = new Map()
	}

	public observe(element: HTMLElement, callback: () => void): void {
		if (this.isReadyToLoad(element)) {
			if (callback && typeof callback === 'function') {
				callback()
			}
			return
		}

		this.#callbacks.set(element, callback)
		this.#observer.observe(element)
	}

	public unobserve(element: Element): void {
		this.#callbacks.delete(element)
		this.#observer.unobserve(element)
	}

	private load(element: Element): void {
		const callback: () => void = this.#callbacks.get(element)
		if (callback && typeof callback === 'function') {
			callback(element)
		}
	}

	private onIntersecting(entries: Array<IntersectionObserverEntry>): void {
		entries.forEach((entry) => {
			if (entry.isIntersecting) {
				const target = entry.target

				if (target.hasAttribute('data-picture-index')) {
					if (target.getAttribute('data-picture-index') % 3 === 2) {
						target.classList.add('ads-core-inimage')
					}
				} else {
					this.load(target)
				}
				this.unobserve(target)
			}
		})
	}

	private isReadyToLoad(element: HTMLElement): boolean {
		return element.getBoundingClientRect().top - this.#offset < 0
	}
}
