# Ticket Flow

## Préparation

https://pmdtech.atlassian.net/jira/software/c/projects/FAC/boards/436

### Je crée un ticket

Je le met dans la colonne `à présenter` afin d'expliquer son contenu lors de la réunion de présentation.
Une fois présenté, je déplace le ticket dans la colonne `Concevoir`.

### Je conçois le ticket

Je fais cette étape en équipe.
Je renseigne le TODO par ce qu'il faudra faire techniquement.
J'estime le ticket selon la suite de Fibonacci.
Je déplace le ticket en à faire.

## Sprint 

https://pmdtech.atlassian.net/jira/software/c/projects/FAC/boards/89

### Je "block" un ticket

Je rajoute un commentaire en expliquant pourquoi le ticket est blocké.
Je rajoute le label `block`.

### Je "déblock" un ticket

Je rajoute un commentaire en expliquant comment j'ai débloqué le ticket.
Je supprime le label "block".
J'en informe la personne qui l'avait mis en `block`.

### Je travail sur un ticket

je change son statut en `dev en cours`.
Cela permet d'éviter de travailler à 2 sur un même ticket.

### Je finis un ticket

Je change son statut en `dev terminé`.
J'indique brièvement ce que j'ai fait.
Je rajoute les actions post MEP. 
Cela permet de visualiser quels sont les tickets à intégrer dans une prochaine release/hotfix.

### Je déploie sur un env feature branche

Je test que cela fonctionne.
Je passe le ticket en `A tester`.
J'en informe le créateur du ticket afin qu'il puisse effectuer ces tests.
Je demande à un développeur de faire la review de la MR.

### Je livre une version en prod

Je test que cela fonctionne.
Je vais sur [Datadog](https://app.datadoghq.eu/apm/services/fac-website/operations/symfony.request/resources) pour voir s'il n'y a pas d'erreur ou de problème de latence.
